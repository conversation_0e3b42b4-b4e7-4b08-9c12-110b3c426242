/**
 * <PERSON><PERSON> sách các ví dụ về cách sử dụng SqlHelper
 * 
 * Các ví dụ này minh họa cách sử dụng SqlHelper trong các tình huống khác nhau.
 */

/**
 * 1. repository-example.ts
 * 
 * Ví dụ về cách sử dụng SqlHelper với Repository.
 * - L<PERSON>y danh sách với phân trang
 * - L<PERSON>y thông tin theo ID
 * - Tạo mới
 * - Cập nhật
 * - Xóa
 * - <PERSON>ếm
 * - Tạo nhiều bản ghi cùng lúc
 */
// export * from './repository-example';

// /**
//  * 2. transaction-example.ts
//  * 
//  * Ví dụ về cách sử dụng SqlHelper với Transaction.
//  * - Thực hiện nhiều thao tác trong một transaction
//  * - Rollback khi có lỗi
//  */
// export * from './transaction-example';

// /**
//  * 3. dto-example.ts
//  * 
//  * <PERSON><PERSON> dụ về cách sử dụng SqlHelper với DTO.
//  * - Chuyển đổi kết quả từ Entity sang DTO
//  * - Sử dụng class-transformer
//  */
// export * from './dto-example';

/**
 * 4. raw-sql-example.ts
 * 
 * Ví dụ về cách sử dụng SqlHelper với Raw SQL.
 * - Thực thi câu lệnh SQL tùy chỉnh
 * - Xử lý kết quả
 */
// export * from './raw-sql-example';

/**
 * 5. naming-strategy-example.ts
 * 
 * Ví dụ về cách giải quyết vấn đề camelCase vs snake_case.
 * - Cấu hình TypeORM để tự động chuyển đổi
 * - Chỉ định tên cột trong entity
 * - Sửa đổi cách sử dụng SqlHelper.getPaginatedData
 */
// export * from './naming-strategy-example';
