import { Column, Entity, PrimaryGeneratedColumn, Unique } from 'typeorm';
import { MethodEnum } from '../constants/method.enum';
import { ToolStatusEnum } from '../constants/tool-status.enum';

/**
 * Entity đại diện cho bảng user_tool_versions trong cơ sở dữ liệu
 * Bảng lưu trữ các phiên bản chỉnh sửa của người dùng, chỉ tạo khi người dùng chỉnh sửa tool công khai
 */
@Entity('user_tool_versions')
@Unique('user_tool_versions_pk', ['userId', 'originalToolId', 'originalVersionId'])
export class UserToolVersion {
  /**
   * ID duy nhất của phiên bản, sử dụng UUID
   */
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  /**
   * ID của người dùng chỉnh sửa, tham chiếu đến bảng users
   */
  @Column({ name: 'user_id', type: 'integer' })
  userId: number;

  /**
   * ID của tool gốc, tham chiếu đến bảng user_tools
   * Trong database là original_function_id
   */
  @Column({ name: 'original_tool_id', type: 'uuid' })
  originalToolId: string;

  /**
   * ID của phiên bản gốc từ admin tool version
   */
  @Column({ name: 'original_version_id', type: 'uuid', nullable: true })
  originalVersionId: string;

  /**
   * Tên định danh cho phiên bản, dễ nhớ hơn số (ví dụ: "v1.0", "v2.0-beta")
   */
  @Column({ name: 'version_name', type: 'varchar', length: 50, nullable: true })
  versionName: string | null;

  /**
   * Mô tả thay đổi so với phiên bản gốc hoặc phiên bản trước
   */
  @Column({ name: 'change_description', type: 'text', nullable: true })
  changeDescription: string | null;

  /**
   * Thời điểm tạo phiên bản, tính bằng millisecond
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật cuối, tính bằng millisecond
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;

  /**
   * Trạng thái của phiên bản: DRAFT, APPROVED, DEPRECATED
   */
  @Column({
    name: 'status',
    type: 'enum',
    enum: ToolStatusEnum,
    default: ToolStatusEnum.APPROVED
  })
  status: ToolStatusEnum;

  /**
   * Tên của hàm trong định nghĩa code
   */
  @Column({ name: 'tool_name', length: 64 })
  toolName: string;

  /**
   * Mô tả chi tiết về chức năng của hàm
   */
  @Column({ name: 'tool_description', type: 'text', nullable: true })
  toolDescription: string | null;

  /**
   * Tham số của hàm, định dạng JSONB
   */
  @Column({ name: 'parameters', type: 'jsonb', default: '{}' })
  parameters: any;

  /**
   * Trạng thái sửa
   */
  @Column({ name: 'edited', default: false })
  edited: boolean;

  /**
   * Endpoint của tool
   */
  @Column({ name: 'endpoint', nullable: true, type: 'varchar', length: 255 })
  endpoint: string | null;

  /**
   * Phương thức HTTP
   */
  @Column({ name: 'method', type: 'enum', enum: MethodEnum, nullable: true })
  method: MethodEnum;
}
