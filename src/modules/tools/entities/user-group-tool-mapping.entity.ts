import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyTo<PERSON>ne, PrimaryColumn } from 'typeorm';
import { UserGroupTool } from './user-group-tool.entity';
import { UserTool } from './user-tool.entity';

/**
 * Entity đại diện cho bảng user_group_tool_mappings trong cơ sở dữ liệu
 * Bảng liên kết ánh xạ công cụ người dùng với nhóm công cụ người dùng
 */
@Entity('user_group_tool_mappings')
export class UserGroupToolMapping {
  /**
   * ID của nhóm công cụ, tham chiếu đến user_group_tools
   */
  @PrimaryColumn({ name: 'group_id' })
  groupId: number;

  /**
   * ID của công cụ, tham chiếu đến user_tools
   */
  @PrimaryColumn({ name: 'tool_id', type: 'uuid' })
  toolId: string;
}
