import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin nhân viên
 */
export class EmployeeInfoDto {
  @ApiProperty({
    description: 'ID của nhân viên',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: '<PERSON>ên của nhân viên',
    example: '<PERSON>uyễn Văn <PERSON>',
  })
  name: string;

  @ApiProperty({
    description: 'Email của nhân viên',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Avatar của nhân viên',
    example: 'https://example.com/avatar.jpg',
    nullable: true,
  })
  avatar: string | null;
}
