import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ErrorCode, AppException } from '@common/exceptions';
import { TOOLS_ERROR_CODES } from '../../exceptions';
import { AdminToolService } from '../services';
import {
  CreateToolDto,
  QueryToolDto,
  ToolDetailDto,
  ToolListItemDto,
  UpdateToolDto,
} from '../dto';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { CurrentEmployee } from '@modules/auth/decorators';
import { ApiResponseDto } from '@/common/response';
import { PaginatedResult } from '@common/response/api-response-dto';
import { SWAGGER_API_TAGS } from '@common/swagger';

@ApiTags(SWAGGER_API_TAGS.ADMIN_TOOL)
@Controller('admin/tools')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class AdminToolController {
  constructor(private readonly adminToolService: AdminToolService) {}

  @Post()
  @ApiOperation({ summary: 'Tạo mới tool' })
  @ApiResponse({
    status: 201,
    description: 'Tool đã được tạo thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_ALREADY_EXISTS,
    TOOLS_ERROR_CODES.TOOL_NAME_INVALID,
    TOOLS_ERROR_CODES.TOOL_PARAMETERS_INVALID
  )
  async createTool(
    @CurrentEmployee('id') employeeId: number,
    @Body() createDto: CreateToolDto,
  ) {
    const toolId = await this.adminToolService.createTool(employeeId, createDto);
    return ApiResponseDto.success({ id: toolId });
  }

  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tool' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tool',
    type: ApiResponseDto<PaginatedResult<ToolListItemDto>>,
  })
  @ApiErrorResponse(ErrorCode.INTERNAL_SERVER_ERROR)
  async getTools(@Query() queryDto: QueryToolDto) {
    const result = await this.adminToolService.getTools(queryDto);
    return ApiResponseDto.paginated(result);
  }

  @Get('trash')
  @ApiOperation({ summary: 'Lấy danh sách tools đã bị xóa mềm' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tools đã bị xóa',
    type: ApiResponseDto<PaginatedResult<ToolListItemDto>>,
  })
  @ApiErrorResponse(ErrorCode.INTERNAL_SERVER_ERROR)
  async getDeletedTools(@Query() queryDto: QueryToolDto) {
    const result = await this.adminToolService.getDeletedTools(queryDto);
    return ApiResponseDto.paginated(result);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết tool' })
  @ApiParam({ name: 'id', description: 'ID của tool' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết tool',
    type: () => ApiResponseDto.success(ToolDetailDto),
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND
  )
  async getToolById(@Param('id') id: string) {

    const tool = await this.adminToolService.getToolById(id);
    return ApiResponseDto.success(tool);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật thông tin tool' })
  @ApiParam({ name: 'id', description: 'ID của tool' })
  @ApiResponse({
    status: 200,
    description: 'Tool đã được cập nhật thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_NAME_INVALID,
    TOOLS_ERROR_CODES.TOOL_PARAMETERS_INVALID
  )
  async updateTool(
    @Param('id') id: string,
    @CurrentEmployee('id') employeeId: number,
    @Body() updateDto: UpdateToolDto,
  ) {
    // Kiểm tra nếu id là 'groups', trả về lỗi rõ ràng
    if (id === 'groups') {
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
        'Để truy cập nhóm tool, vui lòng sử dụng /v1/admin/tools/groups'
      );
    }

    const toolId = await this.adminToolService.updateTool(id, employeeId, updateDto);
    return ApiResponseDto.success({ id: toolId });
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Xóa mềm tool' })
  @ApiParam({ name: 'id', description: 'ID của tool' })
  @ApiResponse({
    status: 200,
    description: 'Tool đã được xóa mềm thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND
  )
  async deleteTool(
    @Param('id') id: string,
    @CurrentEmployee('id') employeeId: number,
  ) {
    // Kiểm tra nếu id là 'groups', trả về lỗi rõ ràng
    if (id === 'groups') {
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
        'Để truy cập nhóm tool, vui lòng sử dụng /v1/admin/tools/groups'
      );
    }

    await this.adminToolService.deleteTool(id, employeeId);
    return ApiResponseDto.success({ message: 'Tool đã được xóa mềm thành công' });
  }



  @Post(':id/rollback')
  @ApiOperation({ summary: 'Khôi phục tool đã bị xóa mềm' })
  @ApiParam({ name: 'id', description: 'ID của tool cần khôi phục' })
  @ApiResponse({
    status: 200,
    description: 'Khôi phục tool thành công',
    type: ApiResponseDto<{ success: boolean }>,
  })
  @ApiErrorResponse(TOOLS_ERROR_CODES.TOOL_NOT_FOUND)
  @ApiErrorResponse(TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED)
  @ApiErrorResponse(ErrorCode.INTERNAL_SERVER_ERROR)
  async rollbackTool(@Param('id') id: string) {
    const success = await this.adminToolService.rollbackTool(id);
    return ApiResponseDto.success({ success });
  }
}
