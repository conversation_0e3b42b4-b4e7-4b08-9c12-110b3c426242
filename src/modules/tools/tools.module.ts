import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ToolsAdminModule } from './admin/tools-admin.module';
import { ToolsUserModule } from './user/tools-user.module';
import {
  AdminTool,
  UserTool,
  AdminToolVersion,
  UserToolVersion,
  AdminGroupTool,
  UserGroupTool,
  AdminGroupToolMapping,
  AdminGroupToolsTypeAgent,
  UserGroupToolMapping,
  UserGroupToolsTypeAgent,
  ApiKey,
  OAuth,
  UserToolsCustom,
  UserGroupToolCustomMapping
} from './entities';
import { TypeAgent } from '@modules/agent/entities';
import {
  AdminToolRepository,
  UserToolRepository,
  AdminToolVersionRepository,
  UserToolVersionRepository,
  AdminGroupToolRepository,
  UserGroupToolRepository,
  AdminGroupToolMappingRepository,
  AdminGroupToolsTypeAgentRepository,
  UserGroupToolMappingRepository,
  UserGroupToolsTypeAgentRepository,
  TypeAgentRepository,
  ApiKeyRepository,
  OAuthRepository,
  UserToolsCustomRepository,
  UserGroupToolCustomMappingRepository
} from './repositories';
import { ToolValidationHelper } from './helpers/tool-validation.helper';
import { ToolFunctionValidationHelper } from './helpers/tool-function-validation.helper';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AdminTool,
      UserTool,
      AdminToolVersion,
      UserToolVersion,
      AdminGroupTool,
      UserGroupTool,
      AdminGroupToolMapping,
      AdminGroupToolsTypeAgent,
      UserGroupToolMapping,
      UserGroupToolsTypeAgent,
      TypeAgent,
      ApiKey,
      OAuth,
      UserToolsCustom,
      UserGroupToolCustomMapping
    ]),
    ToolsAdminModule,
    ToolsUserModule
  ],
  providers: [
    AdminToolRepository,
    UserToolRepository,
    AdminToolVersionRepository,
    UserToolVersionRepository,
    AdminGroupToolRepository,
    UserGroupToolRepository,
    AdminGroupToolMappingRepository,
    AdminGroupToolsTypeAgentRepository,
    UserGroupToolMappingRepository,
    UserGroupToolsTypeAgentRepository,
    TypeAgentRepository,
    ApiKeyRepository,
    OAuthRepository,
    UserToolsCustomRepository,
    UserGroupToolCustomMappingRepository,
    ToolValidationHelper,
    ToolFunctionValidationHelper
  ],
  exports: [
    ToolsAdminModule,
    ToolsUserModule,
    UserToolRepository,
    AdminGroupToolRepository,
    ApiKeyRepository,
    OAuthRepository,
    UserToolsCustomRepository,
    UserGroupToolCustomMappingRepository,
    ToolValidationHelper,
    ToolFunctionValidationHelper
  ],
})
export class ToolsModule {}
