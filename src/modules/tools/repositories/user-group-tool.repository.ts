import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserGroupTool } from '../entities/user-group-tool.entity';
import { PaginatedResult } from '@common/response/api-response-dto';

@Injectable()
export class UserGroupToolRepository extends Repository<UserGroupTool> {
  private readonly logger = new Logger(UserGroupToolRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserGroupTool, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho user group tool
   * @returns SelectQueryBuilder<UserGroupTool> để sử dụng trong các phương thức khác
   */
  private createBaseQuery(): SelectQueryBuilder<UserGroupTool> {
    return this.createQueryBuilder('group');
  }

  /**
   * Tìm nhóm tool theo ID
   * @param id ID của nhóm tool cần tìm
   * @param userId ID của người dùng (tù<PERSON> chọn, nếu cần kiểm tra quyền sở hữu)
   * @returns Nhóm tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findGroupById(id: number, userId?: number): Promise<UserGroupTool | null> {
    const qb = this.createBaseQuery()
      .where('group.id = :id', { id });

    if (userId) {
      qb.andWhere('group.userId = :userId', { userId });
    }

    return qb.getOne();
  }

  /**
   * Tìm nhóm tool theo tên và ID người dùng
   * @param name Tên của nhóm tool cần tìm
   * @param userId ID của người dùng
   * @returns Nhóm tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findGroupByName(name: string, userId: number): Promise<UserGroupTool | null> {
    return this.createBaseQuery()
      .where('group.name = :name', { name })
      .andWhere('group.userId = :userId', { userId })
      .getOne();
  }

  /**
   * Lấy danh sách nhóm tool với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param userId ID của người dùng
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách nhóm tool với phân trang
   */
  async findGroups(
    page: number,
    limit: number,
    userId: number,
    search?: string,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<UserGroupTool>> {
    const qb = this.createBaseQuery()
      .where('group.userId = :userId', { userId });

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('(group.name ILIKE :search OR group.description ILIKE :search)',
        { search: `%${search}%` });
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`group.${sortBy}`, sortDirection);

    const [items, total] = await qb.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page
      }
    };
  }
}
