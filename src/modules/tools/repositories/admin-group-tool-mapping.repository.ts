import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { AdminGroupToolMapping } from '../entities/admin-group-tool-mapping.entity';

@Injectable()
export class AdminGroupToolMappingRepository extends Repository<AdminGroupToolMapping> {
  private readonly logger = new Logger(AdminGroupToolMappingRepository.name);

  constructor(private dataSource: DataSource) {
    super(AdminGroupToolMapping, dataSource.createEntityManager());
  }

  /**
   * Tìm tất cả các mapping theo group ID
   * @param groupId ID của nhóm công cụ
   * @returns Danh sách các mapping
   */
  async findByGroupId(groupId: number): Promise<AdminGroupToolMapping[]> {
    return this.find({
      where: { groupId },
      relations: ['tool']
    });
  }

  /**
   * Tì<PERSON> tất cả các mapping theo tool ID
   * @param toolId ID của công cụ
   * @returns Danh sách các mapping
   */
  async findByToolId(toolId: string): Promise<AdminGroupToolMapping[]> {
    return this.find({
      where: { toolId },
      relations: ['group']
    });
  }

  /**
   * Tìm mapping theo group ID và tool ID
   * @param groupId ID của nhóm công cụ
   * @param toolId ID của công cụ
   * @returns Mapping nếu tìm thấy, null nếu không tìm thấy
   */
  async findByGroupIdAndToolId(groupId: number, toolId: string): Promise<AdminGroupToolMapping | null> {
    return this.findOne({
      where: { groupId, toolId }
    });
  }

  /**
   * Xóa tất cả các mapping theo group ID
   * @param groupId ID của nhóm công cụ
   */
  async deleteByGroupId(groupId: number): Promise<void> {
    await this.delete({ groupId });
  }

  /**
   * Xóa tất cả các mapping theo tool ID
   * @param toolId ID của công cụ
   */
  async deleteByToolId(toolId: string): Promise<void> {
    await this.delete({ toolId });
  }
}
