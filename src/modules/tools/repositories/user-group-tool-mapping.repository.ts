import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { UserGroupToolMapping } from '../entities/user-group-tool-mapping.entity';

@Injectable()
export class UserGroupToolMappingRepository extends Repository<UserGroupToolMapping> {
  private readonly logger = new Logger(UserGroupToolMappingRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserGroupToolMapping, dataSource.createEntityManager());
  }

  /**
   * Tìm tất cả các mapping theo group ID
   * @param groupId ID của nhóm công cụ
   * @returns Danh sách các mapping
   */
  async findByGroupId(groupId: number): Promise<UserGroupToolMapping[]> {
    return this.find({
      where: { groupId },
      relations: ['tool']
    });
  }

  /**
   * Tì<PERSON> tất cả các mapping theo tool ID
   * @param toolId ID của công cụ
   * @returns Danh sách các mapping
   */
  async findByToolId(toolId: string): Promise<UserGroupToolMapping[]> {
    return this.find({
      where: { toolId },
      relations: ['group']
    });
  }

  /**
   * Tìm mapping theo group ID và tool ID
   * @param groupId ID của nhóm công cụ
   * @param toolId ID của công cụ
   * @returns Mapping nếu tìm thấy, null nếu không tìm thấy
   */
  async findByGroupIdAndToolId(groupId: number, toolId: string): Promise<UserGroupToolMapping | null> {
    return this.findOne({
      where: { groupId, toolId }
    });
  }

  /**
   * Xóa tất cả các mapping theo group ID
   * @param groupId ID của nhóm công cụ
   */
  async deleteByGroupId(groupId: number): Promise<void> {
    await this.delete({ groupId });
  }

  /**
   * Lấy thông tin group theo danh sách tool IDs
   * @param toolIds Danh sách ID của các tool
   * @returns Danh sách thông tin group mapping
   */
  async findGroupsByToolIds(toolIds: string[]): Promise<Array<{ toolId: string; groupId: number; groupName: string }>> {
    if (!toolIds || toolIds.length === 0) {
      return [];
    }

    return this.createQueryBuilder('mapping')
      .select([
        'mapping.toolId as toolId',
        'group.id as groupId',
        'group.name as groupName'
      ])
      .leftJoin('user_group_tools', 'group', 'mapping.groupId = group.id')
      .where('mapping.toolId IN (:...toolIds)', { toolIds })
      .getRawMany();
  }

  /**
   * Lấy thông tin group theo tool ID
   * @param toolId ID của tool
   * @returns Danh sách thông tin group
   */
  async findGroupsByToolId(toolId: string): Promise<Array<{ groupId: number; groupName: string }>> {
    return this.createQueryBuilder('mapping')
      .select([
        'group.id as groupId',
        'group.name as groupName'
      ])
      .leftJoin('user_group_tools', 'group', 'mapping.groupId = group.id')
      .where('mapping.toolId = :toolId', { toolId })
      .getRawMany();
  }

  /**
   * Xóa tất cả các mapping theo tool ID
   * @param toolId ID của công cụ
   */
  async deleteByToolId(toolId: string): Promise<void> {
    await this.delete({ toolId });
  }
}
