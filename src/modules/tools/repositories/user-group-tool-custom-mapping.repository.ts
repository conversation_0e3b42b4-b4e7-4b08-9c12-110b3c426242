import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { UserGroupToolCustomMapping } from '../entities/user-group-tool-custom-mapping.entity';
import { AppException } from '@common/exceptions';
import { CUSTOM_TOOLS_ERROR_CODES } from '../exceptions';

@Injectable()
export class UserGroupToolCustomMappingRepository extends Repository<UserGroupToolCustomMapping> {
  private readonly logger = new Logger(UserGroupToolCustomMappingRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserGroupToolCustomMapping, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho ánh xạ nhóm công cụ tùy chỉnh
   * @returns SelectQueryBuilder<UserGroupToolCustomMapping> để sử dụng trong các phương thức khác
   */
  private createBaseQuery(): SelectQueryBuilder<UserGroupToolCustomMapping> {
    return this.createQueryBuilder('mapping');
  }

  /**
   * Tìm ánh xạ theo ID nhóm và ID công cụ
   * @param groupId ID của nhóm
   * @param toolId ID của công cụ
   * @returns Ánh xạ nếu tìm thấy, null nếu không tìm thấy
   */
  async findByGroupAndToolId(groupId: number, toolId: string): Promise<UserGroupToolCustomMapping | null> {
    try {
      return this.createBaseQuery()
        .where('mapping.groupId = :groupId', { groupId })
        .andWhere('mapping.toolId = :toolId', { toolId })
        .getOne();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm ánh xạ nhóm công cụ: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.GROUP_TOOL_MAPPING_NOT_FOUND, 'Không tìm thấy ánh xạ nhóm công cụ');
    }
  }

  /**
   * Tìm tất cả ánh xạ theo ID nhóm
   * @param groupId ID của nhóm
   * @returns Danh sách ánh xạ
   */
  async findByGroupId(groupId: number): Promise<UserGroupToolCustomMapping[]> {
    try {
      return this.createBaseQuery()
        .leftJoinAndSelect('mapping.tool', 'tool')
        .where('mapping.groupId = :groupId', { groupId })
        .getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm ánh xạ nhóm công cụ theo ID nhóm: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.GROUP_TOOL_MAPPING_NOT_FOUND, 'Không tìm thấy ánh xạ nhóm công cụ');
    }
  }

  /**
   * Tìm tất cả ánh xạ theo ID công cụ
   * @param toolId ID của công cụ
   * @returns Danh sách ánh xạ
   */
  async findByToolId(toolId: string): Promise<UserGroupToolCustomMapping[]> {
    try {
      return this.createBaseQuery()
        .leftJoinAndSelect('mapping.group', 'group')
        .where('mapping.toolId = :toolId', { toolId })
        .getMany();
    } catch (error) {
      this.logger.error(`Lỗi khi tìm ánh xạ nhóm công cụ theo ID công cụ: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.GROUP_TOOL_MAPPING_NOT_FOUND, 'Không tìm thấy ánh xạ nhóm công cụ');
    }
  }

  /**
   * Tạo mới ánh xạ nhóm công cụ
   * @param mapping Dữ liệu ánh xạ cần tạo
   * @returns Ánh xạ đã tạo
   */
  @Transactional()
  async createMapping(mapping: Partial<UserGroupToolCustomMapping>): Promise<UserGroupToolCustomMapping> {
    try {
      const newMapping = this.create(mapping);
      return await this.save(newMapping);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo ánh xạ nhóm công cụ: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.GROUP_TOOL_MAPPING_CREATE_FAILED, 'Tạo ánh xạ nhóm công cụ thất bại');
    }
  }

  /**
   * Xóa ánh xạ nhóm công cụ
   * @param groupId ID của nhóm
   * @param toolId ID của công cụ
   * @returns Void
   */
  @Transactional()
  async deleteMapping(groupId: number, toolId: string): Promise<void> {
    try {
      const result = await this.createQueryBuilder()
        .delete()
        .from(UserGroupToolCustomMapping)
        .where('groupId = :groupId', { groupId })
        .andWhere('toolId = :toolId', { toolId })
        .execute();

      if (result.affected === 0) {
        throw new AppException(CUSTOM_TOOLS_ERROR_CODES.GROUP_TOOL_MAPPING_NOT_FOUND, 'Không tìm thấy ánh xạ nhóm công cụ');
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa ánh xạ nhóm công cụ: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.GROUP_TOOL_MAPPING_DELETE_FAILED, 'Xóa ánh xạ nhóm công cụ thất bại');
    }
  }

  /**
   * Xóa tất cả các ánh xạ theo ID nhóm
   * @param groupId ID của nhóm công cụ
   * @returns Void
   */
  @Transactional()
  async deleteByGroupId(groupId: number): Promise<void> {
    try {
      const result = await this.createQueryBuilder()
        .delete()
        .from(UserGroupToolCustomMapping)
        .where('groupId = :groupId', { groupId })
        .execute();

      this.logger.log(`Đã xóa ${result.affected} ánh xạ nhóm công cụ tùy chỉnh với groupId ${groupId}`);
    } catch (error) {
      this.logger.error(`Lỗi khi xóa ánh xạ nhóm công cụ tùy chỉnh theo groupId: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.GROUP_TOOL_MAPPING_DELETE_FAILED, 'Xóa ánh xạ nhóm công cụ tùy chỉnh thất bại');
    }
  }
}
