import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';

/**
 * Kiểm tra xem một đối tượng có phải là đặc tả OpenAPI hợp lệ không
 * @param validationOptions T<PERSON><PERSON> chọn validation
 * @returns Decorator function
 */
export function IsOpenApiSpec(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isOpenApiSpec',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, args: ValidationArguments) {
          // Kiểm tra xem value có phải là object không
          if (typeof value !== 'object' || value === null) {
            return false;
          }
          
          // Kiểm tra các thuộc tính cần thiết của OpenAPI
          if (!value.openapi || !value.paths) {
            return false;
          }
          
          // Kiểm tra phiên bản OpenAPI
          if (typeof value.openapi !== 'string' || !value.openapi.match(/^3\.\d+\.\d+$/)) {
            return false;
          }
          
          // Kiểm tra paths
          if (typeof value.paths !== 'object' || value.paths === null) {
            return false;
          }
          
          // Kiểm tra ít nhất một path
          const pathKeys = Object.keys(value.paths);
          if (pathKeys.length === 0) {
            return false;
          }
          
          // Kiểm tra ít nhất một path có phương thức HTTP
          let hasHttpMethod = false;
          for (const path of pathKeys) {
            const pathObj = value.paths[path];
            if (typeof pathObj !== 'object' || pathObj === null) {
              continue;
            }
            
            const methods = ['get', 'post', 'put', 'delete', 'patch', 'options', 'head'];
            for (const method of methods) {
              if (method in pathObj) {
                hasHttpMethod = true;
                break;
              }
            }
            
            if (hasHttpMethod) {
              break;
            }
          }
          
          return hasHttpMethod;
        },
        defaultMessage(args: ValidationArguments) {
          return `${args.property} phải là đặc tả OpenAPI hợp lệ với phiên bản 3.x.x và ít nhất một path với một phương thức HTTP`;
        },
      },
    });
  };
}
