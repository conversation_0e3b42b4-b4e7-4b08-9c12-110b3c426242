import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  AdminTool,
  UserTool,
  AdminToolVersion,
  UserToolVersion,
  UserGroupTool,
  UserGroupToolMapping,
  UserGroupToolsTypeAgent,
  ApiKey,
  OAuth,
  UserToolsCustom,
  UserGroupToolCustomMapping
} from '../entities';
import { TypeAgent } from '../../agent/entities/type-agent.entity';
import {
  AdminToolRepository,
  UserToolRepository,
  AdminToolVersionRepository,
  UserToolVersionRepository,
  UserGroupToolRepository,
  UserGroupToolMappingRepository,
  UserGroupToolsTypeAgentRepository,
  TypeAgentRepository,
  ApiKeyRepository,
  OAuthRepository,
  UserToolsCustomRepository,
  UserGroupToolCustomMappingRepository
} from '../repositories';
import { UserToolService, UserToolVersionService, UserGroupToolService, IntegrationToolsService, UserToolsCustomService } from './services';
import { UserToolController, UserToolVersionController, UserGroupToolController, IntegrationToolsController } from './controllers';

@Module({
  imports: [TypeOrmModule.forFeature([
    UserTool,
    UserToolVersion,
    AdminTool,
    AdminToolVersion,
    UserGroupTool,
    UserGroupToolMapping,
    UserGroupToolsTypeAgent,
    TypeAgent,
    ApiKey,
    OAuth,
    UserToolsCustom,
    UserGroupToolCustomMapping
  ])],
  controllers: [
    UserToolController,
    UserToolVersionController,
    UserGroupToolController,
    IntegrationToolsController
  ],
  providers: [
    UserToolService,
    UserToolVersionService,
    UserGroupToolService,
    IntegrationToolsService,
    UserToolsCustomService,
    UserToolRepository,
    UserToolVersionRepository,
    AdminToolRepository,
    AdminToolVersionRepository,
    UserGroupToolRepository,
    UserGroupToolMappingRepository,
    UserGroupToolsTypeAgentRepository,
    TypeAgentRepository,
    ApiKeyRepository,
    OAuthRepository,
    UserToolsCustomRepository,
    UserGroupToolCustomMappingRepository
  ],
  exports: [
    UserToolService,
    UserToolVersionService,
    UserGroupToolService,
    IntegrationToolsService,
    UserToolsCustomService,
    UserToolsCustomRepository,
    ApiKeyRepository,
    OAuthRepository
  ],
})
export class ToolsUserModule {}
