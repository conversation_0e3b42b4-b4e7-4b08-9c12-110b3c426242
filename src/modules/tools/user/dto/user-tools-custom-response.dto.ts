import { ApiProperty } from '@nestjs/swagger';
import { UserToolsCustom } from '../../entities';

/**
 * DTO cho việc hiển thị thông tin công cụ tùy chỉnh
 * Chỉ bao gồm các trường cần thiết, loại bỏ các trường không cần thiết
 */
export class UserToolsCustomResponseDto {
  /**
   * ID của công cụ tùy chỉnh
   */
  @ApiProperty({
    description: 'ID của công cụ tùy chỉnh',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  /**
   * Tên của công cụ tùy chỉnh
   */
  @ApiProperty({
    description: 'Tên của công cụ tùy chỉnh',
    example: 'Công cụ tìm kiếm sản phẩm',
  })
  toolName: string;

  /**
   * <PERSON><PERSON> tả của công cụ tùy chỉnh
   */
  @ApiProperty({
    description: '<PERSON>ô tả của công cụ tùy chỉnh',
    example: 'Công cụ này giúp tìm kiếm sản phẩm theo nhiều tiêu chí khác nhau',
    nullable: true,
  })
  toolDescription: string | null;

  /**
   * Thời gian tạo
   */
  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1625097600000,
  })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (timestamp)',
    example: 1625097600000,
  })
  updatedAt: number;

  /**
   * Trạng thái active
   */
  @ApiProperty({
    description: 'Trạng thái active của công cụ',
    example: true,
  })
  active: boolean;

  /**
   * Phương thức chuyển đổi từ entity sang DTO
   * @param entity Entity công cụ tùy chỉnh
   * @returns DTO công cụ tùy chỉnh
   */
  static fromEntity(entity: UserToolsCustom): UserToolsCustomResponseDto {
    const dto = new UserToolsCustomResponseDto();
    dto.id = entity.id;
    dto.toolName = entity.toolName;
    dto.toolDescription = entity.toolDescription;
    dto.createdAt = entity.createdAt;
    dto.updatedAt = entity.updatedAt;
    dto.active = entity.active;
    return dto;
  }

  /**
   * Phương thức chuyển đổi từ danh sách entity sang danh sách DTO
   * @param entities Danh sách entity công cụ tùy chỉnh
   * @returns Danh sách DTO công cụ tùy chỉnh
   */
  static fromEntities(entities: UserToolsCustom[]): UserToolsCustomResponseDto[] {
    return entities.map(entity => UserToolsCustomResponseDto.fromEntity(entity));
  }
}
