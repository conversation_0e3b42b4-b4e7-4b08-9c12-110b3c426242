import { QueryDto } from '@common/dto';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsBoolean, IsEnum, IsOptional, IsString } from 'class-validator';
import { ToolStatusEnum } from '../../constants/tool-status.enum';

/**
 * Enum cho các trường sắp xếp tool của người dùng
 */
export enum UserToolSortBy {
  NAME = 'name',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * DTO cho việc truy vấn danh sách tool của người dùng
 */
export class QueryUserToolDto extends QueryDto {
  @ApiProperty({
    description: 'Tìm kiếm theo tên tool',
    required: false,
    example: 'tìm kiếm',
  })
  @IsString()
  @IsOptional()
  declare search?: string;

  @ApiProperty({
    description: 'Lọ<PERSON> theo có bản cập nhật mới',
    required: false,
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  hasUpdate?: boolean;

  @ApiProperty({
    description: 'Sắp xếp theo trường',
    enum: UserToolSortBy,
    default: UserToolSortBy.CREATED_AT,
    required: false,
  })
  @IsEnum(UserToolSortBy)
  @IsOptional()
  sortBy: UserToolSortBy = UserToolSortBy.CREATED_AT;
}
