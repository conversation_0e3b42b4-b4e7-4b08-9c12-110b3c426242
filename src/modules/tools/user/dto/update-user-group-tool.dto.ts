import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * DTO cập nhật nhóm tool của người dùng
 */
export class UpdateUserGroupToolDto {
  /**
   * Tên của nhóm tool
   */
  @ApiProperty({ description: 'Tên của nhóm tool', required: false })
  @IsOptional()
  @IsString({ message: 'Tên nhóm tool phải là chuỗi' })
  @MaxLength(100, { message: 'Tên nhóm tool không được vượt quá 100 ký tự' })
  name?: string;

  /**
   * Mô tả của nhóm tool
   */
  @ApiProperty({ description: 'Mô tả của nhóm tool', required: false })
  @IsOptional()
  @IsString({ message: 'Mô tả nhóm tool phải là chuỗi' })
  description?: string;

  /**
   * ID của loại agent
   */
  @ApiProperty({ description: 'ID của loại agent', required: false })
  @IsOptional()
  @IsNumber({}, { message: 'ID loại agent phải là số' })
  typeAgentId?: number;
}
