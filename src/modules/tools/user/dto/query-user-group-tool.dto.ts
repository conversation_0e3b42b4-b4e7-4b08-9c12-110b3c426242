import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto } from '@common/dto';

/**
 * Enum cho các trường sắp xếp nhóm tool của người dùng
 */
export enum UserGroupToolSortBy {
  NAME = 'name',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * DTO cho việc truy vấn danh sách nhóm tool của người dùng
 */
export class QueryUserGroupToolDto extends QueryDto {
  @ApiProperty({
    description: 'Tìm kiếm theo tên nhóm tool',
    required: false,
    example: 'tìm kiếm',
  })
  @IsString()
  @IsOptional()
  declare search?: string;

  @ApiProperty({
    description: 'Sắp xếp theo trường',
    enum: UserGroupToolSortBy,
    default: UserGroupToolSortBy.CREATED_AT,
    required: false,
  })
  @IsEnum(UserGroupToolSortBy)
  @IsOptional()
  sortBy: UserGroupToolSortBy = UserGroupToolSortBy.CREATED_AT;
}
