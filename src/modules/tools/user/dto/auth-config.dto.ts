import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiKeyLocationEnum, TokenSourceEnum } from '../../constants';

/**
 * Enum định nghĩa các loại xác thực
 */
export enum AuthTypeEnum {
  /**
   * Không xác thực
   */
  NONE = 'none',
  
  /**
   * Xác thực bằng API Key
   */
  API_KEY = 'apiKey',
  
  /**
   * Xác thực bằng OAuth
   */
  OAUTH = 'oauth'
}

/**
 * DTO cơ sở cho cấu hình xác thực
 */
export class AuthConfigDto {
  /**
   * Loại xác thực
   */
  @ApiProperty({
    description: 'Loại xác thực',
    enum: AuthTypeEnum,
    example: AuthTypeEnum.API_KEY
  })
  @IsEnum(AuthTypeEnum)
  @IsNotEmpty()
  authType: AuthTypeEnum;
  
  /**
   * Tên scheme từ OpenAPI
   */
  @ApiProperty({
    description: 'Tên scheme từ OpenAPI',
    example: 'ApiKeyAuth',
    required: false
  })
  @IsString()
  @IsOptional()
  schemeName?: string;
}

/**
 * DTO cho xác thực API Key
 */
export class ApiKeyAuthDto extends AuthConfigDto {
  /**
   * Giá trị API Key
   */
  @ApiProperty({
    description: 'Giá trị API Key',
    example: 'api_key_123456'
  })
  @IsString()
  @IsNotEmpty()
  apiKey: string;
  
  /**
   * Vị trí gửi API Key
   */
  @ApiProperty({
    description: 'Vị trí gửi API Key',
    enum: ApiKeyLocationEnum,
    example: ApiKeyLocationEnum.HEADER
  })
  @IsEnum(ApiKeyLocationEnum)
  @IsNotEmpty()
  apiKeyLocation: ApiKeyLocationEnum;
  
  /**
   * Tên tham số API Key
   */
  @ApiProperty({
    description: 'Tên tham số API Key',
    example: 'X-API-KEY'
  })
  @IsString()
  @IsNotEmpty()
  paramName: string;
}

/**
 * DTO cho xác thực OAuth
 */
export class OAuthAuthDto extends AuthConfigDto {
  /**
   * Token OAuth
   */
  @ApiProperty({
    description: 'Token OAuth',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  })
  @IsString()
  @IsNotEmpty()
  token: string;
  
  /**
   * Nguồn token
   */
  @ApiProperty({
    description: 'Nguồn token',
    enum: TokenSourceEnum,
    example: TokenSourceEnum.JWT
  })
  @IsEnum(TokenSourceEnum)
  @IsNotEmpty()
  tokenSource: TokenSourceEnum;
  
  /**
   * Client ID (tùy chọn)
   */
  @ApiProperty({
    description: 'Client ID',
    example: 'client_123',
    required: false
  })
  @IsString()
  @IsOptional()
  clientId?: string;
  
  /**
   * Client Secret (tùy chọn)
   */
  @ApiProperty({
    description: 'Client Secret',
    example: 'secret_456',
    required: false
  })
  @IsString()
  @IsOptional()
  clientSecret?: string;
  
  /**
   * URL xác thực (tùy chọn)
   */
  @ApiProperty({
    description: 'URL xác thực',
    example: 'https://example.com/oauth/authorize',
    required: false
  })
  @IsString()
  @IsOptional()
  authUrl?: string;
  
  /**
   * URL lấy token (tùy chọn)
   */
  @ApiProperty({
    description: 'URL lấy token',
    example: 'https://example.com/oauth/token',
    required: false
  })
  @IsString()
  @IsOptional()
  tokenUrl?: string;
  
  /**
   * URL làm mới token (tùy chọn)
   */
  @ApiProperty({
    description: 'URL làm mới token',
    example: 'https://example.com/oauth/refresh',
    required: false
  })
  @IsString()
  @IsOptional()
  refreshUrl?: string;
  
  /**
   * Refresh token (tùy chọn)
   */
  @ApiProperty({
    description: 'Refresh token',
    example: 'refresh_token_789',
    required: false
  })
  @IsString()
  @IsOptional()
  refreshToken?: string;
  
  /**
   * Scopes (tùy chọn)
   */
  @ApiProperty({
    description: 'Scopes',
    example: 'read write',
    required: false
  })
  @IsString()
  @IsOptional()
  scopes?: string;
  
  /**
   * Loại flow (tùy chọn)
   */
  @ApiProperty({
    description: 'Loại flow',
    example: 'authorization_code',
    required: false
  })
  @IsString()
  @IsOptional()
  flowType?: string;
}

/**
 * DTO cho không xác thực
 */
export class NoAuthDto extends AuthConfigDto {
  constructor() {
    super();
    this.authType = AuthTypeEnum.NONE;
  }
}
