import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO chi tiết nhóm tool của người dùng
 */
export class UserGroupToolDetailDto {
  /**
   * ID của nhóm tool
   */
  @ApiProperty({ description: 'ID của nhóm tool' })
  id: number;

  /**
   * Tên của nhóm tool
   */
  @ApiProperty({ description: 'Tên của nhóm tool' })
  name: string;

  /**
   * Mô tả của nhóm tool
   */
  @ApiProperty({ description: 'Mô tả của nhóm tool', nullable: true })
  description: string | null;

  /**
   * Thời gian tạo
   */
  @ApiProperty({ description: 'Thời gian tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật gần nhất
   */
  @ApiProperty({ description: 'Thời gian cập nhật gần nhất' })
  updatedAt: number;

  /**
   * <PERSON>h sách ID của loại agent
   */
  @ApiProperty({ description: 'Danh sách ID của loại agent', type: [Number] })
  typeAgentIds: number[];

  /**
   * Danh sách ID của tool thông thường
   */
  @ApiProperty({ description: 'Danh sách ID của tool thông thường', type: [String] })
  regularToolIds: string[];

  /**
   * Danh sách ID của tool tùy chỉnh
   */
  @ApiProperty({ description: 'Danh sách ID của tool tùy chỉnh', type: [String] })
  customToolIds: string[];

  /**
   * Danh sách ID của tất cả các tool (kết hợp cả thông thường và tùy chỉnh)
   */
  @ApiProperty({ description: 'Danh sách ID của tất cả các tool', type: [String] })
  toolIds: string[];
}
