import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsObject, IsOptional, IsString, Matches, MaxLength, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ToolStatusEnum } from '../../constants/tool-status.enum';

/**
 * DTO cho tham số của tool
 */
export class UserParameterDto {
  @ApiProperty({
    description: 'Loại tham số',
    example: 'object',
  })
  @IsString()
  @IsNotEmpty()
  type: string;

  @ApiProperty({
    description: 'Thuộc tính của tham số',
    example: {
      query: {
        type: 'string',
        description: 'Câu truy vấn tìm kiếm',
      },
    },
  })
  @IsObject()
  @IsOptional()
  properties?: Record<string, any>;

  @ApiProperty({
    description: '<PERSON>á<PERSON> tham số bắt buộc',
    example: ['query'],
    required: false,
  })
  @IsOptional()
  required?: string[];

  @ApiProperty({
    description: 'Mô tả tham số',
    example: 'Tham số đầu vào cho công cụ tìm kiếm',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;
}

/**
 * DTO cho việc tạo mới tool của người dùng
 */
export class CreateUserToolDto {
  @ApiProperty({
    description: 'Tên hiển thị của tool',
    example: 'Công cụ tìm kiếm cá nhân',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Mô tả về tool',
    example: 'Công cụ giúp tìm kiếm thông tin từ nhiều nguồn theo cách riêng của tôi',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Tên tool trong định nghĩa code (chỉ chứa a-z, A-Z, 0-9, _)',
    example: 'mySearchTool',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[a-zA-Z0-9_]+$/, {
    message: 'Tên tool chỉ được chứa a-z, A-Z, 0-9, hoặc dấu gạch dưới',
  })
  @MaxLength(64)
  toolName: string;

  @ApiProperty({
    description: 'Mô tả chi tiết về chức năng của tool',
    example: 'Tìm kiếm thông tin từ nhiều nguồn dữ liệu với bộ lọc tùy chỉnh',
    required: false,
  })
  @IsString()
  @IsOptional()
  toolDescription?: string;

  @ApiProperty({
    description: 'Tham số của tool',
    type: UserParameterDto,
  })
  @ValidateNested()
  @Type(() => UserParameterDto)
  @IsNotEmpty()
  parameters: UserParameterDto;

  @ApiProperty({
    description: 'Trạng thái của tool',
    enum: ToolStatusEnum,
    default: ToolStatusEnum.APPROVED,
    required: false,
  })
  @IsEnum(ToolStatusEnum)
  @IsOptional()
  status?: ToolStatusEnum = ToolStatusEnum.APPROVED;

  @ApiProperty({
    description: 'ID của nhóm tool',
    example: 1,
    required: false,
  })
  @IsOptional()
  groupId?: number;

  @ApiProperty({
    description: 'Mô tả những thay đổi cho phiên bản đầu tiên',
    example: 'Phiên bản đầu tiên',
    required: false,
  })
  @IsString()
  @IsOptional()
  changeDescription?: string;
}
