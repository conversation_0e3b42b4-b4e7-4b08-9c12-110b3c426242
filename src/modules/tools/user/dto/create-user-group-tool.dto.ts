import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID, MaxLength } from 'class-validator';

/**
 * DTO tạo mới nhóm tool của người dùng
 */
export class CreateUserGroupToolDto {
  /**
   * Tên của nhóm tool
   */
  @ApiProperty({ description: 'Tên của nhóm tool' })
  @IsNotEmpty({ message: 'Tên nhóm tool không được để trống' })
  @IsString({ message: 'Tên nhóm tool phải là chuỗi' })
  @MaxLength(100, { message: 'Tên nhóm tool không được vượt quá 100 ký tự' })
  name: string;

  /**
   * Mô tả của nhóm tool
   */
  @ApiProperty({ description: 'Mô tả của nhóm tool', required: false })
  @IsOptional()
  @IsString({ message: '<PERSON><PERSON> tả nhóm tool phải là chuỗi' })
  description?: string;

  /**
   * ID của loại agent
   */
  @ApiProperty({ description: 'ID của loại agent', required: false })
  @IsOptional()
  @IsNumber({}, { message: 'ID loại agent phải là số' })
  typeAgentId?: number;

  /**
   * Danh sách ID của các tool cần thêm vào nhóm
   */
  @ApiProperty({
    description: 'Danh sách ID của các tool cần thêm vào nhóm',
    example: ['cb54cc12-93d6-4d90-9727-a63d1e82df95'],
    required: false,
    type: [String]
  })
  @IsOptional()
  @IsArray({ message: 'toolIds phải là một mảng' })
  @IsUUID('4', { each: true, message: 'Mỗi toolId phải là một UUID hợp lệ' })
  toolIds?: string[];
}
