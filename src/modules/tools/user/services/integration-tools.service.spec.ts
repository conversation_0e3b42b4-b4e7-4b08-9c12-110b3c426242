import { Test, TestingModule } from '@nestjs/testing';
import { IntegrationToolsService } from './integration-tools.service';
import { UserToolRepository } from '../../repositories/user-tool.repository';
import { UserToolVersionRepository } from '../../repositories/user-tool-version.repository';
import { UserTool, UserToolVersion } from '../../entities';
import { ToolStatusEnum } from '../../constants/tool-status.enum';

describe('IntegrationToolsService', () => {
  let service: IntegrationToolsService;
  let userToolRepository: UserToolRepository;
  let userToolVersionRepository: UserToolVersionRepository;

  const mockUserToolRepository = {
    findOne: jest.fn(),
    save: jest.fn(),
  };

  const mockUserToolVersionRepository = {
    save: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        IntegrationToolsService,
        {
          provide: UserToolRepository,
          useValue: mockUserToolRepository,
        },
        {
          provide: UserToolVersionRepository,
          useValue: mockUserToolVersionRepository,
        },
      ],
    }).compile();

    service = module.get<IntegrationToolsService>(IntegrationToolsService);
    userToolRepository = module.get<UserToolRepository>(UserToolRepository);
    userToolVersionRepository = module.get<UserToolVersionRepository>(UserToolVersionRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('integrateFromOpenApi', () => {
    it('should integrate tools and resources from OpenAPI spec', async () => {
      // Mock data
      const userId = 1;
      const openapiSpec = {
        openapi: '3.0.0',
        paths: {
          '/users': {
            get: { 
              summary: 'Get users',
              operationId: 'getUsers',
              parameters: [
                {
                  name: 'limit',
                  in: 'query',
                  schema: { type: 'integer' },
                  description: 'Number of users to return'
                }
              ]
            },
            post: { 
              summary: 'Create user',
              operationId: 'createUser',
              parameters: [
                {
                  name: 'name',
                  in: 'body',
                  schema: { type: 'string' },
                  description: 'User name',
                  required: true
                }
              ]
            }
          }
        }
      };

      // Mock repository responses
      mockUserToolRepository.findOne.mockResolvedValue(null);
      
      const savedTool = new UserTool();
      savedTool.id = 'tool-id';
      mockUserToolRepository.save.mockResolvedValue(savedTool);
      
      mockUserToolVersionRepository.save.mockResolvedValue(new UserToolVersion());

      // Call the service
      const result = await service.integrateFromOpenApi(userId, { openapiSpec });

      // Assertions
      expect(result).toEqual({ toolsCreated: 1, resourcesCreated: 1 });
      expect(mockUserToolRepository.findOne).toHaveBeenCalledTimes(2);
      expect(mockUserToolRepository.save).toHaveBeenCalledTimes(2);
      expect(mockUserToolVersionRepository.save).toHaveBeenCalledTimes(2);
      
      // Verify tool creation
      const toolSaveCall = mockUserToolRepository.save.mock.calls[1][0];
      expect(toolSaveCall.name).toBe('createUser');
      expect(toolSaveCall.userId).toBe(userId);
      expect(toolSaveCall.status).toBe(ToolStatusEnum.APPROVED);
      
      // Verify resource creation
      const resourceSaveCall = mockUserToolRepository.save.mock.calls[0][0];
      expect(resourceSaveCall.name).toBe('getUsers');
      expect(resourceSaveCall.userId).toBe(userId);
      expect(resourceSaveCall.status).toBe(ToolStatusEnum.APPROVED);
    });

    it('should skip existing tools', async () => {
      // Mock data
      const userId = 1;
      const openapiSpec = {
        openapi: '3.0.0',
        paths: {
          '/users': {
            get: { 
              summary: 'Get users',
              operationId: 'getUsers'
            }
          }
        }
      };

      // Mock repository responses - tool already exists
      const existingTool = new UserTool();
      existingTool.id = 'existing-tool-id';
      mockUserToolRepository.findOne.mockResolvedValue(existingTool);

      // Call the service
      const result = await service.integrateFromOpenApi(userId, { openapiSpec });

      // Assertions
      expect(result).toEqual({ toolsCreated: 0, resourcesCreated: 0 });
      expect(mockUserToolRepository.findOne).toHaveBeenCalledTimes(1);
      expect(mockUserToolRepository.save).not.toHaveBeenCalled();
      expect(mockUserToolVersionRepository.save).not.toHaveBeenCalled();
    });
  });
});
