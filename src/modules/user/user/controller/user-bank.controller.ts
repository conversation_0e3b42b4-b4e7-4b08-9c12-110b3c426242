import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { BankService } from '../service/bank.service';
import { BankResponseDto, BankQueryDto } from '../dto/bank';
import { PaginatedResponseDto } from '../dto/common';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtUserGuard } from '@/modules/auth/guards';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { ApiResponseDto } from '@/common/response';

/**
 * Controller xử lý API liên quan đến ngân hàng cho người dùng đã đăng nhập
 */
@ApiTags(SWAGGER_API_TAGS.USER_BANKS)
@Controller('user/banks')
@UseGuards(JwtUserGuard)
@ApiBearerAuth("JWT-auth")
export class UserBankController {
  constructor(private readonly bankService: BankService) {}

  /**
   * Lấy danh sách ngân hàng với phân trang và filter
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách ngân hàng với phân trang và filter' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách ngân hàng với phân trang',
    type: PaginatedResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/PaginatedResponseDto' },
        {
          properties: {
            data: {
              type: 'array',
              items: { $ref: '#/components/schemas/BankResponseDto' }
            }
          }
        }
      ]
    }
  })
  async findAll(
    @CurrentUser() _user: JwtPayload,
    @Query() query: BankQueryDto
  ): Promise<ApiResponseDto<PaginatedResponseDto<BankResponseDto>>> {
    const result = await this.bankService.findAll(query);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy tất cả ngân hàng (không phân trang)
   */
  @Get('all')
  @ApiOperation({ summary: 'Lấy tất cả ngân hàng (không phân trang)' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tất cả ngân hàng',
    type: [BankResponseDto]
  })
  async findAllBanks(
    @CurrentUser() _user: JwtPayload
  ): Promise<ApiResponseDto<BankResponseDto[]>> {
    const result = await this.bankService.findAllBanks();
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy ngân hàng theo mã
   */
  @Get(':bankCode')
  @ApiOperation({ summary: 'Lấy ngân hàng theo mã' })
  @ApiResponse({ status: 200, description: 'Thông tin ngân hàng', type: BankResponseDto })
  @ApiResponse({ status: 404, description: 'Không tìm thấy ngân hàng' })
  async findByCode(
    @CurrentUser() _user: JwtPayload,
    @Param('bankCode') bankCode: string
  ): Promise<ApiResponseDto<BankResponseDto | null>> {
    const result = await this.bankService.findByCode(bankCode);
    return ApiResponseDto.success(result);
  }
}
