import { Test, TestingModule } from '@nestjs/testing';
import { BankService } from '../service/bank.service';
import { BankRepository } from '@modules/user/repositories/bank.repository';
import { BankQueryDto, BankSortField, SortOrder } from '../dto/bank';

describe('BankService', () => {
  let service: BankService;

  // Repository mock
  const mockBankRepository = {
    find: jest.fn(),
    findAll: jest.fn(),
    findByCode: jest.fn(),
    count: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BankService,
        {
          provide: BankRepository,
          useValue: mockBankRepository,
        },
      ],
    }).compile();

    service = module.get<BankService>(BankService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return paginated banks', async () => {
      // Arrange
      const query: BankQueryDto = {
        page: 1,
        limit: 10,
        sortBy: BankSortField.BANK_NAME,
        sortOrder: SortOrder.ASC,
      };

      const banks = [
        {
          bankCode: 'VCB',
          bankName: 'Vietcombank',
          logoPath: '/images/banks/vcb-logo.png',
          fullName: 'Ngân hàng TMCP Ngoại thương Việt Nam',
          iconPath: '/images/banks/vcb-icon.png',
          bin: '970436',
        },
        {
          bankCode: 'TCB',
          bankName: 'Techcombank',
          logoPath: '/images/banks/tcb-logo.png',
          fullName: 'Ngân hàng TMCP Kỹ thương Việt Nam',
          iconPath: '/images/banks/tcb-icon.png',
          bin: '970407',
        },
      ];

      mockBankRepository.find.mockResolvedValue(banks);
      mockBankRepository.count.mockResolvedValue(2);

      // Act
      const result = await service.findAll(query);

      // Assert
      expect(mockBankRepository.find).toHaveBeenCalled();
      expect(mockBankRepository.count).toHaveBeenCalled();
      expect(result.data).toHaveLength(2);
      expect(result.data[0].bankCode).toEqual(banks[0].bankCode);
      expect(result.data[1].bankCode).toEqual(banks[1].bankCode);
      expect(result.meta.total).toEqual(2);
      expect(result.meta.page).toEqual(1);
      expect(result.meta.limit).toEqual(10);
    });

    it('should filter banks by name', async () => {
      // Arrange
      const query: BankQueryDto = {
        page: 1,
        limit: 10,
        bankName: 'Vietcombank',
        sortBy: BankSortField.BANK_NAME,
        sortOrder: SortOrder.ASC,
      };

      const banks = [
        {
          bankCode: 'VCB',
          bankName: 'Vietcombank',
          logoPath: '/images/banks/vcb-logo.png',
          fullName: 'Ngân hàng TMCP Ngoại thương Việt Nam',
          iconPath: '/images/banks/vcb-icon.png',
          bin: '970436',
        },
      ];

      mockBankRepository.find.mockResolvedValue(banks);
      mockBankRepository.count.mockResolvedValue(1);

      // Act
      const result = await service.findAll(query);

      // Assert
      expect(mockBankRepository.find).toHaveBeenCalled();
      expect(result.data).toHaveLength(1);
      expect(result.data[0].bankCode).toEqual(banks[0].bankCode);
      expect(result.meta.total).toEqual(1);
    });
  });

  describe('findAllBanks', () => {
    it('should return all banks', async () => {
      // Arrange
      const banks = [
        {
          bankCode: 'VCB',
          bankName: 'Vietcombank',
          logoPath: '/images/banks/vcb-logo.png',
          fullName: 'Ngân hàng TMCP Ngoại thương Việt Nam',
          iconPath: '/images/banks/vcb-icon.png',
          bin: '970436',
        },
        {
          bankCode: 'TCB',
          bankName: 'Techcombank',
          logoPath: '/images/banks/tcb-logo.png',
          fullName: 'Ngân hàng TMCP Kỹ thương Việt Nam',
          iconPath: '/images/banks/tcb-icon.png',
          bin: '970407',
        },
      ];

      mockBankRepository.findAll.mockResolvedValue(banks);

      // Act
      const result = await service.findAllBanks();

      // Assert
      expect(mockBankRepository.findAll).toHaveBeenCalled();
      expect(result).toHaveLength(2);
      expect(result[0].bankCode).toEqual(banks[0].bankCode);
      expect(result[1].bankCode).toEqual(banks[1].bankCode);
    });
  });

  describe('findByCode', () => {
    it('should return a bank by code', async () => {
      // Arrange
      const bankCode = 'VCB';
      const bank = {
        bankCode: 'VCB',
        bankName: 'Vietcombank',
        logoPath: '/images/banks/vcb-logo.png',
        fullName: 'Ngân hàng TMCP Ngoại thương Việt Nam',
        iconPath: '/images/banks/vcb-icon.png',
        bin: '970436',
      };

      mockBankRepository.findByCode.mockResolvedValue(bank);

      // Act
      const result = await service.findByCode(bankCode);

      // Assert
      expect(mockBankRepository.findByCode).toHaveBeenCalledWith(bankCode);
      expect(result).not.toBeNull();
      expect(result!.bankCode).toEqual(bank.bankCode);
      expect(result!.bankName).toEqual(bank.bankName);
    });

    it('should return null if bank does not exist', async () => {
      // Arrange
      const bankCode = 'INVALID';

      mockBankRepository.findByCode.mockResolvedValue(null);

      // Act
      const result = await service.findByCode(bankCode);

      // Assert
      expect(mockBankRepository.findByCode).toHaveBeenCalledWith(bankCode);
      expect(result).toBeNull();
    });
  });
});
