import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { UserModule } from '@modules/user/user.module';
import { BankService } from '../service/bank.service';
import { BankResponseDto } from '../dto/bank';
import { JwtAuthGuard } from '@/modules/data/knowledge-files/user/tests/__mocks__/@modules/auth/guards/jwt-auth.guard';

describe('Bank API (e2e)', () => {
  let app: INestApplication;

  // Mock data
  const mockBanks: BankResponseDto[] = [
    {
      bankCode: 'VCB',
      bankName: 'Vietcombank',
      logoPath: '/images/banks/vcb-logo.png',
      fullName: 'Ngân hàng TMCP Ngoại thương Việt Nam',
      iconPath: '/images/banks/vcb-icon.png',
      bin: '970436',
    },
    {
      bankCode: 'TCB',
      bankName: 'Techcombank',
      logoPath: '/images/banks/tcb-logo.png',
      fullName: 'Ngân hàng TMCP Kỹ thương Việt Nam',
      iconPath: '/images/banks/tcb-icon.png',
      bin: '970407',
    },
  ];

  // Mock service
  const mockBankService = {
    findAll: jest.fn().mockImplementation((query) => {
      const { page = 1, limit = 10, bankName, bankCode } = query;
      
      let filteredBanks = [...mockBanks];
      
      if (bankName) {
        filteredBanks = filteredBanks.filter(bank => 
          bank.bankName.toLowerCase().includes(bankName.toLowerCase())
        );
      }
      
      if (bankCode) {
        filteredBanks = filteredBanks.filter(bank => 
          bank.bankCode.toLowerCase().includes(bankCode.toLowerCase())
        );
      }
      
      const total = filteredBanks.length;
      const totalPages = Math.ceil(total / limit);
      
      return {
        data: filteredBanks.slice((page - 1) * limit, page * limit),
        meta: {
          total,
          page,
          limit,
          totalPages,
          hasPreviousPage: page > 1,
          hasNextPage: page < totalPages,
        },
      };
    }),
    findAllBanks: jest.fn().mockResolvedValue(mockBanks),
    findByCode: jest.fn().mockImplementation((bankCode) => {
      const bank = mockBanks.find(b => b.bankCode === bankCode);
      return bank || null;
    }),
  };

  // Mock JWT guard
  const mockJwtAuthGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [UserModule],
    })
      .overrideProvider(BankService)
      .useValue(mockBankService)
      .overrideGuard(JwtAuthGuard)
      .useValue(mockJwtAuthGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe({ transform: true }));
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('/banks (GET)', () => {
    it('should return paginated banks', () => {
      return request(app.getHttpServer())
        .get('/banks')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveLength(2);
          expect(res.body.meta.total).toBe(2);
          expect(res.body.meta.page).toBe(1);
        });
    });

    it('should filter banks by name', () => {
      return request(app.getHttpServer())
        .get('/banks?bankName=Vietcombank')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveLength(1);
          expect(res.body.data[0].bankCode).toBe('VCB');
          expect(res.body.meta.total).toBe(1);
        });
    });

    it('should filter banks by code', () => {
      return request(app.getHttpServer())
        .get('/banks?bankCode=TCB')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveLength(1);
          expect(res.body.data[0].bankName).toBe('Techcombank');
          expect(res.body.meta.total).toBe(1);
        });
    });

    it('should paginate results', () => {
      return request(app.getHttpServer())
        .get('/banks?page=1&limit=1')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveLength(1);
          expect(res.body.meta.total).toBe(2);
          expect(res.body.meta.page).toBe(1);
          expect(res.body.meta.limit).toBe(1);
          expect(res.body.meta.totalPages).toBe(2);
          expect(res.body.meta.hasNextPage).toBe(true);
        });
    });
  });

  describe('/banks/all (GET)', () => {
    it('should return all banks', () => {
      return request(app.getHttpServer())
        .get('/banks/all')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveLength(2);
          expect(res.body[0].bankCode).toBe('VCB');
          expect(res.body[1].bankCode).toBe('TCB');
        });
    });
  });

  describe('/banks/:bankCode (GET)', () => {
    it('should return a bank by code', () => {
      return request(app.getHttpServer())
        .get('/banks/VCB')
        .expect(200)
        .expect((res) => {
          expect(res.body.bankCode).toBe('VCB');
          expect(res.body.bankName).toBe('Vietcombank');
        });
    });

    it('should return null if bank does not exist', () => {
      return request(app.getHttpServer())
        .get('/banks/INVALID')
        .expect(200)
        .expect((res) => {
          expect(res.body).toBeNull();
        });
    });
  });

  describe('/user/banks (GET)', () => {
    it('should return paginated banks for authenticated user', () => {
      return request(app.getHttpServer())
        .get('/user/banks')
        .expect(200)
        .expect((res) => {
          expect(res.body.data).toHaveLength(2);
          expect(res.body.meta.total).toBe(2);
        });
    });
  });

  describe('/user/banks/all (GET)', () => {
    it('should return all banks for authenticated user', () => {
      return request(app.getHttpServer())
        .get('/user/banks/all')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveLength(2);
        });
    });
  });

  describe('/user/banks/:bankCode (GET)', () => {
    it('should return a bank by code for authenticated user', () => {
      return request(app.getHttpServer())
        .get('/user/banks/VCB')
        .expect(200)
        .expect((res) => {
          expect(res.body.bankCode).toBe('VCB');
        });
    });
  });
});
