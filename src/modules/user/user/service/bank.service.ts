import { Injectable, Logger } from '@nestjs/common';
import { BankRepository } from '@modules/user/repositories/bank.repository';
import { Bank } from '@modules/user/entities/bank.entity';
import { BankResponseDto, BankQueryDto, BankSortField, SortOrder } from '../dto/bank';
import { PaginatedResponseDto, PaginationMetaDto } from '../dto/common';
import { Like, FindOptionsWhere } from 'typeorm';
import { CdnService } from '@/shared/services/cdn.service';
import { TimeIntervalEnum } from '@/shared/utils';

/**
 * Service xử lý logic liên quan đến ngân hàng
 */
@Injectable()
export class BankService {
  private readonly logger = new Logger(BankService.name);

  constructor(
    private readonly bankRepository: BankRepository,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * <PERSON><PERSON>y danh sách ngân hàng với phân trang và filter
   * @param query Tham số truy vấn
   * @returns Danh sách ngân hàng với phân trang
   */
  async findAll(query: BankQueryDto): Promise<PaginatedResponseDto<BankResponseDto>> {
    this.logger.debug(`Lấy danh sách ngân hàng với query: ${JSON.stringify(query)}`);

    const { page = 1, limit = 10, bankName, bankCode, sortBy = BankSortField.BANK_NAME, sortOrder = SortOrder.ASC } = query;

    // Tính toán offset
    const offset = (page - 1) * limit;

    // Tạo điều kiện cơ bản
    const where: FindOptionsWhere<Bank> = {};

    // Thêm điều kiện tìm kiếm theo tên ngân hàng
    if (bankName) {
      where.bankName = Like(`%${bankName}%`);
    }

    // Thêm điều kiện tìm kiếm theo mã ngân hàng
    if (bankCode) {
      where.bankCode = Like(`%${bankCode}%`);
    }

    // Đếm tổng số ngân hàng
    const total = await this.bankRepository.count({ where });

    // Lấy danh sách ngân hàng với phân trang và sắp xếp
    const banks = await this.bankRepository.find({
      where,
      order: { [sortBy]: sortOrder },
      skip: offset,
      take: limit,
    });

    // Chuyển đổi kết quả thành DTO
    const data = banks.map(bank => this.mapToDto(bank));

    // Tạo thông tin phân trang
    const totalPages = Math.ceil(total / limit);
    const meta: PaginationMetaDto = {
      total,
      page,
      limit,
      totalPages,
      hasPreviousPage: page > 1,
      hasNextPage: page < totalPages,
    };

    return {
      data,
      meta,
    };
  }

  /**
   * Lấy tất cả ngân hàng (không phân trang)
   * @returns Danh sách ngân hàng
   */
  async findAllBanks(): Promise<BankResponseDto[]> {
    this.logger.debug('Lấy tất cả ngân hàng');

    const banks = await this.bankRepository.findAll();

    // Chuyển đổi kết quả thành DTO
    return banks.map(bank => this.mapToDto(bank));
  }

  /**
   * Lấy ngân hàng theo mã
   * @param bankCode Mã ngân hàng
   * @returns Ngân hàng hoặc null nếu không tìm thấy
   */
  async findByCode(bankCode: string): Promise<BankResponseDto | null> {
    this.logger.debug(`Lấy ngân hàng với mã: ${bankCode}`);

    const bank = await this.bankRepository.findByCode(bankCode);

    if (!bank) {
      return null;
    }

    return this.mapToDto(bank);
  }

  /**
   * Chuyển đổi từ entity sang DTO
   * @param bank Bank entity
   * @returns Bank DTO
   */
  private mapToDto(bank: Bank): BankResponseDto {
    const dto = new BankResponseDto();
    dto.bankCode = bank.bankCode;
    dto.bankName = bank.bankName;
    dto.logoPath = this.cdnService.generateUrlView(bank.logoPath, TimeIntervalEnum.FIVE_MINUTES) || '';
    dto.fullName = bank.fullName;
    dto.iconPath = this.cdnService.generateUrlView(bank.iconPath, TimeIntervalEnum.FIVE_MINUTES) || '';
    dto.bin = bank.bin;
    return dto;
  }
}
