import { Injectable, ConflictException, NotFoundException } from '@nestjs/common';
import { UserService } from './user.service';
import { UserHasRoleRepository, UserRoleRepository } from '../../repositories';

/**
 * Service xử lý logic liên quan đến vai trò người dùng
 */
@Injectable()
export class UserRoleService {
  constructor(
    private readonly userHasRoleRepository: UserHasRoleRepository,
    private readonly userRoleRepository: UserRoleRepository,
    private readonly userService: UserService,
  ) {}

  /**
   * Thêm vai trò USER cho người dùng
   * @param userId ID của người dùng
   * @returns Thông báo kết quả
   */
  async addUserRole(userId: number): Promise<{ message: string }> {
    // Kiểm tra người dùng tồn tại
    await this.userService.findOne(userId);

    // Tìm vai trò USER
    const userRole = await this.userRoleRepository.findByType('USER');

    // Kiểm tra người dùng đã có vai trò này chưa
    const hasRole = await this.userHasRoleRepository.hasRole(userId, userRole.id);
    
    if (hasRole) {
      throw new ConflictException(`Người dùng đã có vai trò USER`);
    }

    // Thêm vai trò cho người dùng
    await this.userHasRoleRepository.addRoleToUser(userId, userRole.id);

    return { message: `Đã thêm vai trò USER cho người dùng có ID ${userId}` };
  }

  /**
   * Lấy tất cả vai trò của người dùng
   * @param userId ID của người dùng
   * @returns Danh sách vai trò của người dùng
   */
  async getUserRoles(userId: number): Promise<{ id: number; type: string; name: string }[]> {
    // Kiểm tra người dùng tồn tại
    await this.userService.findOne(userId);

    // Lấy tất cả ID vai trò của người dùng
    const roleIds = await this.userHasRoleRepository.findRolesByUserId(userId);
    if (roleIds.length === 0) {
      return [];
    }

    // Lấy thông tin chi tiết của các vai trò
    const roles = await Promise.all(
      roleIds.map(async (roleId) => {
        try {
          const role = await this.userRoleRepository.findById(roleId);
          return {
            id: role.id,
            type: role.type,
            name: role.name,
          };
        } catch (error) {
          if (error instanceof NotFoundException) {
            return null;
          }
          throw error;
        }
      })
    );

    // Lọc bỏ các vai trò không tồn tại
    return roles.filter(role => role !== null);
  }

  /**
   * Xóa vai trò của người dùng
   * @param userId ID của người dùng
   * @param roleType Loại vai trò
   * @returns Thông báo kết quả
   */
  async removeUserRole(userId: number, roleType: string): Promise<{ message: string }> {
    // Kiểm tra người dùng tồn tại
    await this.userService.findOne(userId);

    // Tìm vai trò theo loại
    const role = await this.userRoleRepository.findByType(roleType);

    // Xóa vai trò của người dùng
    const removed = await this.userHasRoleRepository.removeRoleFromUser(userId, role.id);
    if (!removed) {
      throw new NotFoundException(`Người dùng không có vai trò ${roleType}`);
    }

    return { message: `Đã xóa vai trò ${roleType} của người dùng có ID ${userId}` };
  }

  /**
   * Kiểm tra người dùng có vai trò không
   * @param userId ID của người dùng
   * @param roleType Loại vai trò
   * @returns true nếu người dùng có vai trò, ngược lại false
   */
  async hasRole(userId: number, roleType: string): Promise<boolean> {
    try {
      // Tìm vai trò theo loại
      const role = await this.userRoleRepository.findByType(roleType);
      
      // Kiểm tra người dùng có vai trò không
      return await this.userHasRoleRepository.hasRole(userId, role.id);
    } catch (error) {
      if (error instanceof NotFoundException) {
        return false;
      }
      throw error;
    }
  }
}
