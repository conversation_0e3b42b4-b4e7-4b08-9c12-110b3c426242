import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Permission, UserHasRole, UserRoleHasPermission } from '../../entities';

@Injectable()
export class PermissionService {
  constructor(
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
    @InjectRepository(UserHasRole)
    private readonly userHasRoleRepository: Repository<UserHasRole>,
    @InjectRepository(UserRoleHasPermission)
    private readonly userRoleHasPermissionRepository: Repository<UserRoleHasPermission>,
  ) {}

  /**
   * Lấy tất cả các permission
   * @returns Danh sách tất cả các permission
   */
  async findAll(): Promise<Permission[]> {
    return this.permissionRepository.find();
  }

  /**
   * Lấy permission theo ID
   * @param id ID của permission
   * @returns Permission tìm thấy hoặc null
   */
  async findById(id: number): Promise<Permission | null> {
    return this.permissionRepository.findOne({ where: { id } });
  }

  /**
   * Lấy permission theo module và action
   * @param module Tên module
   * @param action Tên action
   * @returns Permission tìm thấy hoặc null
   */
  async findByModuleAndAction(module: string, action: string): Promise<Permission | null> {
    return this.permissionRepository.findOne({
      where: { module, action },
    });
  }

  /**
   * Lấy tất cả các permission của một role
   * @param roleId ID của role
   * @returns Danh sách các permission
   */
  async findPermissionsByRoleId(roleId: number): Promise<Permission[]> {
    // Lấy tất cả các permission_id của role
    const rolePermissions = await this.userRoleHasPermissionRepository.find({
      where: { roleId },
    });

    // Lấy thông tin chi tiết của các permission
    const permissionIds = rolePermissions.map(rp => rp.permissionId);
    if (permissionIds.length === 0) {
      return [];
    }

    return this.permissionRepository.find({
      where: { id: In(permissionIds) },
    });
  }

  /**
   * Lấy tất cả các permission của một user
   * @param userId ID của user
   * @returns Danh sách các permission
   */
  async findPermissionsByUserId(userId: number): Promise<Permission[]> {
    // Lấy tất cả các role của user
    const userRoles = await this.userHasRoleRepository.find({
      where: { userId },
    });

    // Lấy tất cả các role_id của user
    const roleIds = userRoles.map(ur => ur.roleId);
    if (roleIds.length === 0) {
      return [];
    }

    // Lấy tất cả các permission_id của các role
    const rolePermissions = await this.userRoleHasPermissionRepository.find({
      where: { roleId: In(roleIds) },
    });

    // Lấy thông tin chi tiết của các permission
    const permissionIds = rolePermissions.map(rp => rp.permissionId);
    if (permissionIds.length === 0) {
      return [];
    }

    return this.permissionRepository.find({
      where: { id: In(permissionIds) },
    });
  }

  /**
   * Lấy tất cả các permission của một user theo định dạng "module:action"
   * @param userId ID của user
   * @returns Danh sách các permission theo định dạng "module:action"
   */
  async getUserPermissions(userId: number): Promise<string[]> {
    try {
      const permissions = await this.findPermissionsByUserId(userId);

      // Chuyển đổi các permission thành định dạng "module:action"
      const formattedPermissions = permissions
        .filter(permission => permission.module && permission.action) // Lọc bỏ các permission không có module hoặc action
        .map(permission => `${permission.module}:${permission.action}`);

      // Nếu không có permission nào, trả về danh sách quyền mặc định
      if (formattedPermissions.length === 0) {
        return this.getDefaultUserPermissions();
      }

      return formattedPermissions;
    } catch (error) {
      console.error(`Error getting permissions for user ${userId}:`, error);
      // Trả về danh sách quyền mặc định nếu có lỗi
      return this.getDefaultUserPermissions();
    }
  }

  /**
   * Lấy danh sách quyền mặc định cho người dùng
   * @returns Danh sách quyền mặc định
   */
  private getDefaultUserPermissions(): string[] {
    return [
      'user:read',
      'user:update',
      'profile:read',
      'profile:update',
      'document:read',
      'document:create',
      'document:update',
      'document:delete',
      'chat:read',
      'chat:create',
      'chat:update',
      'chat:delete'
    ];
  }

  /**
   * Kiểm tra xem user có permission cụ thể hay không
   * @param userId ID của user
   * @param requiredPermission Permission cần kiểm tra theo định dạng "module:action"
   * @returns true nếu user có permission, ngược lại false
   */
  async hasPermission(userId: number, requiredPermission: string): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId);
    return userPermissions.includes(requiredPermission);
  }

  /**
   * Kiểm tra xem user có tất cả các permission trong danh sách hay không
   * @param userId ID của user
   * @param requiredPermissions Danh sách các permission cần kiểm tra theo định dạng "module:action"
   * @returns true nếu user có tất cả các permission, ngược lại false
   */
  async hasAllPermissions(userId: number, requiredPermissions: string[]): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId);
    return requiredPermissions.every(permission => userPermissions.includes(permission));
  }

  /**
   * Kiểm tra xem user có ít nhất một permission trong danh sách hay không
   * @param userId ID của user
   * @param requiredPermissions Danh sách các permission cần kiểm tra theo định dạng "module:action"
   * @returns true nếu user có ít nhất một permission, ngược lại false
   */
  async hasAnyPermission(userId: number, requiredPermissions: string[]): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId);
    return requiredPermissions.some(permission => userPermissions.includes(permission));
  }
}
