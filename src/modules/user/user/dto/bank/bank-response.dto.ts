import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi thông tin ngân hàng
 */
export class BankResponseDto {
  /**
   * Mã ngân hàng
   * @example "VCB"
   */
  @ApiProperty({
    description: 'Mã ngân hàng',
    example: 'VCB',
  })
  bankCode: string;

  /**
   * Tên ngân hàng
   * @example "Vietcombank"
   */
  @ApiProperty({
    description: 'Tên ngân hàng',
    example: 'Vietcombank',
  })
  bankName: string;

  /**
   * Đường dẫn logo
   * @example "/images/banks/vcb-logo.png"
   */
  @ApiProperty({
    description: 'Đường dẫn logo',
    example: '/images/banks/vcb-logo.png',
  })
  logoPath: string;

  /**
   * Tên đầy đủ của ngân hàng
   * @example "Ngân hàng TMCP Ngoại thương Việt Nam"
   */
  @ApiProperty({
    description: 'Tên đầy đủ của ngân hàng',
    example: 'Ngân hàng TMCP Ngoại thương Việt Nam',
  })
  fullName: string;

  /**
   * Đường dẫn icon
   * @example "/images/banks/vcb-icon.png"
   */
  @ApiProperty({
    description: 'Đường dẫn icon',
    example: '/images/banks/vcb-icon.png',
  })
  iconPath: string;

  /**
   * Mã BIN của ngân hàng
   * @example "970436"
   */
  @ApiProperty({
    description: 'Mã BIN của ngân hàng',
    example: '970436',
  })
  bin: string;
}
