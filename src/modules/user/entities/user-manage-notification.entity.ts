import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { User } from './user.entity';

/**
 * Entity đại diện cho bảng user_manage_notification trong cơ sở dữ liệu
 * Bảng tùy chỉnh nhận email thông báo của người dùng
 */
@Entity('user_manage_notification')
export class UserManageNotification {
  /**
   * ID tự tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', unique: true })
  userId: number;

  /**
   * Nhận email thông báo hệ thống
   */
  @Column({ name: 'receive_account_system_emails', type: 'boolean', default: true })
  receiveAccountSystemEmails: boolean;

  /**
   * Nhận email thông báo thanh toán
   */
  @Column({ name: 'receive_billing_emails', type: 'boolean', default: true })
  receiveBillingEmails: boolean;

  /**
   * Nhận email thông báo tính năng mới
   */
  @Column({ name: 'receive_new_feature_emails', type: 'boolean', default: true })
  receiveNewFeatureEmails: boolean;

  /**
   * Nhận email thông báo tiếp thị liên kết
   */
  @Column({ name: 'receive_affiliate_emails', type: 'boolean', default: true })
  receiveAffiliateEmails: boolean;

  /**
   * Nhận email thông báo tài liệu
   */
  @Column({ name: 'receive_documentation_emails', type: 'boolean', nullable: true })
  receiveDocumentationEmails: boolean;

  /**
   * Nhận email quảng cáo
   */
  @Column({ name: 'receive_promotional_emails', type: 'boolean', default: true })
  receivePromotionalEmails: boolean;
}
