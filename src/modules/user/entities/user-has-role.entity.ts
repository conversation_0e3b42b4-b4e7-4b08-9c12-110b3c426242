import { Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_has_roles
 * Bảng trung gian để lưu trữ mối quan hệ nhiều-nhiều giữa users và user_roles
 */
@Entity('user_has_roles')
export class UserHasRole {
  /**
   * ID của người dùng
   * Là một phần của khóa chính kết hợp
   * Tham chiếu đến bảng users
   */
  @PrimaryColumn({ name: 'user_id', type: 'integer' })
  userId: number;

  /**
   * ID của vai trò
   * Là một phần của khóa chính kết hợp
   * Tham chiếu đến bảng user_roles
   */
  @PrimaryColumn({ name: 'role_id', type: 'integer' })
  roleId: number;
}
