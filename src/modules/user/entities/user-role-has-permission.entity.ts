import { Entity, Column, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_roles_has_permission
 * Bảng trung gian để lưu trữ mối quan hệ nhiều-nhiều giữa user_roles và permissions
 */
@Entity('user_roles_has_permission')
export class UserRoleHasPermission {
  /**
   * ID của vai trò người dùng
   * Là một phần của khóa chính kết hợp
   * Tham chiếu đến bảng user_roles
   */
  @PrimaryColumn({ name: 'role_id', type: 'integer' })
  roleId: number;

  /**
   * ID của quyền
   * Là một phần của khóa chính kết hợp
   * Tham chiếu đến bảng permissions
   */
  @PrimaryColumn({ name: 'permission_id', type: 'integer' })
  permissionId: number;
}
