import { Column, <PERSON>ti<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryColumn } from 'typeorm';
import { User } from './user.entity';

/**
 * Entity đại diện cho bảng device_info trong cơ sở dữ liệu
 * Bảng lưu thông tin thiết bị của người dùng
 */
@Entity('device_info')
export class DeviceInfo {
  /**
   * ID của thiết bị (UUID)
   */
  @PrimaryColumn({ name: 'id', type: 'uuid' })
  id: string;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', nullable: true })
  userId: number;

  /**
   * Fingerprint của thiết bị
   */
  @Column({ name: 'fingerprint', length: 255, nullable: false })
  fingerprint: string;

  /**
   * Địa chỉ IP
   */
  @Column({ name: 'ip_address', length: 45, nullable: true })
  ipAddress: string;

  /**
   * User agent
   */
  @Column({ name: 'user_agent', type: 'text', nullable: true })
  userAgent: string;

  /**
   * Trình duyệt
   */
  @Column({ name: 'browser', length: 255, nullable: true })
  browser: string;

  /**
   * Hệ điều hành
   */
  @Column({ name: 'operating_system', length: 255, nullable: true })
  operatingSystem: string;

  /**
   * Thiết bị đáng tin cậy
   */
  @Column({ name: 'is_trusted', type: 'boolean', default: false })
  isTrusted: boolean;

  /**
   * Thời gian đăng nhập gần nhất
   */
  @Column({ name: 'last_login', type: 'bigint', nullable: true })
  lastLogin: number;

  /**
   * Thời gian tạo
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: false })
  updatedAt: number;
}
