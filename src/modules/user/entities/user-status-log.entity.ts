import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>umn, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { User } from './user.entity';
import { UserStatusEventEnum } from '../enums/user-status-event.enum';

/**
 * Entity đại diện cho bảng user_status_logs trong cơ sở dữ liệu
 * Bảng lưu lịch sử thay đổi trạng thái của người dùng (khóa/mở khóa)
 */
@Entity('user_status_logs')
export class UserStatusLog {
  /**
   * ID tự tăng
   */
  @PrimaryGeneratedColumn('increment', { name: 'id' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', nullable: false })
  userId: number;

  /**
   * Loại sự kiện
   */
  @Column({ 
    name: 'event_type', 
    type: 'enum', 
    enum: UserStatusEventEnum, 
    nullable: false,
    comment: 'Loạ<PERSON> sự kiện (USER_BLOCK, USER_UNBLOCK, USER_SUSPEND, USER_ACTIVATE)'
  })
  eventType: UserStatusEventEnum;

  /**
   * Lý do thay đổi trạng thái
   */
  @Column({ name: 'reason', type: 'text', nullable: true, comment: 'Lý do thay đổi trạng thái' })
  reason: string;

  /**
   * ID của admin thực hiện hành động
   */
  @Column({ name: 'created_by', nullable: false, comment: 'ID của admin thực hiện hành động' })
  createdBy: number;

  /**
   * Thông tin bổ sung (JSON)
   */
  @Column({ name: 'info', type: 'jsonb', nullable: true, comment: 'Thông tin bổ sung' })
  info: Record<string, any>;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'Thời gian tạo' })
  createdAt: number;

  /**
   * Quan hệ với bảng User
   */
  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;
}
