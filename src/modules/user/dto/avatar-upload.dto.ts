import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber, IsPositive } from 'class-validator';
import { ImageTypeEnum } from '@shared/utils/file/image-media_type.util';
import { Type } from 'class-transformer';

/**
 * DTO cho việc tạo URL tải lên avatar
 */
export class AvatarUploadDto {
  @ApiProperty({
    description: 'Loại hình ảnh',
    enum: ImageTypeEnum,
    example: 'image/jpeg',
  })
  @IsNotEmpty({ message: '<PERSON><PERSON><PERSON> hình ảnh không được để trống' })
  @IsEnum(ImageTypeEnum, { message: '<PERSON>ại hình ảnh không hợp lệ' })
  imageType: ImageTypeEnum;

  @ApiProperty({
    description: '<PERSON>ích thước tối đa của file (bytes)',
    example: 2097152,
  })
  @IsNotEmpty({ message: '<PERSON><PERSON><PERSON> thước tối đa không được để trống' })
  @IsNumber({}, { message: '<PERSON><PERSON><PERSON> thước tối đa phải là số' })
  @IsPositive({ message: 'Kích thước tối đa phải là số dương' })
  @Type(() => Number)
  maxSize: number;
}

/**
 * DTO cho phản hồi URL tải lên avatar
 */
export class AvatarUploadResponseDto {
  @ApiProperty({
    description: 'URL tạm thời để tải lên avatar',
    example: 'https://example.com/presigned-url',
  })
  uploadUrl: string;

  @ApiProperty({
    description: 'Khóa S3 của avatar',
    example: 'avatars/user-123/avatar-1234567890.jpg',
  })
  avatarKey: string;

  @ApiProperty({
    description: 'Thời gian hết hạn của URL (giây)',
    example: 300,
  })
  expiresIn: number;
}

/**
 * DTO cho việc cập nhật avatar
 */
export class UpdateAvatarDto {
  @ApiProperty({
    description: 'Khóa S3 của avatar',
    example: 'avatars/user-123/avatar-1234567890.jpg',
  })
  @IsNotEmpty({ message: 'Khóa avatar không được để trống' })
  avatarKey: string;
}
