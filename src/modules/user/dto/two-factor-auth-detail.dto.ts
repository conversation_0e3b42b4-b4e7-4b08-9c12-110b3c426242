import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho việc trả về thông tin chi tiết về cài đặt xác thực hai yếu tố
 */
export class TwoFactorAuthDetailDto {
  @ApiProperty({
    description: 'ID của cài đặt xác thực hai yếu tố',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của người dùng',
    example: 123,
  })
  userId: number;

  @ApiProperty({
    description: 'Trạng thái bật/tắt xác thực hai lớp qua SMS',
    example: false,
  })
  otpSmsEnabled: boolean;

  @ApiProperty({
    description: 'Trạng thái bật/tắt xác thực hai lớp qua email',
    example: true,
  })
  otpEmailEnabled: boolean;

  @ApiProperty({
    description: 'Trạng thái bật/tắt xác thực hai lớp qua Google Authenticator',
    example: false,
  })
  googleAuthenticatorEnabled: boolean;

  @ApiProperty({
    description: 'Trạng thái xác nhận cài đặt Google Authenticator',
    example: false,
  })
  isGoogleAuthenticatorConfirmed: boolean;

  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1678901234567,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1678901234567,
  })
  updatedAt: number;

  @ApiProperty({
    description: 'Có ít nhất một phương thức xác thực hai lớp được bật',
    example: true,
  })
  is2FAEnabled: boolean;
}
