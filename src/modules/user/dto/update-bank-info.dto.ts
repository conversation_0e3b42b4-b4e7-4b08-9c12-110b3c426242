import { IsOptional, IsString, Max<PERSON>ength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateBankInfoDto {
  @ApiProperty({
    description: 'Mã ngân hàng',
    example: 'VIETCOMBANK',
    required: true,
  })
  @IsString()
  @MaxLength(20)
  bankCode: string;

  @ApiProperty({
    description: 'Số tài khoản',
    example: '**********',
    required: true,
  })
  @IsString()
  @MaxLength(50)
  accountNumber: string;

  @ApiProperty({
    description: 'Tên chủ tài khoản',
    example: 'NGUYEN VAN A',
    required: true,
  })
  @IsString()
  @MaxLength(255)
  accountHolder: string;

  @ApiProperty({
    description: 'Chi nhánh ngân hàng',
    example: 'Chi nhánh Hà Nội',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  bankBranch?: string;
} 