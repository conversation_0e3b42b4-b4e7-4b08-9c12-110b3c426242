import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MinLength, Matches } from 'class-validator';

/**
 * DTO cho request đổi mật khẩu
 */
export class ChangePasswordDto {
  @ApiProperty({
    description: 'Mật khẩu hiện tại của người dùng',
    example: 'currentPassword123',
  })
  @IsString({ message: 'Mật khẩu hiện tại phải là chuỗi' })
  @IsNotEmpty({ message: 'Mật khẩu hiện tại không được để trống' })
  currentPassword: string;

  @ApiProperty({
    description: 'Mật khẩu mới của người dùng',
    example: 'newPassword123',
  })
  @IsString({ message: 'Mật khẩu mới phải là chuỗi' })
  @IsNotEmpty({ message: 'Mật khẩu mới không được để trống' })
  @MinLength(8, { message: '<PERSON>ật khẩu mới phải có ít nhất 8 ký tự' })
  @Matches(/((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
    message: 'Mật khẩu mới phải chứa ít nhất 1 chữ hoa, 1 chữ thường và 1 số hoặc ký tự đặc biệt',
  })
  newPassword: string;

  @ApiProperty({
    description: 'Xác nhận mật khẩu mới',
    example: 'newPassword123',
  })
  @IsString({ message: 'Xác nhận mật khẩu mới phải là chuỗi' })
  @IsNotEmpty({ message: 'Xác nhận mật khẩu mới không được để trống' })
  confirmPassword: string;
}
