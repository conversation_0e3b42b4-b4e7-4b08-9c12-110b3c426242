import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere, FindManyOptions } from 'typeorm';
import { UserStatusLog } from '../entities/user-status-log.entity';
import { UserStatusEventEnum } from '../enums/user-status-event.enum';
import { PaginatedResult } from '@/common/response';

/**
 * Repository cho UserStatusLog
 */
@Injectable()
export class UserStatusLogRepository {
  constructor(
    @InjectRepository(UserStatusLog)
    private readonly repository: Repository<UserStatusLog>,
  ) {}

  /**
   * Tạo log mới về thay đổi trạng thái người dùng
   * @param userId ID của người dùng
   * @param eventType Loại sự kiện
   * @param reason Lý do thay đổi trạng thái
   * @param createdBy ID của admin thực hiện hành động
   * @param info Thông tin bổ sung (tùy chọn)
   * @returns Log đã tạo
   */
  async create(
    userId: number,
    eventType: UserStatusEventEnum,
    reason: string,
    createdBy: number,
    info?: Record<string, any>,
  ): Promise<UserStatusLog> {
    const log = this.repository.create({
      userId,
      eventType,
      reason,
      createdBy,
      info,
      createdAt: Date.now(),
    });

    return this.repository.save(log);
  }

  /**
   * Tìm tất cả log của một người dùng
   * @param userId ID của người dùng
   * @returns Danh sách log
   */
  async findByUserId(userId: number): Promise<UserStatusLog[]> {
    return this.repository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tìm tất cả log theo loại sự kiện
   * @param eventType Loại sự kiện
   * @returns Danh sách log
   */
  async findByEventType(eventType: UserStatusEventEnum): Promise<UserStatusLog[]> {
    return this.repository.find({
      where: { eventType },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Tìm log với phân trang
   * @param options Tùy chọn tìm kiếm
   * @param page Trang hiện tại
   * @param limit Số lượng kết quả trên mỗi trang
   * @returns Kết quả phân trang
   */
  async findWithPagination(
    options: FindOptionsWhere<UserStatusLog>,
    page: number = 1,
    limit: number = 10,
  ): Promise<PaginatedResult<UserStatusLog>> {
    const [items, totalItems] = await this.repository.findAndCount({
      where: options,
      order: { createdAt: 'DESC' },
      skip: (page - 1) * limit,
      take: limit,
    });

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }
}
