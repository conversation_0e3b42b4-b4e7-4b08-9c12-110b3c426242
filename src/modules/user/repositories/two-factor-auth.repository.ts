import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TwoFactorAuth } from '../entities';
import { AppException, ErrorCode } from '@common/exceptions';

@Injectable()
export class TwoFactorAuthRepository {
  constructor(
    @InjectRepository(TwoFactorAuth)
    private readonly repository: Repository<TwoFactorAuth>,
  ) {}

  /**
   * Tìm cấu hình xác thực hai yếu tố của người dùng
   * @param userId ID của người dùng
   * @returns Cấu hình xác thực hai yếu tố
   */
  async findByUserId(userId: number): Promise<TwoFactorAuth | null> {
    return this.repository.findOne({ where: { userId } });
  }

  /**
   * Tạo hoặc cập nhật cấu hình xác thực hai yếu tố
   * @param twoFactorAuth C<PERSON><PERSON> hình xác thực hai yếu tố
   * @returns C<PERSON>u hình xác thực hai yếu tố đã được lưu
   */
  async save(twoFactorAuth: TwoFactorAuth): Promise<TwoFactorAuth> {
    return this.repository.save(twoFactorAuth);
  }

  /**
   * Tạo mới cấu hình xác thực hai yếu tố cho người dùng
   * @param userId ID của người dùng
   * @returns Cấu hình xác thực hai yếu tố mới
   */
  async create(userId: number): Promise<TwoFactorAuth> {
    const now = Date.now();
    const twoFactorAuth = new TwoFactorAuth();
    twoFactorAuth.userId = userId;
    twoFactorAuth.otpSmsEnabled = false;
    twoFactorAuth.otpEmailEnabled = false;
    twoFactorAuth.googleAuthenticatorEnabled = false;
    twoFactorAuth.isGoogleAuthenticatorConfirmed = false;
    twoFactorAuth.createdAt = now;
    twoFactorAuth.updatedAt = now;
    return this.repository.save(twoFactorAuth);
  }

  /**
   * Lấy hoặc tạo mới cấu hình xác thực hai yếu tố cho người dùng
   * @param userId ID của người dùng
   * @returns Cấu hình xác thực hai yếu tố
   */
  async findOrCreate(userId: number): Promise<TwoFactorAuth> {
    const twoFactorAuth = await this.findByUserId(userId);
    if (twoFactorAuth) {
      return twoFactorAuth;
    }
    return this.create(userId);
  }

  /**
   * Bật/tắt xác thực hai yếu tố qua SMS
   * @param userId ID của người dùng
   * @param enabled Trạng thái bật/tắt
   * @returns Cấu hình xác thực hai yếu tố đã cập nhật
   */
  async toggleSmsAuth(userId: number, enabled: boolean): Promise<TwoFactorAuth> {
    const twoFactorAuth = await this.findOrCreate(userId);
    twoFactorAuth.otpSmsEnabled = enabled;
    twoFactorAuth.updatedAt = Date.now();
    return this.repository.save(twoFactorAuth);
  }

  /**
   * Bật/tắt xác thực hai yếu tố qua email
   * @param userId ID của người dùng
   * @param enabled Trạng thái bật/tắt
   * @returns Cấu hình xác thực hai yếu tố đã cập nhật
   */
  async toggleEmailAuth(userId: number, enabled: boolean): Promise<TwoFactorAuth> {
    const twoFactorAuth = await this.findOrCreate(userId);
    twoFactorAuth.otpEmailEnabled = enabled;
    twoFactorAuth.updatedAt = Date.now();
    return this.repository.save(twoFactorAuth);
  }

  /**
   * Bật/tắt xác thực hai yếu tố qua Google Authenticator
   * @param userId ID của người dùng
   * @param enabled Trạng thái bật/tắt
   * @param secret Secret key (chỉ cần khi bật)
   * @returns Cấu hình xác thực hai yếu tố đã cập nhật
   */
  async toggleGoogleAuth(userId: number, enabled: boolean, secret?: string): Promise<TwoFactorAuth> {
    const twoFactorAuth = await this.findOrCreate(userId);
    
    if (enabled && !secret) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Secret key là bắt buộc khi bật Google Authenticator');
    }
    
    twoFactorAuth.googleAuthenticatorEnabled = enabled;
    if (enabled && secret) {
      twoFactorAuth.googleAuthenticatorSecret = secret;
    } else {
      twoFactorAuth.isGoogleAuthenticatorConfirmed = false;
    }
    
    twoFactorAuth.updatedAt = Date.now();
    return this.repository.save(twoFactorAuth);
  }

  /**
   * Xác nhận cài đặt Google Authenticator
   * @param userId ID của người dùng
   * @returns Cấu hình xác thực hai yếu tố đã cập nhật
   */
  async confirmGoogleAuth(userId: number): Promise<TwoFactorAuth> {
    const twoFactorAuth = await this.findByUserId(userId);
    
    if (!twoFactorAuth) {
      throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy cấu hình xác thực hai yếu tố');
    }
    
    if (!twoFactorAuth.googleAuthenticatorEnabled) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Google Authenticator chưa được bật');
    }
    
    twoFactorAuth.isGoogleAuthenticatorConfirmed = true;
    twoFactorAuth.updatedAt = Date.now();
    return this.repository.save(twoFactorAuth);
  }
}
