import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserRole } from '../entities/user-role.entity';

/**
 * Repository cho UserRole
 */
@Injectable()
export class UserRoleRepository {
  constructor(
    @InjectRepository(UserRole)
    private readonly repository: Repository<UserRole>,
  ) {}

  /**
   * Tìm vai trò theo ID
   * @param id ID của vai trò
   * @returns Vai trò
   */
  async findById(id: number): Promise<UserRole> {
    const role = await this.repository.findOne({
      where: { id },
    });
    if (!role) {
      throw new NotFoundException(`Vai trò với ID "${id}" không tồn tại`);
    }
    return role;
  }

  /**
   * Tìm vai trò theo loại
   * @param type Loại vai trò
   * @returns Vai trò
   */
  async findByType(type: string): Promise<UserRole> {
    const role = await this.repository.findOne({
      where: { type },
    });
    if (!role) {
      throw new NotFoundException(`Vai trò với loại "${type}" không tồn tại`);
    }
    return role;
  }

  /**
   * Lấy tất cả vai trò
   * @returns Danh sách vai trò
   */
  async findAll(): Promise<UserRole[]> {
    return this.repository.find();
  }
}
