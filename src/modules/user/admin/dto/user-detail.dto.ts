import { ApiProperty } from '@nestjs/swagger';
import { GenderEnum, UserTypeEnum } from '@modules/user/enums';

/**
 * DTO cho thông tin chi tiết người dùng
 */
export class UserDetailDto {
  @ApiProperty({
    description: 'ID của người dùng',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Tên đầy đủ của người dùng',
    example: 'Nguyễn Văn A'
  })
  fullName: string;

  @ApiProperty({
    description: 'Email của người dùng',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({
    description: 'Số điện thoại của người dùng',
    example: '**********'
  })
  phoneNumber: string;

  @ApiProperty({
    description: 'Trạng thái tài khoản',
    example: true
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Trạng thái xá<PERSON> thực email',
    example: true
  })
  isVerifyEmail: boolean;

  @ApiProperty({
    description: 'Trạng thái xác thực số điện thoại',
    example: true
  })
  isVerifyPhone: boolean;

  @ApiProperty({
    description: 'Thời gian tạo tài khoản',
    example: 1625097600000
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật thông tin',
    example: 1625097600000
  })
  updatedAt: number;

  @ApiProperty({
    description: 'Địa chỉ',
    example: '123 Đường ABC, Quận XYZ, TP. Hồ Chí Minh'
  })
  address: string;

  @ApiProperty({
    description: 'Mã số thuế',
    example: '**********'
  })
  taxCode: string;

  @ApiProperty({
    description: 'Số dư points hiện tại',
    example: 1000
  })
  pointsBalance: number;

  @ApiProperty({
    description: 'Loại tài khoản',
    enum: UserTypeEnum,
    example: UserTypeEnum.INDIVIDUAL
  })
  type: UserTypeEnum;

  @ApiProperty({
    description: 'Nền tảng đăng ký',
    example: 'web'
  })
  platform: string;

  @ApiProperty({
    description: 'Số CMND/CCCD',
    example: '**********01'
  })
  citizenId: string;

  @ApiProperty({
    description: 'Nơi cấp CMND/CCCD',
    example: 'TP. Hồ Chí Minh'
  })
  citizenIssuePlace: string;

  @ApiProperty({
    description: 'Ngày cấp CMND/CCCD',
    example: '2020-01-01'
  })
  citizenIssueDate: Date;

  @ApiProperty({
    description: 'URL ảnh đại diện',
    example: 'https://example.com/avatar.jpg',
    nullable: true
  })
  avatarUrl: string | null;

  @ApiProperty({
    description: 'Ngày sinh',
    example: '1990-01-01'
  })
  dateOfBirth: Date;

  @ApiProperty({
    description: 'Giới tính',
    enum: GenderEnum,
    example: GenderEnum.MALE
  })
  gender: GenderEnum;

  @ApiProperty({
    description: 'Mã ngân hàng',
    example: 'TCB'
  })
  bankCode: string;

  @ApiProperty({
    description: 'Số tài khoản ngân hàng',
    example: '**********'
  })
  accountNumber: string;

  @ApiProperty({
    description: 'Tên chủ tài khoản ngân hàng',
    example: 'NGUYEN VAN A'
  })
  accountHolder: string;

  @ApiProperty({
    description: 'Chi nhánh ngân hàng',
    example: 'Chi nhánh Quận 1'
  })
  bankBranch: string;

  @ApiProperty({
    description: 'ID tài khoản affiliate (nếu có)',
    example: 1,
    required: false
  })
  affiliateAccountId?: number;
}
