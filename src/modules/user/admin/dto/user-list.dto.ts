import { ApiProperty } from '@nestjs/swagger';
import { UserTypeEnum } from '@modules/user/enums';

/**
 * DTO cho thông tin người dùng trong danh sách
 */
export class UserListItemDto {
  @ApiProperty({
    description: 'ID của người dùng',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'Tên đầy đủ của người dùng',
    example: 'Nguyễn Văn A'
  })
  fullName: string;

  @ApiProperty({
    description: 'Email của người dùng',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({
    description: 'Số điện thoại của người dùng',
    example: '0123456789'
  })
  phoneNumber: string;

  @ApiProperty({
    description: 'Trạng thái tài khoản',
    example: true
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Trạng thái xác thực email',
    example: true
  })
  isVerifyEmail: boolean;

  @ApiProperty({
    description: 'Thời gian tạo tài khoản',
    example: 1625097600000
  })
  createdAt: number;

  @ApiProperty({
    description: 'Số dư points hiện tại',
    example: 1000
  })
  pointsBalance: number;

  @ApiProperty({
    description: 'Loại tài khoản',
    enum: UserTypeEnum,
    example: UserTypeEnum.INDIVIDUAL
  })
  type: UserTypeEnum;

  @ApiProperty({
    description: 'URL ảnh đại diện',
    example: 'https://example.com/avatar.jpg',
    nullable: true
  })
  avatarUrl: string | null;
}
