import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { TokenDecoderRequestDto, TokenDecoderResponseDto } from '../dto/token-decoder.dto';
import { JwtUtilService } from '../../guards/jwt.util';

/**
 * Service xử lý giải mã token
 */
@Injectable()
export class TokenDecoderService {
  private readonly logger = new Logger(TokenDecoderService.name);
  private readonly agentApiKey: string;

  constructor(
    private readonly jwtService: JwtUtilService,
    private readonly configService: ConfigService,
  ) {
    this.agentApiKey = this.configService.get<string>('AGENT_API_KEY') || '';
  }

  /**
   * <PERSON>á<PERSON> thực API key
   * @param apiKey API key từ request
   * @throws UnauthorizedException nếu API key không hợp lệ
   */
  private validateApiKey(apiKey: string): void {
    if (apiKey !== this.agentApiKey) {
      this.logger.warn(`Yêu cầu giải mã token với API key không hợp lệ: ${apiKey}`);
      throw new UnauthorizedException('API key không hợp lệ');
    }
  }

  /**
   * Giải mã token JWT
   * @param dto DTO chứa token cần giải mã và API key
   * @returns Thông tin giải mã từ token
   */
  async decodeToken(dto: TokenDecoderRequestDto): Promise<TokenDecoderResponseDto> {
    // Xác thực API key
    this.validateApiKey(dto.apiKey);

    try {
      // Giải mã token
      const payload = this.jwtService.verifyToken(dto.token);

      // Tính toán thời gian hết hạn
      const now = Math.floor(Date.now() / 1000);
      const expiresAt = payload.exp;
      const expiresIn = expiresAt - now;

      this.logger.log(`Giải mã token thành công cho payload: ${JSON.stringify(payload)}`);

      // Trả về kết quả
      return {
        payload,
        expiresAt,
        expiresIn,
        isValid: expiresIn > 0,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi giải mã token: ${error.message}`);

      // Nếu token không hợp lệ, vẫn giải mã nhưng đánh dấu là không hợp lệ
      try {
        const payload = this.jwtService.decodeToken(dto.token);
        const expiresAt = payload && typeof payload === 'object' ? payload.exp || 0 : 0;
        const now = Math.floor(Date.now() / 1000);
        const expiresIn = expiresAt - now;

        return {
          payload: payload || {},
          expiresAt,
          expiresIn,
          isValid: false,
        };
      } catch (decodeError) {
        this.logger.error(`Không thể giải mã token: ${decodeError.message}`);
        return {
          payload: {},
          expiresAt: 0,
          expiresIn: 0,
          isValid: false,
        };
      }
    }
  }
}
