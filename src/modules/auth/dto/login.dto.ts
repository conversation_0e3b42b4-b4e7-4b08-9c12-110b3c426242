import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  MinLength,
  IsBoolean,
} from 'class-validator';

export class LoginDto {
  @ApiProperty({
    description: 'Email của người dùng',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Email không hợp lệ' })
  @IsNotEmpty({ message: 'Email không được để trống' })
  email: string;

  @ApiProperty({
    description: 'Mật khẩu của người dùng',
    example: 'password123',
  })
  @IsString({ message: 'Mật khẩu phải là chuỗi' })
  @IsNotEmpty({ message: 'Mật khẩu không được để trống' })
  @MinLength(6, { message: 'Mật khẩu phải có ít nhất 6 ký tự' })
  password: string;

  @ApiProperty({
    description: 'Token reCAPTCHA',
    example: '03AGdBq24PBCbwiDRVu...',
  })
  @IsString({ message: 'Token reCAPTCHA phải là chuỗi' })
  @IsOptional({ message: 'Token reCAPTCHA không được để trống' })
  recaptchaToken: string;

  @ApiProperty({
    description: 'Ghi nhớ đăng nhập',
    example: true,
    required: false,
    default: false,
  })
  @IsBoolean({ message: 'Ghi nhớ đăng nhập phải là boolean' })
  @IsOptional()
  rememberMe?: boolean;
}

// Interface cho thông tin permission trong response
export interface PermissionResponse {
  module: string;
  action: string;
  description: string;
}

// Interface cho thông tin user trong response
export interface UserResponse {
  id: number;
  username: string;
  email: string;
  accessToken?: string;
  status: string;
  permissions: string[];
}

export interface VerifyAccountInfo {
  platform: string;
  value: string;
}

// Interface cho login response
export interface LoginResponse {
  accessToken?: string;
  expiresIn?: number; // Giữ lại để tương thích ngược
  expiresAt?: number; // Thời điểm hết hạn (timestamp)
  user?: UserResponse;
  info: VerifyAccountInfo[];
  verifyToken?: string;
}
