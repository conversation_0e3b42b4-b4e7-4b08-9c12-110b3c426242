import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi đăng ký với OTP
 */
export class RegisterOtpResponseDto {
  @ApiProperty({
    description: 'Mã OTP (chỉ hiển thị trong môi trường phát triển)',
    example: '123456',
    required: false,
  })
  otp?: string;

  @ApiProperty({
    description: 'Token OTP để xác thực',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  otpToken: string;

  @ApiProperty({
    description: 'Thời gian hết hạn của OTP (giây)',
    example: 300,
  })
  expiresIn: number;

  @ApiProperty({
    description: 'Số điện thoại đã được gửi OTP (đã được che một phần)',
    example: '091****678',
  })
  maskedPhoneNumber: string;

  @ApiProperty({
    description: 'Email đã được gử<PERSON> (đã được che một phần)',
    example: 'u***@example.com',
    required: false,
  })
  maskedEmail?: string;
}
