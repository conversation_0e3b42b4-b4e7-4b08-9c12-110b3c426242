import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi gửi lại OTP
 */
export class ResendOtpResponseDto {
  @ApiProperty({
    description: 'Token OTP mới',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  otpToken: string;

  @ApiProperty({
    description: 'Thời điểm hết hạn của token OTP (timestamp)',
    example: 1746968772000,
  })
  expiresAt: number;

  @ApiProperty({
    description: 'Email đã được che một phần',
    example: 'j***<EMAIL>',
  })
  maskedEmail: string;

  @ApiProperty({
    description: 'Mã OTP (chỉ trả về trong môi trường phát triển)',
    example: '123456',
    required: false,
  })
  otp?: string;
}
