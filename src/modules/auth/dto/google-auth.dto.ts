import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho yêu cầu đăng nhập bằng Google OAuth2
 */
export class GoogleAuthDto {
  @ApiProperty({
    description: 'Authorization code từ Google OAuth2',
    example: '4/0AY0e-g6_DUip4j8KhO...',
  })
  @IsNotEmpty({ message: 'Authorization code không được để trống' })
  @IsString({ message: 'Authorization code phải là chuỗi' })
  code: string;

  @ApiProperty({
    description: 'URL chuyển hướng sau khi xác thực (tùy chọn)',
    example: 'https://example.com/callback',
    required: false,
  })
  @IsString({ message: 'Redirect URI phải là chuỗi' })
  redirectUri?: string;
}
