# Auth Module Tests

This directory contains tests for the authentication module, including unit tests for services and controllers, as well as integration tests.

## Test Files

### Unit Tests

- `auth.controller.spec.ts`: Tests for the AuthController
- `auth.service.spec.ts`: Tests for the AuthService
- `jwt.util.spec.ts`: Tests for the JwtUtilService

### End-to-End Tests

- `auth.e2e-spec.ts`: End-to-end tests for the auth API endpoints

## Test Coverage

The tests cover the following functionality:

1. **User Authentication**
   - Login with email and password
   - Handling unverified users
   - Refresh token generation and cookie setting

2. **User Registration**
   - Registration with email, phone, and password
   - OTP generation and verification
   - Account verification

3. **Password Management**
   - Forgot password flow
   - OTP verification for password reset
   - Password reset

4. **JWT Token Management**
   - Token generation for different purposes (access, refresh, verification)
   - Token verification
   - Token decoding

## Running Tests

### Running Unit Tests

To run all unit tests for the auth module:

```bash
npm run test -- src/modules/auth
```

To run a specific test file:

```bash
npm run test -- src/modules/auth/controller/auth.controller.spec.ts
```

### Running End-to-End Tests

To run the end-to-end tests for the auth API:

```bash
npm run test:e2e -- test/auth.e2e-spec.ts
```

### Running Tests in Watch Mode

To run tests in watch mode (tests will automatically re-run when files change):

```bash
npm run test:watch -- src/modules/auth
```

### Running All Tests with Coverage

To generate a test coverage report:

```bash
npm run test:cov
```

## Test Environment Setup

The unit tests use Jest's mocking capabilities to mock dependencies, while the end-to-end tests require:

1. A running PostgreSQL database (configured in the test environment)
2. A running Redis instance (for token storage)

The e2e tests will:
- Create temporary test users
- Generate and verify OTPs
- Test the complete authentication flow
- Clean up after themselves

## Writing Additional Tests

When adding new features to the auth module, please add corresponding tests following these patterns:

1. **Unit Tests**: Test individual components in isolation, mocking dependencies
2. **Integration Tests**: Test how components work together
3. **E2E Tests**: Test the complete flow from API endpoints to database

## Troubleshooting

If you encounter issues with the tests:

1. Ensure your test database is running and accessible
2. Check that Redis is running and accessible
3. Verify that the test environment variables are correctly set
4. For e2e tests, ensure the application can start properly in test mode
