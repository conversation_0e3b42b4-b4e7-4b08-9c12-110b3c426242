import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { LoginDto } from '../../dto/login.dto';

describe('LoginDto', () => {
  it('phải xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(LoginDto, {
      email: '<EMAIL>',
      password: 'password123',
      recaptchaToken: '03AGdBq24PBCbwiDRVu...',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải xác thực DTO hợp lệ khi không có recaptchaToken', async () => {
    // Arrange
    const dto = plainToInstance(LoginDto, {
      email: '<EMAIL>',
      password: 'password123',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('phải báo lỗi khi email bị thiếu', async () => {
    // Arrange
    const dto = plainToInstance(LoginDto, {
      password: 'password123',
      recaptchaToken: '03AGdBq24PBCbwiDRVu...',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('phải báo lỗi khi email không đúng định dạng', async () => {
    // Arrange
    const dto = plainToInstance(LoginDto, {
      email: 'invalid-email',
      password: 'password123',
      recaptchaToken: '03AGdBq24PBCbwiDRVu...',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isEmail');
  });

  it('phải báo lỗi khi password bị thiếu', async () => {
    // Arrange
    const dto = plainToInstance(LoginDto, {
      email: '<EMAIL>',
      recaptchaToken: '03AGdBq24PBCbwiDRVu...',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('phải báo lỗi khi password ngắn hơn 6 ký tự', async () => {
    // Arrange
    const dto = plainToInstance(LoginDto, {
      email: '<EMAIL>',
      password: '12345',
      recaptchaToken: '03AGdBq24PBCbwiDRVu...',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('minLength');
  });

  it('phải báo lỗi khi recaptchaToken không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(LoginDto, {
      email: '<EMAIL>',
      password: 'password123',
      recaptchaToken: 12345,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });
});
