# Tài liệu về các luồng xác thực (Authentication)

Tài liệu này mô tả chi tiết các luồng xác thực được triển khai trong hệ thống, bao gồ<PERSON> đă<PERSON>, <PERSON><PERSON><PERSON>, l<PERSON><PERSON> mới token và xác thực OTP.

## 1. <PERSON>ồng đăng nhập (Login)

### M<PERSON> tả

Luồng đăng nhập cho phép người dùng xác thực và nhận JWT token để truy cập các tài nguyên được bảo vệ trong hệ thống.

### Các bước

1. Người dùng gửi thông tin đăng nhập (email và mật khẩu) đến API `/auth/login`
2. <PERSON>ệ thống kiểm tra thông tin đăng nhập
3. <PERSON><PERSON><PERSON> thông tin hợp lệ, hệ thống kiểm tra xem người dùng đã xác thực email hoặc số điện thoại chưa
   - <PERSON><PERSON><PERSON> chưa x<PERSON> thực, hệ thống trả về token xác thực và các phương thức xác thực có sẵn
   - Nếu đã xác thực, hệ thống tiếp tục bước 4
4. Hệ thống tạo access token và refresh token
5. Hệ thống lưu refresh token vào Redis với key là user ID
6. Hệ thống trả về access token và thông tin người dùng trong phần thân (body) của phản hồi
7. Hệ thống đặt refresh token vào cookie HTTP-only

### Phản hồi

- **Thành công (200 OK)**: Trả về access token và thông tin người dùng
- **Chưa xác thực (202 Accepted)**: Trả về token xác thực và các phương thức xác thực có sẵn
- **Thất bại (401 Unauthorized)**: Thông tin đăng nhập không chính xác

## 2. Luồng đăng ký (Register)

### Mô tả

Luồng đăng ký cho phép người dùng tạo tài khoản mới trong hệ thống. Luồng này bao gồm hai bước: đăng ký và xác thực OTP.

### Các bước

#### Bước 1: Đăng ký

1. Người dùng gửi thông tin đăng ký (email, số điện thoại, mật khẩu, họ tên) đến API `/auth/register`
2. Hệ thống kiểm tra email đã tồn tại chưa
3. Hệ thống tạo OTP 6 số và token OTP
4. Hệ thống lưu thông tin đăng ký và OTP vào Redis
5. Hệ thống gửi email chứa OTP cho người dùng (phần này để trống theo yêu cầu)
6. Hệ thống trả về token OTP và thông tin OTP cho người dùng

#### Bước 2: Xác thực OTP

1. Người dùng nhập OTP và gửi lên API `/auth/verify-otp` cùng với token OTP
2. Hệ thống kiểm tra OTP và token OTP
3. Nếu OTP chính xác, hệ thống tạo tài khoản mới với trạng thái đã xác thực email và số điện thoại
4. Hệ thống xóa thông tin đăng ký khỏi Redis
5. Hệ thống tạo access token và refresh token
6. Hệ thống lưu refresh token vào Redis với key là user ID
7. Hệ thống trả về access token và thông tin người dùng trong phần thân (body) của phản hồi
8. Hệ thống đặt refresh token vào cookie HTTP-only

### Phản hồi

#### Đăng ký

- **Thành công (200 OK)**: Trả về token OTP và thông tin OTP
- **Thất bại (400 Bad Request)**: Dữ liệu đầu vào không hợp lệ
- **Thất bại (409 Conflict)**: Email hoặc số điện thoại đã được sử dụng

#### Xác thực OTP

- **Thành công (200 OK)**: Trả về access token và thông tin người dùng
- **Thất bại (400 Bad Request)**: Dữ liệu đầu vào không hợp lệ
- **Thất bại (401 Unauthorized)**: OTP không chính xác hoặc token OTP không hợp lệ

## 3. Luồng làm mới token (Refresh Token)

### Mô tả

Luồng làm mới token cho phép người dùng lấy access token mới khi access token hiện tại hết hạn mà không cần đăng nhập lại.

### Các bước

1. Người dùng gửi yêu cầu đến API `/auth/refresh-token`
2. Hệ thống lấy refresh token từ cookie
3. Hệ thống xác thực refresh token
4. Hệ thống kiểm tra xem refresh token có tồn tại trong Redis không
5. Nếu refresh token hợp lệ, hệ thống tạo access token mới
6. Hệ thống trả về access token mới

### Phản hồi

- **Thành công (200 OK)**: Trả về access token mới
- **Thất bại (401 Unauthorized)**: Refresh token không hợp lệ hoặc đã hết hạn

## 4. Bảo mật và xử lý token

### Access Token

- **Lưu trữ**: Access token được lưu trữ ở phía client (trong bộ nhớ của trình duyệt hoặc local storage)
- **Thời gian hết hạn**: Ngắn (thường là 15-60 phút)
- **Sử dụng**: Được gửi trong header Authorization của các yêu cầu API

### Refresh Token

- **Lưu trữ**: Refresh token được lưu trữ trong cookie HTTP-only ở phía client và trong Redis ở phía server
- **Thời gian hết hạn**: Dài (thường là 7-30 ngày)
- **Bảo mật**: Cookie được thiết lập với các tùy chọn bảo mật:
  - `httpOnly`: Không cho phép JavaScript truy cập cookie
  - `secure`: Chỉ gửi cookie qua HTTPS
  - `sameSite`: Chỉ gửi cookie cho cùng site
  - `path`: Cookie có hiệu lực trên toàn bộ domain

## 5. Xử lý OTP

### Tạo OTP

- OTP được tạo ngẫu nhiên với 6 chữ số
- OTP có thời gian hết hạn (thường là 5-15 phút)

### Lưu trữ OTP

- OTP và thông tin đăng ký được lưu trữ trong Redis
- Key Redis có dạng `registration:{otpToken}`
- Giá trị lưu trữ là JSON chứa thông tin đăng ký và OTP

### Bảo mật

- Trong môi trường phát triển, OTP được trả về trong phản hồi API để tiện test
- Trong môi trường sản xuất, OTP không được trả về mà chỉ được gửi qua email hoặc SMS
- Số điện thoại và email được che một phần khi trả về cho người dùng

## 6. Các API xác thực

### Đăng nhập

```
POST /auth/login
```

### Đăng ký

```
POST /auth/register
```

### Xác thực OTP

```
POST /auth/verify-otp
```

### Làm mới token

```
POST /auth/refresh-token
```

## 7. Lưu ý về bảo mật

- Tất cả các mật khẩu đều được mã hóa bằng bcrypt trước khi lưu vào cơ sở dữ liệu
- Refresh token được lưu trữ trong cookie HTTP-only để ngăn chặn tấn công XSS
- Cookie được thiết lập với `secure=true` để chỉ gửi qua HTTPS
- Cookie được thiết lập với `sameSite=strict` để ngăn chặn tấn công CSRF
- Refresh token được lưu trữ trong Redis để có thể vô hiệu hóa khi cần thiết
