import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString } from 'class-validator';

/**
 * DTO cho giá của phân loại sản phẩm
 */
export class ClassificationPriceDto {
  @ApiProperty({
    description: 'Giá trị',
    example: 35990000,
  })
  @IsNumber()
  value: number;
  
  @ApiProperty({
    description: 'Đơn vị tiền tệ',
    example: 'VND',
  })
  @IsString()
  currency: string;
}
