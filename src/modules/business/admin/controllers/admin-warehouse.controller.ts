import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { ADMIN_WAREHOUSE_ERROR_CODES } from '../exceptions/warehouse.exception';
import { AdminWarehouseService } from '@modules/business/admin/services';
import { QueryWarehouseDto } from '../dto/warehouse/query-warehouse.dto';
import { WarehouseResponseDto } from '@modules/business/admin/dto/warehouse';
import { WarehouseDetailResponseDto } from '@modules/business/admin/dto/warehouse';
import { SWAGGER_API_TAGS } from '@common/swagger';

/**
 * Controller xử lý API liên quan đến kho cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_BUSINESS_WAREHOUSE)
@ApiExtraModels(
  ApiResponseDto,
  WarehouseResponseDto,
  WarehouseDetailResponseDto,
  PaginatedResult,
  ApiErrorResponseDto
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/warehouses')
export class AdminWarehouseController {
  constructor(private readonly adminWarehouseService: AdminWarehouseService) {}

  /**
   * Lấy danh sách kho với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách kho với phân trang' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách kho',
    schema: ApiResponseDto.getPaginatedSchema(WarehouseResponseDto)
  })
  @ApiErrorResponse(
    ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_FIND_FAILED
  )
  async findAll(
    @Query() queryDto: QueryWarehouseDto
  ): Promise<ApiResponseDto<PaginatedResult<WarehouseResponseDto>>> {
    const result = await this.adminWarehouseService.findAll(queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách kho thành công');
  }

  /**
   * Lấy thông tin chi tiết kho theo ID
   */
  @Get(':warehouseId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết kho theo ID' })
  @ApiParam({ name: 'warehouseId', description: 'ID của kho', example: 1 })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết kho',
    schema: ApiResponseDto.getSchema(WarehouseDetailResponseDto)
  })
  @ApiErrorResponse(
    ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_NOT_FOUND,
    ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_FIND_FAILED
  )
  async findOne(
    @Param('warehouseId') warehouseId: number
  ): Promise<ApiResponseDto<WarehouseDetailResponseDto>> {
    const result = await this.adminWarehouseService.findOne(warehouseId);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết kho thành công');
  }
}
