import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { ADMIN_WAREHOUSE_ERROR_CODES } from '../exceptions/warehouse.exception';
import {
  WarehouseRepository,
  WarehouseCustomFieldRepository,
  CustomFieldRepository
} from '@modules/business/repositories';
import {
  QueryWarehouseCustomFieldDto,
  WarehouseCustomFieldResponseDto,
  WarehouseCustomFieldDetailResponseDto,
  FieldDetailsDto
} from '../dto/warehouse';
import { PaginatedResult } from '@common/response';
import { plainToInstance } from 'class-transformer';
import { WarehouseValidationHelper } from '@modules/business/admin/helpers';

/**
 * Service xử lý logic nghiệp vụ cho trường tùy chỉnh của kho
 */
@Injectable()
export class AdminWarehouseCustomFieldService {
  private readonly logger = new Logger(AdminWarehouseCustomFieldService.name);

  constructor(
    private readonly warehouseRepository: WarehouseRepository,
    private readonly warehouseCustomFieldRepository: WarehouseCustomFieldRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly warehouseValidationHelper: WarehouseValidationHelper
  ) {}

  /**
   * Lấy danh sách trường tùy chỉnh của kho với phân trang
   * @param queryDto DTO truy vấn
   * @returns Danh sách trường tùy chỉnh của kho với phân trang
   */
  async findAll(queryDto: QueryWarehouseCustomFieldDto): Promise<PaginatedResult<WarehouseCustomFieldResponseDto>> {
    this.logger.log(`Lấy danh sách trường tùy chỉnh của kho với phân trang: ${JSON.stringify(queryDto)}`);

    try {
      // Lấy danh sách trường tùy chỉnh từ repository
      const [customFields, total] = await this.warehouseCustomFieldRepository.findAllWithPagination(queryDto);

      // Chuyển đổi dữ liệu thành DTO sử dụng constructor
      const items = customFields.map(cf => new WarehouseCustomFieldResponseDto(cf));

      // Trả về kết quả với phân trang
      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page
        }
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách trường tùy chỉnh của kho: ${error.message}`, error.stack);
      throw new AppException(
        ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_FIND_FAILED,
        'Lỗi khi lấy danh sách trường tùy chỉnh của kho'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết trường tùy chỉnh của kho theo ID kho và ID trường
   * @param warehouseId ID của kho
   * @param fieldId ID của trường tùy chỉnh
   * @returns Thông tin chi tiết trường tùy chỉnh của kho
   */
  async findOne(warehouseId: number, fieldId: number): Promise<WarehouseCustomFieldDetailResponseDto> {
    this.logger.log(`Lấy thông tin chi tiết trường tùy chỉnh của kho với warehouseId: ${warehouseId} và fieldId: ${fieldId}`);

    // Kiểm tra kho có tồn tại không
    const warehouse = await this.warehouseRepository.findByWarehouseId_admin(warehouseId);
    this.warehouseValidationHelper.validateWarehouseExists(warehouse);

    // Lấy thông tin chi tiết trường tùy chỉnh của kho
    const customFieldDetail = await this.warehouseCustomFieldRepository.findDetailByWarehouseIdAndFieldId(warehouseId, fieldId);

    // Kiểm tra trường tùy chỉnh có tồn tại không
    if (!customFieldDetail || customFieldDetail.length === 0) {
      throw new AppException(
        ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_NOT_FOUND,
        'Không tìm thấy trường tùy chỉnh của kho'
      );
    }

    try {
      // Kiểm tra xem customFieldDetail có phải là mảng không và có phần tử nào không
      if (!customFieldDetail || !customFieldDetail.fieldLabel) {
        this.logger.error(`Không tìm thấy thông tin chi tiết của trường tùy chỉnh với warehouseId=${warehouseId} và fieldId=${fieldId}`);
        throw new AppException(
          ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_NOT_FOUND,
          'Không tìm thấy thông tin chi tiết của trường tùy chỉnh'
        );
      }

      // Lấy thông tin chi tiết từ kết quả truy vấn
      const detail = customFieldDetail;

      // Khởi tạo đối tượng FieldDetailsDto
      const fieldDetails = new FieldDetailsDto({
        label: detail.fieldLabel,
        type: detail.type,
        required: detail.required,
        configJson: detail.configJson
      });

      // Khởi tạo đối tượng WarehouseCustomFieldDetailResponseDto
      const response = new WarehouseCustomFieldDetailResponseDto({
        warehouseId: detail.warehouseId,
        fieldId: detail.fieldId,
        value: detail.value,
        warehouseName: detail.warehouseName,
        fieldLabel: detail.fieldLabel,
        fieldDetails: fieldDetails
      });

      return response;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin chi tiết trường tùy chỉnh của kho: ${error.message}`, error.stack);
      throw new AppException(
        ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_FIND_FAILED,
        'Lỗi khi lấy thông tin chi tiết trường tùy chỉnh của kho'
      );
    }
  }
}
