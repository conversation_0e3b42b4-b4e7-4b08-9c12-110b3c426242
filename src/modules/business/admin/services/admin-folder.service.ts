import { Injectable, Logger } from '@nestjs/common';
import { FileRepository, FolderRepository } from '@modules/business/repositories';
import { FolderQueryDto, FolderResponseDto, FolderDetailResponseDto } from '../dto/folder';
import { FileResponseDto } from '../dto/file';
import { PaginatedResult } from '@common/response/api-response-dto';
import { AppException } from '@common/exceptions';
import { FOLDER_ERROR_CODES } from '../exceptions/folder.exception';
import { plainToInstance } from 'class-transformer';
import { FolderValidationHelper } from '../helpers/folder-validation.helper';
import { FolderHelper } from '../helpers/folder-helper';
import { Folder } from '@modules/business/entities/folder.entity';

/**
 * Service xử lý các thao tác liên quan đến folder cho admin
 */
@Injectable()
export class AdminFolderService {
  private readonly logger = new Logger(AdminFolderService.name);

  constructor(
    private readonly folderRepository: FolderRepository,
    private readonly fileRepository: FileRepository,
    private readonly folderValidationHelper: FolderValidationHelper,
    private readonly folderHelper: FolderHelper,
  ) {}

  /**
   * Lấy danh sách folder với phân trang và tìm kiếm
   * @param queryDto Tham số truy vấn
   * @returns Danh sách folder với phân trang
   */
  async findAll(queryDto: FolderQueryDto): Promise<PaginatedResult<FolderResponseDto>> {
    try {
      this.logger.log(`Lấy danh sách folder với tham số: ${JSON.stringify(queryDto)}`);

      const { page, limit, search, parentId, userId, sortBy, sortDirection } = queryDto;

      // Lấy danh sách folder từ repository
      const result = await this.folderRepository.findAll_admin(
        page,
        limit,
        search,
        parentId,
        userId,
        sortBy,
        sortDirection,
      );

      // Chuyển đổi sang DTO
      const items = result.items.map(folder => plainToInstance(FolderResponseDto, folder, { excludeExtraneousValues: true }));

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách folder: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`method: ${this.findAll.name}`);
      throw new AppException(
        FOLDER_ERROR_CODES.FOLDER_FIND_FAILED,
        'Lỗi khi lấy danh sách folder',
      );
    }
  }

  /**
   * Lấy chi tiết folder theo ID
   * @param id ID của folder
   * @returns Chi tiết folder
   */
  async findById(id: number): Promise<FolderDetailResponseDto> {
    try {
      this.logger.log(`Lấy chi tiết folder với ID: ${id}`);

      // Lấy chi tiết folder từ repository
      const folder = await this.folderRepository.findById_admin(id);

      // Kiểm tra folder tồn tại
      this.folderValidationHelper.validateFolderExists(folder);

      // Lấy thông tin thư mục cha (nếu có)
      let parentFolder: Folder | null = null;
      if (folder.parentId) {
        parentFolder = await this.folderRepository.findById_admin(folder.parentId);
      }

      // Lấy danh sách file trong thư mục
      const filesResult = await this.fileRepository.findAllAdmin({page: 1, limit: 100, folderId: folder.id});
      const files = filesResult.items.map(file => plainToInstance(FileResponseDto, file, { excludeExtraneousValues: true }));

      // Tạo đối tượng chi tiết thư mục
      const folderDetail = {
        id: folder.id,
        name: folder.name,
        parent: parentFolder ? {
          id: parentFolder.id,
          name: parentFolder.name,
          path: parentFolder.path,
        } : null,
        owner: {
          id: folder.userId,
          // Tạm thời chưa có thông tin chi tiết về người dùng
          name: `User ${folder.userId}`,
        },
        path: folder.path,
        root: folder.root,
        fileCount: filesResult.items.length,
        files: files,
        createdAt: folder.createdAt,
        formattedCreatedAt: this.folderHelper.formatTimestamp(folder.createdAt),
        updatedAt: folder.updatedAt,
        formattedUpdatedAt: this.folderHelper.formatTimestamp(folder.updatedAt),
      };

      // Chuyển đổi sang DTO
      return plainToInstance(FolderDetailResponseDto, folderDetail, { excludeExtraneousValues: true });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết folder: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`method: ${this.findById.name}`);
      throw new AppException(
        FOLDER_ERROR_CODES.FOLDER_DETAIL_FAILED,
        'Lỗi khi lấy chi tiết folder',
      );
    }
  }
}
