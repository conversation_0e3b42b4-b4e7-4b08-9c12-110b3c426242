import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { QueryPhysicalWarehouseDto } from '../../dto/warehouse/query-physical-warehouse.dto';

describe('QueryPhysicalWarehouseDto', () => {
  it('nên chuyển đổi plain object thành instance của QueryPhysicalWarehouseDto', () => {
    // Arrange
    const plainObject = {
      page: 1,
      limit: 10,
      address: 'Hà Nội',
      minCapacity: 1000,
      maxCapacity: 5000,
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(QueryPhysicalWarehouseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(QueryPhysicalWarehouseDto);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.address).toBe('<PERSON><PERSON>');
    expect(dto.minCapacity).toBe(1000);
    expect(dto.maxCapacity).toBe(5000);
    // plainToInstance không loại bỏ các trường thừa mặc định, chỉ loại bỏ khi sử dụng excludeExtraneousValues
  });

  it('nên chuyển đổi plain object với các trường thiếu thành instance của QueryPhysicalWarehouseDto', () => {
    // Arrange
    const plainObject = {
      page: 1,
      limit: 10
    };

    // Act
    const dto = plainToInstance(QueryPhysicalWarehouseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(QueryPhysicalWarehouseDto);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.address).toBeUndefined();
    expect(dto.minCapacity).toBeUndefined();
    expect(dto.maxCapacity).toBeUndefined();
  });

  it('nên validate thành công với dữ liệu hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryPhysicalWarehouseDto, {
      page: 1,
      limit: 10,
      address: 'Hà Nội',
      minCapacity: 1000,
      maxCapacity: 5000
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên validate thất bại với minCapacity âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryPhysicalWarehouseDto, {
      page: 1,
      limit: 10,
      address: 'Hà Nội',
      minCapacity: -1000,
      maxCapacity: 5000
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('minCapacity');
    expect(errors[0].constraints).toHaveProperty('min');
  });

  it('nên validate thất bại với maxCapacity âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryPhysicalWarehouseDto, {
      page: 1,
      limit: 10,
      address: 'Hà Nội',
      minCapacity: 1000,
      maxCapacity: -5000
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('maxCapacity');
    expect(errors[0].constraints).toHaveProperty('min');
  });

  it('nên validate thất bại với address không phải là string', async () => {
    // Arrange
    const dto = plainToInstance(QueryPhysicalWarehouseDto, {
      page: 1,
      limit: 10,
      address: 123,
      minCapacity: 1000,
      maxCapacity: 5000
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('address');
    expect(errors[0].constraints).toHaveProperty('isString');
  });

  it('nên validate thất bại với minCapacity không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryPhysicalWarehouseDto, {
      page: 1,
      limit: 10,
      address: 'Hà Nội',
      minCapacity: 'not-a-number',
      maxCapacity: 5000
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('minCapacity');
    expect(errors[0].constraints).toHaveProperty('isNumber');
  });
});
