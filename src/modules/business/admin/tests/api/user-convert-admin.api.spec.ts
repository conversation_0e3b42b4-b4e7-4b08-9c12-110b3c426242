import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { JwtEmployeeGuard } from '../../../../auth/guards';
import { PermissionsGuard } from '../../../../auth/guards/permissions.guard';
import { UserConvertAdminService } from '../../services';
import { PaginatedResult } from '../../../../../common/response';
import { UserConvertResponseDto, UserConvertDetailResponseDto } from '../../dto';

describe('UserConvertAdminController (e2e)', () => {
  let app: INestApplication;
  let userConvertAdminService: UserConvertAdminService;

  const mockUserConvertAdminService = {
    getUserConverts: jest.fn() as jest.Mock,
    getUserConvertById: jest.fn() as jest.Mock,
  };

  const mockJwtEmployeeGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  const mockPermissionsGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [],
      providers: [
        {
          provide: UserConvertAdminService,
          useValue: mockUserConvertAdminService
        }
      ]
    })
      .overrideGuard(JwtEmployeeGuard)
      .useValue(mockJwtEmployeeGuard)
      .overrideGuard(PermissionsGuard)
      .useValue(mockPermissionsGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    userConvertAdminService = moduleFixture.get<UserConvertAdminService>(UserConvertAdminService);

    // Thêm middleware giả lập request.employee
    app.use((req, res, next) => {
      req.employee = { id: 1, email: '<EMAIL>', role: 'admin' };
      next();
    });

    // Thiết lập mock routes
    app.use('/admin/business/user-converts/:id', (req, res, next) => {
      if (req.method === 'GET' && req.params.id) {
        const id = parseInt(req.params.id, 10);
        mockUserConvertAdminService.getUserConvertById(req.employee.id, id)
          .then((result: UserConvertDetailResponseDto) => {
            res.status(200).json({
              code: 200,
              message: 'Lấy chi tiết bản ghi chuyển đổi khách hàng thành công',
              result
            });
          })
          .catch((err: any) => {
            res.status(500).json({
              code: 500,
              message: err.message,
              errors: err.errors
            });
          });
      } else {
        next();
      }
    });

    app.use('/admin/business/user-converts', (req, res, next) => {
      if (req.method === 'GET') {
        const queryParams = req.query;
        mockUserConvertAdminService.getUserConverts(req.employee.id, queryParams)
          .then((result: PaginatedResult<UserConvertResponseDto>) => {
            res.status(200).json({
              code: 200,
              message: 'Lấy danh sách bản ghi chuyển đổi khách hàng thành công',
              result
            });
          })
          .catch((err: any) => {
            res.status(500).json({
              code: 500,
              message: err.message,
              errors: err.errors
            });
          });
      } else {
        next();
      }
    });

    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('GET /admin/business/user-converts', () => {
    it('nên trả về danh sách bản ghi chuyển đổi khách hàng phân trang', async () => {
      // Arrange
      const mockConverts: UserConvertResponseDto[] = [
        {
          id: 1,
          convertCustomerId: 2,
          userId: 3,
          conversionType: 'online',
          source: 'website',
          notes: 'Khách hàng quan tâm đến sản phẩm X',
          content: { additionalInfo: 'Thông tin thêm về khách hàng' },
          createdAt: 1625097600000,
          updatedAt: 1625184000000,
        },
      ];

      const mockPaginatedResult: PaginatedResult<UserConvertResponseDto> = {
        items: mockConverts,
        meta: {
          currentPage: 1,
          itemsPerPage: 10,
          itemCount: 1,
          totalItems: 1,
          totalPages: 1,
        },
      };

      mockUserConvertAdminService.getUserConverts.mockResolvedValue(mockPaginatedResult);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/business/user-converts')
        .query({
          page: 2,
          limit: 5,
          search: 'test',
          convertCustomerId: 2,
          userId: 3,
          conversionType: 'online',
        })
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Lấy danh sách bản ghi chuyển đổi khách hàng thành công');
          expect(res.body.result.items).toHaveLength(1);
          expect(res.body.result.items[0].id).toBe(1);
          expect(res.body.result.items[0].conversionType).toBe('online');

          const getUserConvertsMock = userConvertAdminService.getUserConverts as jest.Mock;
          const lastCall = getUserConvertsMock.mock.calls[getUserConvertsMock.mock.calls.length - 1];
          expect(lastCall[0]).toEqual(expect.any(Number));
          expect(lastCall[1]).toEqual(expect.objectContaining({
            page: '2', // Query params are strings
            limit: '5', // Query params are strings
            search: 'test',
            convertCustomerId: '2', // Query params are strings
            userId: '3', // Query params are strings
            conversionType: 'online',
          }));
        });
    });
  });

  describe('GET /admin/business/user-converts/:id', () => {
    it('nên trả về thông tin chi tiết bản ghi chuyển đổi khách hàng theo ID', async () => {
      // Arrange
      const mockConvertDetail: UserConvertDetailResponseDto = {
        id: 1,
        convertCustomerId: 2,
        userId: 3,
        conversionType: 'online',
        source: 'website',
        notes: 'Khách hàng quan tâm đến sản phẩm X',
        content: { additionalInfo: 'Thông tin thêm về khách hàng' },
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
        customer: {
          id: 2,
          avatar: 'avatars/customer-123.jpg',
          name: 'Nguyễn Văn A',
          email: { primary: '<EMAIL>' },
          phone: '0912345678',
          platform: 'Facebook',
          timezone: 'Asia/Ho_Chi_Minh',
          createdAt: 1625097600000,
          updatedAt: 1625097600000,
          userId: 3,
          agentId: '550e8400-e29b-41d4-a716-446655440000',
          metadata: []
        }
      };

      mockUserConvertAdminService.getUserConvertById.mockResolvedValue(mockConvertDetail);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/business/user-converts/1')
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Lấy chi tiết bản ghi chuyển đổi khách hàng thành công');
          expect(res.body.result.id).toBe(1);
          expect(res.body.result.conversionType).toBe('online');
          expect(res.body.result.customer).toBeDefined();
          expect(res.body.result.customer.id).toBe(2);
          expect(res.body.result.customer.name).toBe('Nguyễn Văn A');
        });
    });

    it('nên truyền ID bản ghi chuyển đổi đúng cho service', async () => {
      // Arrange
      const convertId = 123;
      const mockConvertDetail: UserConvertDetailResponseDto = {
        id: convertId,
        convertCustomerId: 2,
        userId: 3,
        conversionType: 'online',
        source: 'website',
        notes: 'Khách hàng quan tâm đến sản phẩm X',
        content: { additionalInfo: 'Thông tin thêm về khách hàng' },
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
        customer: null
      };

      mockUserConvertAdminService.getUserConvertById.mockResolvedValue(mockConvertDetail);

      // Act & Assert
      return request(app.getHttpServer())
        .get(`/admin/business/user-converts/${convertId}`)
        .expect(200)
        .expect(() => {
          const getUserConvertByIdMock = userConvertAdminService.getUserConvertById as jest.Mock;
          expect(getUserConvertByIdMock).toHaveBeenCalledWith(expect.any(Number), convertId);
        });
    });
  });
});
