import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_converts trong cơ sở dữ liệu
 * Lưu lại nội dung chuyển đổi khách hàng
 */
@Entity('user_converts')
export class UserConvert {
  /**
   * ID bản ghi chuyển đổi
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * <PERSON>h<PERSON>ch hàng được chuyển đổi
   */
  @Column({ name: 'convert_customer_id', type: 'bigint', nullable: true, comment: '<PERSON>hách hàng được chuyển đổi' })
  convertCustomerId: number;

  /**
   * Người dùng thực hiện chuyển đổi
   */
  @Column({ name: 'user_id', nullable: true, comment: 'Người dùng thực hiện chuyển đổi' })
  userId: number;

  /**
   * <PERSON><PERSON><PERSON> chuyển đổi (ví dụ: online, offline, referral)
   */
  @Column({ name: 'conversion_type', length: 50, nullable: true, comment: '<PERSON><PERSON><PERSON> chuyển đổi (ví dụ: online, offline, referral)' })
  conversionType: string;

  /**
   * Nguồn gốc chuyển đổi (ví dụ: website, social media, event)
   */
  @Column({ name: 'source', length: 100, nullable: true, comment: 'Nguồn gốc chuyển đổi (ví dụ: website, social media, event)' })
  source: string;

  /**
   * Ghi chú thêm về chuyển đổi
   */
  @Column({ name: 'notes', type: 'text', nullable: true, comment: 'Ghi chú thêm về chuyển đổi' })
  notes: string;

  /**
   * Thông tin bổ sung (JSON)
   */
  @Column({ name: 'content', type: 'jsonb', nullable: true, comment: 'Thông tin bổ sung (JSON)' })
  content: Record<string, unknown>;

  /**
   * Thời gian tạo
   */
  @Column({ 
    name: 'created_at', 
    type: 'bigint', 
    nullable: false, 
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint", 
    comment: 'Thời gian tạo' 
  })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @Column({ 
    name: 'updated_at', 
    type: 'bigint', 
    nullable: false, 
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint", 
    comment: 'Thời gian cập nhật' 
  })
  updatedAt: number;
}
