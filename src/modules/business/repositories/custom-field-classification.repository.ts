import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { CustomFieldClassification } from '@modules/business/entities';

/**
 * Repository xử lý truy vấn dữ liệu cho entity CustomFieldClassification
 */
@Injectable()
export class CustomFieldClassificationRepository extends Repository<CustomFieldClassification> {
  protected readonly logger = new Logger(CustomFieldClassificationRepository.name);

  constructor(private dataSource: DataSource) {
    super(CustomFieldClassification, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho CustomFieldClassification
   * @returns SelectQueryBuilder<CustomFieldClassification>
   */
  protected createBaseQuery(): SelectQueryBuilder<CustomFieldClassification> {
    return this.createQueryBuilder('customFieldClassification');
  }

  /**
   * Tìm giá trị trường tùy chỉnh theo ID phân loại
   * @param classificationId ID của phân loại
   * @returns Danh sách giá trị trường tùy chỉnh
   */
  async findByClassificationId(classificationId: number): Promise<CustomFieldClassification[]> {
    this.logger.log(`Tìm giá trị trường tùy chỉnh với classificationId: ${classificationId}`);
    try {
      const customFieldValues = await this.find({
        where: { classificationId }
      });

      this.logger.log(`Đã tìm thấy ${customFieldValues.length} giá trị trường tùy chỉnh cho phân loại ${classificationId}`);
      return customFieldValues;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm giá trị trường tùy chỉnh theo classificationId ${classificationId}: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm giá trị trường tùy chỉnh theo classificationId ${classificationId}: ${error.message}`);
    }
  }

  /**
   * Tìm giá trị trường tùy chỉnh theo ID phân loại và ID trường tùy chỉnh
   * @param classificationId ID của phân loại
   * @param customFieldId ID của trường tùy chỉnh
   * @returns Giá trị trường tùy chỉnh hoặc null nếu không tìm thấy
   */
  async findByClassificationIdAndCustomFieldId(
    classificationId: number,
    customFieldId: number,
  ): Promise<CustomFieldClassification | null> {
    try {
      return await this.findOne({
        where: { classificationId, customFieldId },
      });
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm giá trị trường tùy chỉnh theo classificationId ${classificationId} và customFieldId ${customFieldId}: ${error.message}`,
      );
      throw new Error(`Lỗi khi tìm giá trị trường tùy chỉnh theo classificationId ${classificationId} và customFieldId ${customFieldId}: ${error.message}`);
    }
  }

  /**
   * Xóa giá trị trường tùy chỉnh theo ID phân loại
   * @param classificationId ID của phân loại
   */
  async deleteByClassificationId(classificationId: number): Promise<void> {
    try {
      await this.delete({ classificationId });
    } catch (error) {
      this.logger.error(`Lỗi khi xóa giá trị trường tùy chỉnh theo classificationId ${classificationId}: ${error.message}`);
      throw new Error(`Lỗi khi xóa giá trị trường tùy chỉnh theo classificationId ${classificationId}: ${error.message}`);
    }
  }

  /**
   * Tìm giá trị trường tùy chỉnh theo danh sách ID phân loại
   * @param classificationIds Danh sách ID phân loại
   * @returns Danh sách giá trị trường tùy chỉnh
   */
  async findByClassificationIds(classificationIds: number[]): Promise<CustomFieldClassification[]> {
    if (!classificationIds.length) {
      return [];
    }

    this.logger.log(`Tìm giá trị trường tùy chỉnh với classificationIds: ${classificationIds.join(', ')}`);
    try {
      const customFieldValues = await this.createBaseQuery()
        .where('customFieldClassification.classificationId IN (:...classificationIds)', { classificationIds })
        .getMany();

      this.logger.log(`Đã tìm thấy ${customFieldValues.length} giá trị trường tùy chỉnh cho ${classificationIds.length} phân loại`);
      return customFieldValues;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm giá trị trường tùy chỉnh với classificationIds: ${error.message}`, error.stack);
      throw error;
    }
  }
}