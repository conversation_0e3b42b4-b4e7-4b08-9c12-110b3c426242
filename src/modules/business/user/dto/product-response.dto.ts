import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { PriceTypeEnum } from '@modules/business/enums';
import { ClassificationResponseDto } from './classification.dto';

// Đã loại bỏ GridConfigDto vì không còn cần thiết

// Đã loại bỏ FieldConfigDto vì không còn cần thiết

// Đã loại bỏ FieldDto vì không còn cần thiết

// Đã loại bỏ GroupDto vì không còn cần thiết

// Đã loại bỏ CustomFieldResponseDto vì không còn cần thiết

/**
 * DTO cho cấu hình vận chuyển
 */
export class ShipmentConfigResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Chiều rộng (cm)',
    example: 30,
  })
  widthCm: number;

  @Expose()
  @ApiProperty({
    description: 'Chiều cao (cm)',
    example: 5,
  })
  heightCm: number;

  @Expose()
  @ApiProperty({
    description: 'Chiều dài (cm)',
    example: 40,
  })
  lengthCm: number;

  @Expose()
  @ApiProperty({
    description: 'Trọng lượng (gram)',
    example: 250,
  })
  weightGram: number;
}

/**
 * DTO cho URL upload hình ảnh trong module business
 */
export class BusinessPresignedUrlImageDto {
  @ApiProperty({
    description: 'URL để upload hình ảnh',
    example: 'https://cdn.redai.vn/uploads/user_products/2025/05/product-image-0-1715270400000-uuid.jpg',
  })
  url: string;

  @ApiProperty({
    description: 'Key của hình ảnh trên S3',
    example: 'uploads/user_products/2025/05/product-image-0-1715270400000-uuid.jpg',
  })
  key: string;

  @ApiProperty({
    description: 'Chỉ số của hình ảnh',
    example: 0,
  })
  index: number;
}

/**
 * DTO cho thông tin URL upload trong module business
 */
export class BusinessUploadUrlsDto {
  @ApiProperty({
    description: 'ID sản phẩm',
    example: '101',
  })
  productId: string;

  @ApiProperty({
    description: 'Danh sách URL upload hình ảnh',
    type: [BusinessPresignedUrlImageDto],
  })
  imagesUploadUrls: BusinessPresignedUrlImageDto[];
}

/**
 * DTO cho response khi tạo sản phẩm thành công
 */
export class ProductResponseDto {
  @Expose()
  @ApiProperty({
    description: 'ID của sản phẩm',
    example: 101,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Áo thun nam',
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Giá sản phẩm',
    oneOf: [
      {
        type: 'object',
        properties: {
          listPrice: { type: 'number', example: 200000 },
          salePrice: { type: 'number', example: 150000 },
          currency: { type: 'string', example: 'VND' }
        }
      },
      {
        type: 'object',
        properties: {
          priceDescription: { type: 'string', example: 'Giá chưa công bố' }
        }
      },
      { type: 'null' }
    ],
    example: { listPrice: 200000, salePrice: 150000, currency: 'VND' },
  })
  price: any;

  @Expose()
  @ApiProperty({
    description: 'Loại giá',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
  })
  typePrice: PriceTypeEnum;

  @Expose()
  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Áo thun nam chất liệu cotton cao cấp',
    required: false,
  })
  description?: string;

  @Expose()
  @ApiProperty({
    description: 'Danh sách hình ảnh',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        key: { type: 'string', example: 'uploads/user_products/2025/05/product-image-0-1715270400000-uuid.jpg' },
        position: { type: 'number', example: 0 },
        url: { type: 'string', example: 'https://cdn.redai.vn/uploads/user_products/2025/05/product-image-0-1715270400000-uuid.jpg?t=1715270400000' }
      }
    },
    example: [
      {
        key: 'uploads/user_products/2025/05/product-image-0-1715270400000-uuid.jpg',
        position: 0,
        url: 'https://cdn.redai.vn/uploads/user_products/2025/05/product-image-0-1715270400000-uuid.jpg?t=1715270400000'
      }
    ],
    required: false,
  })
  images?: Array<{
    key: string;
    position: number;
    url: string;
  }>;

  @Expose()
  @ApiProperty({
    description: 'Danh sách tag',
    type: [String],
    example: ['áo thun', 'nam', 'cotton'],
    required: false,
  })
  tags?: string[];

  // Các trường groupFieldId và customFields đã bị loại bỏ vì không tồn tại trong database

  @Expose()
  @ApiProperty({
    description: 'ID người tạo',
    example: 1001,
  })
  createdBy: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: 1741708800000,
  })
  createdAt: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian cập nhật (Unix timestamp)',
    example: 1741708800000,
  })
  updatedAt: number;

  @Expose()
  @ApiProperty({
    description: 'Cấu hình vận chuyển',
    type: ShipmentConfigResponseDto,
  })
  @Type(() => ShipmentConfigResponseDto)
  shipmentConfig: ShipmentConfigResponseDto;

  // Đã loại bỏ các trường nameEmbedding, descriptionEmbedding, tagsEmbedding theo yêu cầu

  @Expose()
  @ApiProperty({
    description: 'Thông tin URL upload',
    type: BusinessUploadUrlsDto,
    required: false,
  })
  uploadUrls?: BusinessUploadUrlsDto;

  @Expose()
  @ApiProperty({
    description: 'Danh sách phân loại sản phẩm',
    type: [ClassificationResponseDto],
    required: false,
  })
  classifications?: ClassificationResponseDto[];
}
