import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto';

/**
 * DTO cho các tham số truy vấn danh sách file
 */
export class QueryFileDto extends QueryDto {
  /**
   * ID người dùng
   * @example 1
   */
  @ApiProperty({
    description: 'ID người dùng',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID người dùng phải là số' })
  @Type(() => Number)
  userId?: number;

  /**
   * ID kho ảo
   * @example 1
   */
  @ApiProperty({
    description: 'ID kho ảo',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID kho ảo phải là số' })
  @Type(() => Number)
  warehouseId?: number;

  /**
   * ID thư mục
   * @example 1
   */
  @ApiProperty({
    description: 'ID thư mục',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID thư mục phải là số' })
  @Type(() => Number)
  folderId?: number;

  /**
   * Trường sắp xếp
   * @example "id"
   */
  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: ['id', 'name', 'sizeBytes', 'createdAt', 'updatedAt'],
    default: 'id',
    required: false,
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'id';

  /**
   * Hướng sắp xếp
   * @example "ASC"
   */
  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.ASC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  @Type(() => String)
  sortDirection?: SortDirection = SortDirection.ASC;

}
