import { Test, TestingModule } from '@nestjs/testing';
import { UserPhysicalWarehouseService } from '../../services/user-physical-warehouse.service';
import { PhysicalWarehouseRepository, WarehouseRepository } from '@modules/business/repositories';
import { ValidationHelper } from '../../helpers/validation.helper';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { CreatePhysicalWarehouseDto, UpdatePhysicalWarehouseDto, QueryPhysicalWarehouseDto } from '../../dto/warehouse';
import { PhysicalWarehouse, Warehouse } from '@modules/business/entities';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { plainToInstance } from 'class-transformer';
import { PhysicalWarehouseResponseDto } from '../../dto/warehouse/physical-warehouse-response.dto';

describe('UserPhysicalWarehouseService', () => {
  let service: UserPhysicalWarehouseService;
  let physicalWarehouseRepository: PhysicalWarehouseRepository;
  let warehouseRepository: WarehouseRepository;
  let validationHelper: ValidationHelper;

  // Mock data
  const mockPhysicalWarehouses: PhysicalWarehouse[] = [
    {
      warehouseId: 1,
      address: '123 Đường ABC, Quận 1, TP.HCM',
      capacity: 1000,
    },
    {
      warehouseId: 2,
      address: '456 Đường XYZ, Quận 2, TP.HCM',
      capacity: 2000,
    },
  ];

  const mockWarehouses: Warehouse[] = [
    {
      warehouseId: 1,
      name: 'Kho hàng 1',
      description: 'Mô tả kho hàng 1',
      type: WarehouseTypeEnum.PHYSICAL,
    },
    {
      warehouseId: 2,
      name: 'Kho hàng 2',
      description: 'Mô tả kho hàng 2',
      type: WarehouseTypeEnum.PHYSICAL,
    },
  ];

  const mockPhysicalWarehouseWithDetails = {
    warehouseId: 1,
    address: '123 Đường ABC, Quận 1, TP.HCM',
    capacity: 1000,
    name: 'Kho hàng 1',
    description: 'Mô tả kho hàng 1',
    type: WarehouseTypeEnum.PHYSICAL,
    customFields: [
      {
        warehouseId: 1,
        fieldId: 1,
        value: { value: 'Giá trị 1' },
      },
    ],
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserPhysicalWarehouseService,
        {
          provide: PhysicalWarehouseRepository,
          useValue: {
            createPhysicalWarehouse: jest.fn(),
            findByWarehouseId: jest.fn(),
            findByWarehouseIdWithDetails: jest.fn(),
            findAll: jest.fn(),
            updatePhysicalWarehouse: jest.fn(),
            deletePhysicalWarehouse: jest.fn(),
          },
        },
        {
          provide: WarehouseRepository,
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            findById: jest.fn(),
          },
        },
        {
          provide: ValidationHelper,
          useValue: {
            validateCreatePhysicalWarehouse: jest.fn(),
            validateWarehouseExists: jest.fn(),
            validateUpdatePhysicalWarehouse: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UserPhysicalWarehouseService>(UserPhysicalWarehouseService);
    physicalWarehouseRepository = module.get<PhysicalWarehouseRepository>(PhysicalWarehouseRepository);
    warehouseRepository = module.get<WarehouseRepository>(WarehouseRepository);
    validationHelper = module.get<ValidationHelper>(ValidationHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createPhysicalWarehouse', () => {
    it('nên tạo kho vật lý mới thành công', async () => {
      // Arrange
      const createDto: CreatePhysicalWarehouseDto = {
        name: 'Kho hàng mới',
        description: 'Mô tả kho hàng mới',
        address: '789 Đường DEF, Quận 3, TP.HCM',
        capacity: 3000,
      };
      const newWarehouse = {
        warehouseId: 3,
        name: 'Kho hàng mới',
        description: 'Mô tả kho hàng mới',
        type: WarehouseTypeEnum.PHYSICAL,
      };
      const newPhysicalWarehouse = {
        warehouseId: 3,
        address: '789 Đường DEF, Quận 3, TP.HCM',
        capacity: 3000,
      };

      jest.spyOn(validationHelper, 'validateCreatePhysicalWarehouse').mockResolvedValue(undefined);
      jest.spyOn(warehouseRepository, 'create').mockReturnValue(newWarehouse as Warehouse);
      jest.spyOn(warehouseRepository, 'save').mockResolvedValue(newWarehouse as Warehouse);
      jest.spyOn(physicalWarehouseRepository, 'createPhysicalWarehouse').mockResolvedValue(newPhysicalWarehouse as PhysicalWarehouse);

      // Act
      const result = await service.createPhysicalWarehouse(createDto);

      // Assert
      expect(validationHelper.validateCreatePhysicalWarehouse).toHaveBeenCalledWith(createDto);
      expect(warehouseRepository.create).toHaveBeenCalledWith({
        name: 'Kho hàng mới',
        description: 'Mô tả kho hàng mới',
        type: WarehouseTypeEnum.PHYSICAL,
      });
      expect(warehouseRepository.save).toHaveBeenCalledWith(newWarehouse);
      expect(physicalWarehouseRepository.createPhysicalWarehouse).toHaveBeenCalledWith({
        warehouseId: 3,
        address: '789 Đường DEF, Quận 3, TP.HCM',
        capacity: 3000,
      });
      expect(result).toBeInstanceOf(PhysicalWarehouseResponseDto);
      expect(result.warehouseId).toBe(3);
      expect(result.name).toBe('Kho hàng mới');
      expect(result.description).toBe('Mô tả kho hàng mới');
      expect(result.type).toBe(WarehouseTypeEnum.PHYSICAL);
      expect(result.address).toBe('789 Đường DEF, Quận 3, TP.HCM');
      expect(result.capacity).toBe(3000);
    });

    it('nên ném lỗi khi validation thất bại', async () => {
      // Arrange
      const createDto: CreatePhysicalWarehouseDto = {
        name: 'Kho hàng mới',
        description: 'Mô tả kho hàng mới',
        address: '789 Đường DEF, Quận 3, TP.HCM',
        capacity: 3000,
      };
      const error = new AppException(BUSINESS_ERROR_CODES.PHYSICAL_WAREHOUSE_CREATION_FAILED, 'Validation error');

      jest.spyOn(validationHelper, 'validateCreatePhysicalWarehouse').mockRejectedValue(error);

      // Act & Assert
      await expect(service.createPhysicalWarehouse(createDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateCreatePhysicalWarehouse).toHaveBeenCalledWith(createDto);
    });

    it('nên ném lỗi khi tạo warehouse thất bại', async () => {
      // Arrange
      const createDto: CreatePhysicalWarehouseDto = {
        name: 'Kho hàng mới',
        description: 'Mô tả kho hàng mới',
        address: '789 Đường DEF, Quận 3, TP.HCM',
        capacity: 3000,
      };
      const newWarehouse = {
        warehouseId: 3,
        name: 'Kho hàng mới',
        description: 'Mô tả kho hàng mới',
        type: WarehouseTypeEnum.PHYSICAL,
      };

      jest.spyOn(validationHelper, 'validateCreatePhysicalWarehouse').mockResolvedValue(undefined);
      jest.spyOn(warehouseRepository, 'create').mockReturnValue(newWarehouse as Warehouse);
      jest.spyOn(warehouseRepository, 'save').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.createPhysicalWarehouse(createDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateCreatePhysicalWarehouse).toHaveBeenCalledWith(createDto);
      expect(warehouseRepository.create).toHaveBeenCalled();
      expect(warehouseRepository.save).toHaveBeenCalled();
    });
  });

  describe('updatePhysicalWarehouse', () => {
    it('nên cập nhật kho vật lý thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const updateDto: UpdatePhysicalWarehouseDto = {
        address: '123 Đường ABC (đã cập nhật), Quận 1, TP.HCM',
        capacity: 1500,
      };
      const existingWarehouse = mockWarehouses[0];
      const existingPhysicalWarehouse = mockPhysicalWarehouses[0];
      const updatedPhysicalWarehouse = {
        ...existingPhysicalWarehouse,
        address: '123 Đường ABC (đã cập nhật), Quận 1, TP.HCM',
        capacity: 1500,
      };

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(physicalWarehouseRepository, 'findByWarehouseId').mockResolvedValue(existingPhysicalWarehouse);
      jest.spyOn(validationHelper, 'validateUpdatePhysicalWarehouse').mockResolvedValue(undefined);
      jest.spyOn(physicalWarehouseRepository, 'updatePhysicalWarehouse').mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });
      jest.spyOn(physicalWarehouseRepository, 'findByWarehouseIdWithDetails').mockResolvedValue(
        { ...mockPhysicalWarehouseWithDetails, ...updatedPhysicalWarehouse }
      );

      // Act
      const result = await service.updatePhysicalWarehouse(warehouseId, updateDto);

      // Assert
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(physicalWarehouseRepository.findByWarehouseId).toHaveBeenCalledWith(warehouseId);
      expect(validationHelper.validateUpdatePhysicalWarehouse).toHaveBeenCalledWith(updateDto, existingPhysicalWarehouse);
      expect(physicalWarehouseRepository.updatePhysicalWarehouse).toHaveBeenCalledWith(warehouseId, updateDto);
      expect(physicalWarehouseRepository.findByWarehouseIdWithDetails).toHaveBeenCalledWith(warehouseId);
      expect(result).toBeInstanceOf(PhysicalWarehouseResponseDto);
      expect(result.warehouseId).toBe(1);
      expect(result.address).toBe('123 Đường ABC (đã cập nhật), Quận 1, TP.HCM');
      expect(result.capacity).toBe(1500);
    });

    it('nên ném lỗi khi kho không tồn tại', async () => {
      // Arrange
      const warehouseId = 999;
      const updateDto: UpdatePhysicalWarehouseDto = {
        address: '123 Đường ABC (đã cập nhật), Quận 1, TP.HCM',
      };
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND, 'Kho không tồn tại');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.updatePhysicalWarehouse(warehouseId, updateDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi kho vật lý không tồn tại', async () => {
      // Arrange
      const warehouseId = 1;
      const updateDto: UpdatePhysicalWarehouseDto = {
        address: '123 Đường ABC (đã cập nhật), Quận 1, TP.HCM',
      };
      const existingWarehouse = mockWarehouses[0];

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(physicalWarehouseRepository, 'findByWarehouseId').mockResolvedValue(null);

      // Act & Assert
      await expect(service.updatePhysicalWarehouse(warehouseId, updateDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(physicalWarehouseRepository.findByWarehouseId).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi validation thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const updateDto: UpdatePhysicalWarehouseDto = {
        address: '123 Đường ABC (đã cập nhật), Quận 1, TP.HCM',
      };
      const existingWarehouse = mockWarehouses[0];
      const existingPhysicalWarehouse = mockPhysicalWarehouses[0];
      const error = new AppException(BUSINESS_ERROR_CODES.PHYSICAL_WAREHOUSE_UPDATE_FAILED, 'Validation error');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(physicalWarehouseRepository, 'findByWarehouseId').mockResolvedValue(existingPhysicalWarehouse);
      jest.spyOn(validationHelper, 'validateUpdatePhysicalWarehouse').mockRejectedValue(error);

      // Act & Assert
      await expect(service.updatePhysicalWarehouse(warehouseId, updateDto)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(physicalWarehouseRepository.findByWarehouseId).toHaveBeenCalledWith(warehouseId);
      expect(validationHelper.validateUpdatePhysicalWarehouse).toHaveBeenCalledWith(updateDto, existingPhysicalWarehouse);
    });
  });

  describe('getPhysicalWarehouseById', () => {
    it('nên lấy thông tin kho vật lý theo ID thành công', async () => {
      // Arrange
      const warehouseId = 1;

      jest.spyOn(physicalWarehouseRepository, 'findByWarehouseIdWithDetails').mockResolvedValue(mockPhysicalWarehouseWithDetails);

      // Act
      const result = await service.getPhysicalWarehouseById(warehouseId);

      // Assert
      expect(physicalWarehouseRepository.findByWarehouseIdWithDetails).toHaveBeenCalledWith(warehouseId);
      expect(result).toBeInstanceOf(PhysicalWarehouseResponseDto);
      expect(result.warehouseId).toBe(1);
      expect(result.name).toBe('Kho hàng 1');
      expect(result.description).toBe('Mô tả kho hàng 1');
      expect(result.type).toBe(WarehouseTypeEnum.PHYSICAL);
      expect(result.address).toBe('123 Đường ABC, Quận 1, TP.HCM');
      expect(result.capacity).toBe(1000);
      expect(result.customFields).toHaveLength(1);
    });

    it('nên ném lỗi khi kho vật lý không tồn tại', async () => {
      // Arrange
      const warehouseId = 999;

      jest.spyOn(physicalWarehouseRepository, 'findByWarehouseIdWithDetails').mockResolvedValue(null);

      // Act & Assert
      await expect(service.getPhysicalWarehouseById(warehouseId)).rejects.toThrow(AppException);
      expect(physicalWarehouseRepository.findByWarehouseIdWithDetails).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi lấy thông tin kho vật lý thất bại', async () => {
      // Arrange
      const warehouseId = 1;

      jest.spyOn(physicalWarehouseRepository, 'findByWarehouseIdWithDetails').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.getPhysicalWarehouseById(warehouseId)).rejects.toThrow(AppException);
      expect(physicalWarehouseRepository.findByWarehouseIdWithDetails).toHaveBeenCalledWith(warehouseId);
    });
  });

  describe('getPhysicalWarehouses', () => {
    it('nên lấy danh sách kho vật lý với phân trang thành công', async () => {
      // Arrange
      const queryDto: QueryPhysicalWarehouseDto = {
        page: 1,
        limit: 10,
        search: 'kho',
        sortBy: 'name',
        sortDirection: 'ASC',
      };
      const paginatedResult = {
        items: [mockPhysicalWarehouseWithDetails],
        meta: {
          totalItems: 1,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      jest.spyOn(physicalWarehouseRepository, 'findAll').mockResolvedValue(paginatedResult);

      // Act
      const result = await service.getPhysicalWarehouses(queryDto);

      // Assert
      expect(physicalWarehouseRepository.findAll).toHaveBeenCalledWith(queryDto);
      expect(result.items.length).toBe(1);
      expect(result.items[0]).toBeInstanceOf(PhysicalWarehouseResponseDto);
      expect(result.items[0].warehouseId).toBe(1);
      expect(result.items[0].name).toBe('Kho hàng 1');
      expect(result.meta.totalItems).toBe(1);
    });

    it('nên ném lỗi khi lấy danh sách kho vật lý thất bại', async () => {
      // Arrange
      const queryDto: QueryPhysicalWarehouseDto = {
        page: 1,
        limit: 10,
      };

      jest.spyOn(physicalWarehouseRepository, 'findAll').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.getPhysicalWarehouses(queryDto)).rejects.toThrow(AppException);
      expect(physicalWarehouseRepository.findAll).toHaveBeenCalledWith(queryDto);
    });
  });

  describe('deletePhysicalWarehouse', () => {
    it('nên xóa kho vật lý thành công', async () => {
      // Arrange
      const warehouseId = 1;
      const existingWarehouse = mockWarehouses[0];
      const existingPhysicalWarehouse = mockPhysicalWarehouses[0];

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(physicalWarehouseRepository, 'findByWarehouseId').mockResolvedValue(existingPhysicalWarehouse);
      jest.spyOn(physicalWarehouseRepository, 'deletePhysicalWarehouse').mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });
      jest.spyOn(warehouseRepository, 'delete').mockResolvedValue({ affected: 1, raw: {}, generatedMaps: [] });

      // Act
      await service.deletePhysicalWarehouse(warehouseId);

      // Assert
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(physicalWarehouseRepository.findByWarehouseId).toHaveBeenCalledWith(warehouseId);
      expect(physicalWarehouseRepository.deletePhysicalWarehouse).toHaveBeenCalledWith(warehouseId);
      expect(warehouseRepository.delete).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi kho không tồn tại', async () => {
      // Arrange
      const warehouseId = 999;
      const error = new AppException(BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND, 'Kho không tồn tại');

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockRejectedValue(error);

      // Act & Assert
      await expect(service.deletePhysicalWarehouse(warehouseId)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi kho vật lý không tồn tại', async () => {
      // Arrange
      const warehouseId = 1;
      const existingWarehouse = mockWarehouses[0];

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(physicalWarehouseRepository, 'findByWarehouseId').mockResolvedValue(null);

      // Act & Assert
      await expect(service.deletePhysicalWarehouse(warehouseId)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(physicalWarehouseRepository.findByWarehouseId).toHaveBeenCalledWith(warehouseId);
    });

    it('nên ném lỗi khi xóa kho vật lý thất bại', async () => {
      // Arrange
      const warehouseId = 1;
      const existingWarehouse = mockWarehouses[0];
      const existingPhysicalWarehouse = mockPhysicalWarehouses[0];

      jest.spyOn(validationHelper, 'validateWarehouseExists').mockResolvedValue(existingWarehouse);
      jest.spyOn(physicalWarehouseRepository, 'findByWarehouseId').mockResolvedValue(existingPhysicalWarehouse);
      jest.spyOn(physicalWarehouseRepository, 'deletePhysicalWarehouse').mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(service.deletePhysicalWarehouse(warehouseId)).rejects.toThrow(AppException);
      expect(validationHelper.validateWarehouseExists).toHaveBeenCalledWith(warehouseId);
      expect(physicalWarehouseRepository.findByWarehouseId).toHaveBeenCalledWith(warehouseId);
      expect(physicalWarehouseRepository.deletePhysicalWarehouse).toHaveBeenCalledWith(warehouseId);
    });
  });
});
