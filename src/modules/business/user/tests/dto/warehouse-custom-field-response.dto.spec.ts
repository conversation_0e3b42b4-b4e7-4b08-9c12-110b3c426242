import { plainToInstance } from 'class-transformer';
import { WarehouseCustomFieldResponseDto } from '../../dto/warehouse/warehouse-custom-field-response.dto';

describe('WarehouseCustomFieldResponseDto', () => {
  it('nên chuyển đổi đúng từ plain object sang DTO với đầy đủ thông tin', () => {
    // Arrange
    const plainData = {
      warehouseId: 1,
      fieldId: 2,
      value: { value: 'Giá trị mẫu' },
      extraField: 'should be excluded',
    };

    // Act
    const dto = plainToInstance(WarehouseCustomFieldResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(WarehouseCustomFieldResponseDto);
    expect(dto.warehouseId).toBe(1);
    expect(dto.fieldId).toBe(2);
    expect(dto.value).toEqual({ value: 'Gi<PERSON> trị mẫu' });
    expect((dto as any).extraField).toBeUndefined();
  });

  it('nên chuyển đổi đúng từ plain object sang DTO với value là đối tượng phức tạp', () => {
    // Arrange
    const plainData = {
      warehouseId: 1,
      fieldId: 2,
      value: {
        value: 'Giá trị mẫu',
        additionalInfo: {
          key1: 'value1',
          key2: 123,
          key3: true,
        },
        items: [1, 2, 3],
      },
    };

    // Act
    const dto = plainToInstance(WarehouseCustomFieldResponseDto, plainData, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeInstanceOf(WarehouseCustomFieldResponseDto);
    expect(dto.value).toEqual({
      value: 'Giá trị mẫu',
      additionalInfo: {
        key1: 'value1',
        key2: 123,
        key3: true,
      },
      items: [1, 2, 3],
    });
  });

  it('nên chuyển đổi một mảng các plain object sang mảng DTO', () => {
    // Arrange
    const plainDataArray = [
      {
        warehouseId: 1,
        fieldId: 1,
        value: { value: 'Giá trị 1' },
      },
      {
        warehouseId: 1,
        fieldId: 2,
        value: { value: 'Giá trị 2' },
      },
    ];

    // Act
    const dtoArray = plainToInstance(WarehouseCustomFieldResponseDto, plainDataArray, { excludeExtraneousValues: true });

    // Assert
    expect(Array.isArray(dtoArray)).toBe(true);
    expect(dtoArray.length).toBe(2);
    expect(dtoArray[0]).toBeInstanceOf(WarehouseCustomFieldResponseDto);
    expect(dtoArray[1]).toBeInstanceOf(WarehouseCustomFieldResponseDto);
    expect(dtoArray[0].warehouseId).toBe(1);
    expect(dtoArray[1].warehouseId).toBe(1);
    expect(dtoArray[0].fieldId).toBe(1);
    expect(dtoArray[1].fieldId).toBe(2);
    expect(dtoArray[0].value).toEqual({ value: 'Giá trị 1' });
    expect(dtoArray[1].value).toEqual({ value: 'Giá trị 2' });
  });
});
