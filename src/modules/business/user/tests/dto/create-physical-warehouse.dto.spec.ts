import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { CreatePhysicalWarehouseDto } from '../../dto/warehouse/create-physical-warehouse.dto';

describe('CreatePhysicalWarehouseDto', () => {
  it('nên xác thực DTO hợp lệ với đầy đủ thông tin bắt buộc', async () => {
    // Arrange
    const dto = plainToInstance(CreatePhysicalWarehouseDto, {
      address: '123 Đường ABC, Quận 1, TP.HCM',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin bao gồm cả trường không bắt buộc', async () => {
    // Arrange
    const dto = plainToInstance(CreatePhysicalWarehouseDto, {
      address: '123 Đường ABC, Quận 1, TP.HCM',
      capacity: 1000,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi thiếu address', async () => {
    // Arrange
    const dto = plainToInstance(CreatePhysicalWarehouseDto, {
      capacity: 1000,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const addressErrors = errors.find(e => e.property === 'address');
    expect(addressErrors).toBeDefined();
    expect(addressErrors?.constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi address không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(CreatePhysicalWarehouseDto, {
      address: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const addressErrors = errors.find(e => e.property === 'address');
    expect(addressErrors).toBeDefined();
    expect(addressErrors?.constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi address vượt quá độ dài tối đa', async () => {
    // Arrange
    const dto = plainToInstance(CreatePhysicalWarehouseDto, {
      address: 'a'.repeat(256), // 256 ký tự, vượt quá giới hạn 255
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const addressErrors = errors.find(e => e.property === 'address');
    expect(addressErrors).toBeDefined();
    expect(addressErrors?.constraints).toHaveProperty('maxLength');
  });

  it('nên thất bại khi capacity không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(CreatePhysicalWarehouseDto, {
      address: '123 Đường ABC, Quận 1, TP.HCM',
      capacity: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const capacityErrors = errors.find(e => e.property === 'capacity');
    expect(capacityErrors).toBeDefined();
    expect(capacityErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên chuyển đổi đúng kiểu dữ liệu cho các trường số', async () => {
    // Arrange
    const dto = plainToInstance(CreatePhysicalWarehouseDto, {
      address: '123 Đường ABC, Quận 1, TP.HCM',
      capacity: '1000', // String that should be converted to number
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(typeof dto.capacity).toBe('number');
    expect(dto.capacity).toBe(1000);
  });
});
