import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { CreateVirtualWarehouseDto } from '../../dto/warehouse/create-virtual-warehouse.dto';

describe('CreateVirtualWarehouseDto', () => {
  it('nên xác thực DTO hợp lệ khi không có trường nào được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(CreateVirtualWarehouseDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(CreateVirtualWarehouseDto, {
      associatedSystem: 'SAP ERP',
      purpose: 'Quản lý hàng hóa trực tuyến',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi associatedSystem không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(CreateVirtualWarehouseDto, {
      associatedSystem: 123,
      purpose: 'Quản lý hàng hóa trực tuyến',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const associatedSystemErrors = errors.find(e => e.property === 'associatedSystem');
    expect(associatedSystemErrors).toBeDefined();
    expect(associatedSystemErrors?.constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi associatedSystem vượt quá độ dài tối đa', async () => {
    // Arrange
    const dto = plainToInstance(CreateVirtualWarehouseDto, {
      associatedSystem: 'a'.repeat(101), // 101 ký tự, vượt quá giới hạn 100
      purpose: 'Quản lý hàng hóa trực tuyến',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const associatedSystemErrors = errors.find(e => e.property === 'associatedSystem');
    expect(associatedSystemErrors).toBeDefined();
    expect(associatedSystemErrors?.constraints).toHaveProperty('maxLength');
  });

  it('nên thất bại khi purpose không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(CreateVirtualWarehouseDto, {
      associatedSystem: 'SAP ERP',
      purpose: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const purposeErrors = errors.find(e => e.property === 'purpose');
    expect(purposeErrors).toBeDefined();
    expect(purposeErrors?.constraints).toHaveProperty('isString');
  });
});
