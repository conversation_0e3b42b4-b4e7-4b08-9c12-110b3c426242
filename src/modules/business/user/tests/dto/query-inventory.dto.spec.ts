import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { QueryInventoryDto } from '../../dto/inventory/query-inventory.dto';
import { SortDirection } from '@common/dto';

describe('QueryInventoryDto', () => {
  it('nên xác thực DTO hợp lệ với các giá trị mặc định', async () => {
    // Arrange
    const dto = plainToInstance(QueryInventoryDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.sortBy).toBe('id');
    expect(dto.sortDirection).toBe(SortDirection.ASC);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin', async () => {
    // Arrange
    const dto = plainToInstance(QueryInventoryDto, {
      page: 2,
      limit: 20,
      productId: 1,
      warehouseId: 1,
      sortBy: 'currentQuantity',
      sortDirection: SortDirection.DESC,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(dto.page).toBe(2);
    expect(dto.limit).toBe(20);
    expect(dto.productId).toBe(1);
    expect(dto.warehouseId).toBe(1);
    expect(dto.sortBy).toBe('currentQuantity');
    expect(dto.sortDirection).toBe(SortDirection.DESC);
  });

  it('nên thất bại khi page không phải là số nguyên', async () => {
    // Arrange
    const dto = plainToInstance(QueryInventoryDto, {
      page: 1.5,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const pageErrors = errors.find(e => e.property === 'page');
    expect(pageErrors).toBeDefined();
    expect(pageErrors?.constraints).toHaveProperty('isInt');
  });

  it('nên thất bại khi page nhỏ hơn 1', async () => {
    // Arrange
    const dto = plainToInstance(QueryInventoryDto, {
      page: 0,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const pageErrors = errors.find(e => e.property === 'page');
    expect(pageErrors).toBeDefined();
    expect(pageErrors?.constraints).toHaveProperty('min');
  });

  it('nên thất bại khi limit không phải là số nguyên', async () => {
    // Arrange
    const dto = plainToInstance(QueryInventoryDto, {
      limit: 10.5,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const limitErrors = errors.find(e => e.property === 'limit');
    expect(limitErrors).toBeDefined();
    expect(limitErrors?.constraints).toHaveProperty('isInt');
  });

  it('nên thất bại khi limit nhỏ hơn 1', async () => {
    // Arrange
    const dto = plainToInstance(QueryInventoryDto, {
      limit: 0,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const limitErrors = errors.find(e => e.property === 'limit');
    expect(limitErrors).toBeDefined();
    expect(limitErrors?.constraints).toHaveProperty('min');
  });

  it('nên thất bại khi productId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryInventoryDto, {
      productId: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const productIdErrors = errors.find(e => e.property === 'productId');
    expect(productIdErrors).toBeDefined();
    expect(productIdErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi warehouseId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryInventoryDto, {
      warehouseId: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const warehouseIdErrors = errors.find(e => e.property === 'warehouseId');
    expect(warehouseIdErrors).toBeDefined();
    expect(warehouseIdErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên thất bại khi sortDirection không phải là giá trị hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryInventoryDto, {
      sortDirection: 'INVALID',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const sortDirectionErrors = errors.find(e => e.property === 'sortDirection');
    expect(sortDirectionErrors).toBeDefined();
    expect(sortDirectionErrors?.constraints).toHaveProperty('isEnum');
  });

  it('nên chuyển đổi đúng kiểu dữ liệu cho các trường số', async () => {
    // Arrange
    const dto = plainToInstance(QueryInventoryDto, {
      page: '2',
      limit: '20',
      productId: '1',
      warehouseId: '1',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(typeof dto.page).toBe('number');
    expect(typeof dto.limit).toBe('number');
    expect(typeof dto.productId).toBe('number');
    expect(typeof dto.warehouseId).toBe('number');
    expect(dto.page).toBe(2);
    expect(dto.limit).toBe(20);
    expect(dto.productId).toBe(1);
    expect(dto.warehouseId).toBe(1);
  });
});
