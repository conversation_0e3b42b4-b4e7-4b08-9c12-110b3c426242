import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { CreateWarehouseDto } from '../../dto/warehouse/create-warehouse.dto';
import { WarehouseTypeEnum } from '@modules/business/enums';

describe('CreateWarehouseDto', () => {
  it('nên xác thực DTO hợp lệ với đầy đủ thông tin bắt buộc', async () => {
    // Arrange
    const dto = plainToInstance(CreateWarehouseDto, {
      name: 'Kho hàng chính',
      type: WarehouseTypeEnum.PHYSICAL,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với đầy đủ thông tin bao gồm cả trường không bắt buộc', async () => {
    // Arrange
    const dto = plainToInstance(CreateWarehouseDto, {
      name: '<PERSON><PERSON> hàng chín<PERSON>',
      description: '<PERSON><PERSON> hàng chính của công ty',
      type: WarehouseTypeEnum.PHYSICAL,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi thiếu name', async () => {
    // Arrange
    const dto = plainToInstance(CreateWarehouseDto, {
      type: WarehouseTypeEnum.PHYSICAL,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const nameErrors = errors.find(e => e.property === 'name');
    expect(nameErrors).toBeDefined();
    expect(nameErrors?.constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi thiếu type', async () => {
    // Arrange
    const dto = plainToInstance(CreateWarehouseDto, {
      name: 'Kho hàng chính',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const typeErrors = errors.find(e => e.property === 'type');
    expect(typeErrors).toBeDefined();
    expect(typeErrors?.constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi name không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(CreateWarehouseDto, {
      name: 123,
      type: WarehouseTypeEnum.PHYSICAL,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const nameErrors = errors.find(e => e.property === 'name');
    expect(nameErrors).toBeDefined();
    expect(nameErrors?.constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi name vượt quá độ dài tối đa', async () => {
    // Arrange
    const dto = plainToInstance(CreateWarehouseDto, {
      name: 'a'.repeat(101), // 101 ký tự, vượt quá giới hạn 100
      type: WarehouseTypeEnum.PHYSICAL,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const nameErrors = errors.find(e => e.property === 'name');
    expect(nameErrors).toBeDefined();
    expect(nameErrors?.constraints).toHaveProperty('maxLength');
  });

  it('nên thất bại khi description không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(CreateWarehouseDto, {
      name: 'Kho hàng chính',
      description: 123,
      type: WarehouseTypeEnum.PHYSICAL,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const descriptionErrors = errors.find(e => e.property === 'description');
    expect(descriptionErrors).toBeDefined();
    expect(descriptionErrors?.constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi type không phải là giá trị hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(CreateWarehouseDto, {
      name: 'Kho hàng chính',
      type: 'INVALID_TYPE',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const typeErrors = errors.find(e => e.property === 'type');
    expect(typeErrors).toBeDefined();
    expect(typeErrors?.constraints).toHaveProperty('isEnum');
  });

  it('nên xác thực DTO hợp lệ với type là VIRTUAL', async () => {
    // Arrange
    const dto = plainToInstance(CreateWarehouseDto, {
      name: 'Kho ảo',
      type: WarehouseTypeEnum.VIRTUAL,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });
});
