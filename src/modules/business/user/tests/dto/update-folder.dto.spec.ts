import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { UpdateFolderDto } from '../../dto/folder/update-folder.dto';

describe('UpdateFolderDto', () => {
  it('nên xác thực DTO hợp lệ khi không có trường nào được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(UpdateFolderDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với một trường được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(UpdateFolderDto, {
      name: 'Tài liệu dự án (đã cập nhật)',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với tất cả các trường được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(UpdateFolderDto, {
      name: 'Tài liệu dự án (đã cập nhật)',
      parentId: 2,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ khi parentId là null', async () => {
    // Arrange
    const dto = plainToInstance(UpdateFolderDto, {
      name: 'Tài liệu dự án (đã cập nhật)',
      parentId: null,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi name không phải là chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(UpdateFolderDto, {
      name: 123,
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const nameErrors = errors.find(e => e.property === 'name');
    expect(nameErrors).toBeDefined();
    expect(nameErrors?.constraints).toHaveProperty('isString');
  });

  it('nên thất bại khi name vượt quá độ dài tối đa', async () => {
    // Arrange
    const dto = plainToInstance(UpdateFolderDto, {
      name: 'a'.repeat(256), // 256 ký tự, vượt quá giới hạn 255
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const nameErrors = errors.find(e => e.property === 'name');
    expect(nameErrors).toBeDefined();
    expect(nameErrors?.constraints).toHaveProperty('maxLength');
  });

  it('nên thất bại khi parentId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(UpdateFolderDto, {
      parentId: 'not-a-number',
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const parentIdErrors = errors.find(e => e.property === 'parentId');
    expect(parentIdErrors).toBeDefined();
    expect(parentIdErrors?.constraints).toHaveProperty('isNumber');
  });

  it('nên chuyển đổi đúng kiểu dữ liệu cho các trường số', async () => {
    // Arrange
    const dto = plainToInstance(UpdateFolderDto, {
      parentId: '2', // String that should be converted to number
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
    expect(typeof dto.parentId).toBe('number');
    expect(dto.parentId).toBe(2);
  });
});
