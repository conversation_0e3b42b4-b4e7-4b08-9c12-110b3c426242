import { Test, TestingModule } from '@nestjs/testing';
import { UserFolderController } from '../../controllers/user-folder.controller';
import { UserFolderService } from '../../services/user-folder.service';
import { CreateFolderDto, UpdateFolderDto, QueryFolderDto } from '../../dto/folder';
import { FolderResponseDto } from '../../dto/folder/folder-response.dto';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { JwtPayload } from '@common/interfaces';
import { PaginatedResult } from '@common/response';

describe('UserFolderController', () => {
  let controller: UserFolderController;
  let service: UserFolderService;

  // Mock data
  const mockUser: JwtPayload = {
    id: 1,
    email: '<EMAIL>',
    role: 'user',
  };

  const mockFolderResponse: FolderResponseDto = {
    id: 1,
    name: 'Thư mục 1',
    parentId: null,
    userId: 1,
    path: '/Thư mục 1',
    root: 1,
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
  };

  const mockFolderResponseList: FolderResponseDto[] = [
    mockFolderResponse,
    {
      id: 2,
      name: 'Thư mục 2',
      parentId: 1,
      userId: 1,
      path: '/Thư mục 1/Thư mục 2',
      root: 1,
      createdAt: 1625097700000,
      updatedAt: 1625097700000,
    },
  ];

  const mockPaginatedResult: PaginatedResult<FolderResponseDto> = {
    items: mockFolderResponseList,
    meta: {
      totalItems: 2,
      itemCount: 2,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  const mockFolderDetail = {
    ...mockFolderResponse,
    parentFolder: null,
    user: {
      id: 1,
      fullName: 'Nguyễn Văn A',
      email: '<EMAIL>',
    },
    virtualWarehouse: {
      warehouseId: 1,
      associatedSystem: 'System A',
      purpose: 'Purpose A',
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserFolderController],
      providers: [
        {
          provide: UserFolderService,
          useValue: {
            createFolder: jest.fn(),
            updateFolder: jest.fn(),
            getFolderById: jest.fn(),
            getFolderDetailById: jest.fn(),
            getFolders: jest.fn(),
            getRootFolders: jest.fn(),
            deleteFolder: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UserFolderController>(UserFolderController);
    service = module.get<UserFolderService>(UserFolderService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createFolder', () => {
    it('nên tạo thư mục mới thành công', async () => {
      // Arrange
      const createDto: CreateFolderDto = {
        name: 'Thư mục mới',
        parentId: null,
        root: 1,
      };

      jest.spyOn(service, 'createFolder').mockResolvedValue(mockFolderResponse);

      // Act
      const result = await controller.createFolder(createDto, mockUser);

      // Assert
      expect(service.createFolder).toHaveBeenCalledWith(createDto, mockUser);
      expect(result.data).toEqual(mockFolderResponse);
      expect(result.message).toBe('Tạo thư mục thành công');
    });

    it('nên ném lỗi khi tạo thư mục thất bại', async () => {
      // Arrange
      const createDto: CreateFolderDto = {
        name: 'Thư mục mới',
        parentId: null,
        root: 1,
      };
      const error = new AppException(BUSINESS_ERROR_CODES.FOLDER_CREATION_FAILED, 'Lỗi khi tạo thư mục');

      jest.spyOn(service, 'createFolder').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.createFolder(createDto, mockUser)).rejects.toThrow(AppException);
      expect(service.createFolder).toHaveBeenCalledWith(createDto, mockUser);
    });
  });

  describe('updateFolder', () => {
    it('nên cập nhật thư mục thành công', async () => {
      // Arrange
      const folderId = 1;
      const updateDto: UpdateFolderDto = {
        name: 'Thư mục đã cập nhật',
      };

      jest.spyOn(service, 'updateFolder').mockResolvedValue(mockFolderResponse);

      // Act
      const result = await controller.updateFolder(folderId, updateDto);

      // Assert
      expect(service.updateFolder).toHaveBeenCalledWith(folderId, updateDto);
      expect(result.data).toEqual(mockFolderResponse);
      expect(result.message).toBe('Cập nhật thư mục thành công');
    });

    it('nên ném lỗi khi cập nhật thư mục thất bại', async () => {
      // Arrange
      const folderId = 1;
      const updateDto: UpdateFolderDto = {
        name: 'Thư mục đã cập nhật',
      };
      const error = new AppException(BUSINESS_ERROR_CODES.FOLDER_UPDATE_FAILED, 'Lỗi khi cập nhật thư mục');

      jest.spyOn(service, 'updateFolder').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.updateFolder(folderId, updateDto)).rejects.toThrow(AppException);
      expect(service.updateFolder).toHaveBeenCalledWith(folderId, updateDto);
    });
  });

  describe('getFolderById', () => {
    it('nên lấy thông tin thư mục theo ID thành công', async () => {
      // Arrange
      const folderId = 1;

      jest.spyOn(service, 'getFolderById').mockResolvedValue(mockFolderResponse);

      // Act
      const result = await controller.getFolderById(folderId);

      // Assert
      expect(service.getFolderById).toHaveBeenCalledWith(folderId);
      expect(result.data).toEqual(mockFolderResponse);
      expect(result.message).toBe('Lấy thông tin thư mục thành công');
    });

    it('nên ném lỗi khi lấy thông tin thư mục thất bại', async () => {
      // Arrange
      const folderId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.FOLDER_NOT_FOUND, 'Thư mục không tồn tại');

      jest.spyOn(service, 'getFolderById').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getFolderById(folderId)).rejects.toThrow(AppException);
      expect(service.getFolderById).toHaveBeenCalledWith(folderId);
    });
  });

  describe('getFolderDetailById', () => {
    it('nên lấy thông tin chi tiết thư mục theo ID thành công', async () => {
      // Arrange
      const folderId = 1;

      jest.spyOn(service, 'getFolderDetailById').mockResolvedValue(mockFolderDetail);

      // Act
      const result = await controller.getFolderDetailById(folderId);

      // Assert
      expect(service.getFolderDetailById).toHaveBeenCalledWith(folderId);
      expect(result.data).toEqual(mockFolderDetail);
      expect(result.message).toBe('Lấy thông tin chi tiết thư mục thành công');
    });

    it('nên ném lỗi khi lấy thông tin chi tiết thư mục thất bại', async () => {
      // Arrange
      const folderId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.FOLDER_NOT_FOUND, 'Thư mục không tồn tại');

      jest.spyOn(service, 'getFolderDetailById').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getFolderDetailById(folderId)).rejects.toThrow(AppException);
      expect(service.getFolderDetailById).toHaveBeenCalledWith(folderId);
    });
  });

  describe('getFolders', () => {
    it('nên lấy danh sách thư mục với phân trang thành công', async () => {
      // Arrange
      const queryDto: QueryFolderDto = {
        page: 1,
        limit: 10,
        userId: 1,
      };

      jest.spyOn(service, 'getFolders').mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.getFolders(queryDto);

      // Assert
      expect(service.getFolders).toHaveBeenCalledWith(queryDto);
      expect(result.data).toEqual(mockPaginatedResult);
      expect(result.message).toBe('Lấy danh sách thư mục thành công');
    });

    it('nên ném lỗi khi lấy danh sách thư mục thất bại', async () => {
      // Arrange
      const queryDto: QueryFolderDto = {
        page: 1,
        limit: 10,
      };
      const error = new AppException(BUSINESS_ERROR_CODES.FOLDER_FETCH_FAILED, 'Lỗi khi lấy danh sách thư mục');

      jest.spyOn(service, 'getFolders').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getFolders(queryDto)).rejects.toThrow(AppException);
      expect(service.getFolders).toHaveBeenCalledWith(queryDto);
    });
  });

  describe('getRootFolders', () => {
    it('nên lấy danh sách thư mục gốc thành công', async () => {
      // Arrange
      jest.spyOn(service, 'getRootFolders').mockResolvedValue([mockFolderResponse]);

      // Act
      const result = await controller.getRootFolders(mockUser);

      // Assert
      expect(service.getRootFolders).toHaveBeenCalledWith(mockUser);
      expect(result.data).toEqual([mockFolderResponse]);
      expect(result.message).toBe('Lấy danh sách thư mục gốc thành công');
    });

    it('nên ném lỗi khi lấy danh sách thư mục gốc thất bại', async () => {
      // Arrange
      const error = new AppException(BUSINESS_ERROR_CODES.FOLDER_FETCH_FAILED, 'Lỗi khi lấy danh sách thư mục gốc');

      jest.spyOn(service, 'getRootFolders').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getRootFolders(mockUser)).rejects.toThrow(AppException);
      expect(service.getRootFolders).toHaveBeenCalledWith(mockUser);
    });
  });

  describe('deleteFolder', () => {
    it('nên xóa thư mục thành công', async () => {
      // Arrange
      const folderId = 1;

      jest.spyOn(service, 'deleteFolder').mockResolvedValue(undefined);

      // Act
      const result = await controller.deleteFolder(folderId);

      // Assert
      expect(service.deleteFolder).toHaveBeenCalledWith(folderId);
      expect(result.message).toBe('Xóa thư mục thành công');
    });

    it('nên ném lỗi khi xóa thư mục thất bại', async () => {
      // Arrange
      const folderId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.FOLDER_DELETE_FAILED, 'Lỗi khi xóa thư mục');

      jest.spyOn(service, 'deleteFolder').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.deleteFolder(folderId)).rejects.toThrow(AppException);
      expect(service.deleteFolder).toHaveBeenCalledWith(folderId);
    });
  });
});
