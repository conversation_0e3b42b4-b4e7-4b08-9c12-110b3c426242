import { Entity, PrimaryColumn, Column } from 'typeorm';

/**
 * Entity đại diện cho bảng admin_group_tools_type_agents trong cơ sở dữ liệu
 * Bảng liên kết ánh xạ nhóm công cụ admin với loại agent
 */
@Entity('admin_group_tools_type_agents')
export class AdminGroupToolsTypeAgents {
  /**
   * Mã nhóm công cụ, tham chiếu đến admin_group_tools
   * Là một phần của khóa chính
   */
  @PrimaryColumn({ name: 'group_id', type: 'integer' })
  groupId: number;

  /**
   * Mã loại agent, tham chiếu đến type_agents
   * Là một phần của khóa chính
   */
  @PrimaryColumn({ name: 'type_agent_id', type: 'integer' })
  typeAgentId: number;
}
