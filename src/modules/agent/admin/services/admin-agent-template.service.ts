import { Injectable, Logger } from '@nestjs/common';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { AgentTemplateRepository } from '@modules/agent/repositories/agent-template.repository';
import { TypeAgentRepository } from '@modules/agent/repositories/type-agent.repository';
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { Agent, AgentTemplate } from '@modules/agent/entities';
import { ModelConfig } from '@modules/agent/interfaces/model-config.interface';
import { AvatarUrlHelper } from '../helpers/avatar-url.helper';
import { AgentStatusEnum, AgentTemplateStatus } from '@modules/agent/constants';
import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { VectorStoreRepository } from '@modules/data/knowledge-files/repositories/vector-store.repository';
import {
  AgentTemplateDetailDto,
  AgentTemplateListItemDto,
  AgentTemplateQueryDto,
  CreateAgentTemplateDto,
  DeletedAgentTemplateQueryDto,
  RestoreAgentTemplateDto,
  UpdateAgentTemplateDto,
  UpdateAgentTemplateStatusDto,
} from '../dto/agent-template';
import { ModelConfigResponseDto } from '../dto/agent-system';
import { TypeAgentResponseDto } from '../dto/type-agent';
import { BaseModelUserService } from '@modules/model-training/user/services';
import { TypeProviderEnum } from '@modules/model-training/constants/type-provider.enum';
import { BaseModelAdminService } from '@/modules/model-training/admin/services';

/**
 * Service xử lý logic nghiệp vụ cho Agent Template
 */
@Injectable()
export class AdminAgentTemplateService {
  private readonly logger = new Logger(AdminAgentTemplateService.name);

  constructor(
    private readonly agentRepository: AgentRepository,
    private readonly agentTemplateRepository: AgentTemplateRepository,
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly s3Service: S3Service,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly vectorStoreRepository: VectorStoreRepository,
    private readonly baseModelAdminService: BaseModelAdminService,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Lấy danh sách agent template với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent template với phân trang
   */
  async findAll(
    queryDto: AgentTemplateQueryDto,
  ): Promise<PaginatedResult<AgentTemplateListItemDto>> {
    this.logger.log(
      `Lấy danh sách agent template với query: ${JSON.stringify(queryDto)}`,
    );

    const { page, limit, search, status, sortBy, sortDirection } = queryDto;

    // Lấy danh sách agent template với phân trang
    const result = await this.agentTemplateRepository.findPaginated(
      page,
      limit,
      search,
      status,
      sortBy,
      sortDirection,
    );

    // Lấy thông tin agent tương ứng, chỉ lấy các trường cần thiết
    const agentIds = result.items.map((template) => template.id);

    // Sử dụng phương thức findByIds mới đã được cập nhật
    const agents = await this.agentRepository.findByIds(agentIds);

    // Log để debug
    this.logger.debug(`Số lượng agent tìm thấy: ${agents.length}`);
    if (agents.length > 0) {
      this.logger.debug(`Mẫu agent đầu tiên: ${JSON.stringify({
        id: agents[0].id,
        name: agents[0].name,
        avatar: agents[0].avatar,
        modelConfig: agents[0].modelConfig,
        status: agents[0].status
      })}`);
    }

    // Map kết quả sang DTO
    const items = result.items
      .map((template) => {
        const agent = agents.find((a) => a.id === template.id);

        if (!agent) {
          return null;
        }

        return this.mapToListItemDto(agent);
      })
      .filter(Boolean) as AgentTemplateListItemDto[];

    // Log để debug
    if (items.length > 0) {
      this.logger.debug(`Mẫu DTO đầu tiên: ${JSON.stringify(items[0])}`);
    }

    return {
      items,
      meta: result.meta,
    };
  }

  /**
   * Lấy chi tiết agent template theo ID
   * @param id ID của agent template
   * @returns Chi tiết agent template
   */
  async findById(id: string): Promise<AgentTemplateDetailDto> {
    this.logger.log(`Lấy chi tiết agent template với ID: ${id}`);

    // Tìm agent template
    const template = await this.agentTemplateRepository.findById(id);

    if (!template) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND);
    }

    // Tìm agent tương ứng
    const agent = await this.agentRepository.findById(id);

    if (!agent) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    // Tìm loại agent
    const typeAgent = await this.typeAgentRepository.findById(template.typeId);

    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // Map kết quả sang DTO
    return await this.mapToDetailDto(agent, template, typeAgent);
  }

  /**
   * Tạo agent template mới
   * @param createDto Dữ liệu tạo agent template
   * @param employeeId ID của nhân viên tạo
   * @returns URL tải lên avatar và ID của agent template mới
   */
  async create(
    createDto: CreateAgentTemplateDto,
    employeeId: number,
  ): Promise<string | null> {
    this.logger.log(`Tạo agent template mới: ${JSON.stringify(createDto)}`);

    // Kiểm tra tên agent đã tồn tại chưa
    const existingAgent = await this.agentRepository.findByName(createDto.name);

    if (existingAgent) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NAME_EXISTS);
    }

    // Kiểm tra loại agent có tồn tại không
    const typeAgent = await this.typeAgentRepository.findById(createDto.typeId);

    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // Kiểm tra model có tồn tại không và có trạng thái APPROVED không
    await this.validateModel(
      createDto.modelConfig.modelId,
    );

    // Kiểm tra vector store có tồn tại không nếu có cung cấp
    if (createDto.vectorStoreId) {
      const vectorStore = await this.vectorStoreRepository.findOne({
        where: { id: createDto.vectorStoreId }
      });

      if (!vectorStore) {
        throw new AppException(AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
          `Vector store với ID ${createDto.vectorStoreId} không tồn tại`);
      }
    }

    try {
      // Tạo agent mới
      const modelConfig: ModelConfig = {
        modelId: createDto.modelConfig.modelId,
        temperature: createDto.modelConfig.temperature,
        top_p: createDto.modelConfig.top_p,
        top_k: createDto.modelConfig.top_k,
        max_tokens: createDto.modelConfig.max_tokens,
        // Tự động xác định providerName từ modelId nếu không được cung cấp
        providerName: createDto.modelConfig.providerName || this.getProviderFromModelId(createDto.modelConfig.modelId),
      };

      const agent = this.agentRepository.create({
        name: createDto.name,
        modelConfig,
        instruction: createDto.instruction,
        vectorStoreId: createDto.vectorStoreId,
        status: this.mapTemplateStatusToAgentStatus(createDto.status),
        isForSale: false, // Mặc định là false vì đã loại bỏ trường isForSale
      });

      // Lưu agent
      const savedAgent = (await this.agentRepository.save(agent)) as Agent;

      // Tạo agent template
      const template = this.agentTemplateRepository.create({
        id: savedAgent.id,
        typeId: createDto.typeId,
        profile: createDto.profile || {
          gender: undefined,
          dateOfBirth: undefined,
          position: undefined,
          education: undefined,
          skills: [],
          personality: [],
          languages: [],
          nations: undefined
        },
        convertConfig: createDto.convertConfig || {
          name: '',
          description: ''
        },
        status: createDto.status,
        createdBy: employeeId,
      });

      // Tạo URL tải lên avatar nếu có
      let avatarUrl: string | undefined;

      if (createDto.avatarMimeType) {
        // Tạo URL tải lên avatar
        const avatarUrlData = await AvatarUrlHelper.generateUploadUrl(
          this.s3Service,
          employeeId.toString(),
          createDto.avatarMimeType,
          savedAgent.id,
          undefined // Không có avatar key hiện tại vì đây là agent mới
        );

        avatarUrl = avatarUrlData.url;

        // Cập nhật avatar key cho agent nếu có key mới
        if (avatarUrlData.key) {
          agent.avatar = avatarUrlData.key;
          // Lưu lại agent để cập nhật avatar key
          await this.agentRepository.save(agent);
        }
      }

      // Lưu agent template
      await this.agentTemplateRepository.save(template);

      return avatarUrl || null;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi tạo agent template: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_CREATION_FAILED);
    }
  }

  /**
   * Cập nhật agent template
   * @param id ID của agent template
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @returns URL tải lên avatar mới (nếu có)
   */
  async update(
    id: string,
    updateDto: UpdateAgentTemplateDto,
    employeeId: number,
  ): Promise<{
    avatarUrlUpload?: string;
  }> {
    this.logger.log(
      `Cập nhật agent template với ID ${id}: ${JSON.stringify(updateDto)}`,
    );

    // Tìm agent template
    const template = await this.agentTemplateRepository.findById(id);

    if (!template) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND);
    }

    // Tìm agent tương ứng
    const agent = await this.agentRepository.findById(id);

    if (!agent) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    // Kiểm tra tên agent đã tồn tại chưa (nếu có thay đổi tên)
    if (updateDto.name && updateDto.name !== agent.name) {
      const existingAgent = await this.agentRepository.findByName(
        updateDto.name,
      );

      if (existingAgent && existingAgent.id !== id) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_NAME_EXISTS);
      }
    }

    // Kiểm tra loại agent có tồn tại không (nếu có thay đổi loại)
    if (updateDto.typeId && updateDto.typeId !== template.typeId) {
      const typeAgent = await this.typeAgentRepository.findById(
        updateDto.typeId,
      );

      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }
    }

    // Kiểm tra model có tồn tại không (nếu có thay đổi model)
    if (updateDto.modelConfig) {
      await this.validateModel(
        updateDto.modelConfig.modelId,
      );
    }

    // Kiểm tra vector store có tồn tại không (nếu có thay đổi vector store)
    if (updateDto.vectorStoreId !== undefined) {
      const vectorStore = await this.vectorStoreRepository.findOne({
        where: { id: updateDto.vectorStoreId }
      });

      if (!vectorStore) {
        throw new AppException(AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
          `Vector store với ID ${updateDto.vectorStoreId} không tồn tại`);
      }
    }

    try {
      // Cập nhật agent
      const agentUpdateData: Partial<Agent> = {};

      if (updateDto.name) agentUpdateData.name = updateDto.name;
      if (updateDto.modelConfig) {
        const modelConfig: ModelConfig = {
          modelId: updateDto.modelConfig.modelId,
          temperature: updateDto.modelConfig.temperature,
          top_p: updateDto.modelConfig.top_p,
          top_k: updateDto.modelConfig.top_k,
          max_tokens: updateDto.modelConfig.max_tokens,
          // Tự động xác định providerName từ modelId nếu không được cung cấp
          providerName: updateDto.modelConfig.providerName || this.getProviderFromModelId(updateDto.modelConfig.modelId),
        };
        agentUpdateData.modelConfig = modelConfig;
      }
      if (updateDto.instruction !== undefined)
        agentUpdateData.instruction = updateDto.instruction;
      if (updateDto.vectorStoreId !== undefined)
        agentUpdateData.vectorStoreId = updateDto.vectorStoreId;
      if (updateDto.status)
        agentUpdateData.status = this.mapTemplateStatusToAgentStatus(
          updateDto.status,
        );
      if (updateDto.isForSale !== undefined)
        agentUpdateData.isForSale = updateDto.isForSale;

      if (Object.keys(agentUpdateData).length > 0) {
        await this.agentRepository.update(id, agentUpdateData);
      }

      // Cập nhật agent template
      const templateUpdateData: Partial<AgentTemplate> = {
        updatedBy: employeeId,
      };

      if (updateDto.typeId) templateUpdateData.typeId = updateDto.typeId;
      if (updateDto.profile) templateUpdateData.profile = updateDto.profile;
      if (updateDto.convertConfig)
        templateUpdateData.convertConfig = updateDto.convertConfig;
      if (updateDto.status) templateUpdateData.status = updateDto.status;

      if (Object.keys(templateUpdateData).length > 0) {
        await this.agentTemplateRepository.update(id, templateUpdateData);
      }

      // Tạo URL tải lên avatar mới nếu có
      let avatarUrl: string | undefined;

      if (updateDto.avatarMimeType) {
        // Tạo URL tải lên avatar
        // Nếu agent đã có avatar key, sử dụng key đó để tạo URL upload
        const avatarUrlData = await AvatarUrlHelper.generateUploadUrl(
          this.s3Service,
          employeeId.toString(),
          updateDto.avatarMimeType,
          id,
          agent.avatar || undefined // Truyền avatar key hiện tại nếu có
        );

        avatarUrl = avatarUrlData.url;

        // Cập nhật avatar key cho agent nếu có key mới
        if (avatarUrlData.key) {
          await this.agentRepository.update(id, { avatar: avatarUrlData.key });
        }
      }

      return {
        avatarUrlUpload: avatarUrl,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi cập nhật agent template: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Cập nhật trạng thái agent template
   * @param id ID của agent template
   * @param updateStatusDto Dữ liệu cập nhật trạng thái
   * @param employeeId ID của nhân viên cập nhật
   */
  async updateStatus(
    id: string,
    updateStatusDto: UpdateAgentTemplateStatusDto,
    employeeId: number,
  ): Promise<void> {
    this.logger.log(
      `Cập nhật trạng thái agent template với ID ${id}: ${JSON.stringify(updateStatusDto)}`,
    );

    // Tìm agent template
    const template = await this.agentTemplateRepository.findById(id);

    if (!template) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND);
    }

    // Tìm agent tương ứng
    const agent = await this.agentRepository.findById(id);

    if (!agent) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }
    try {
      // Cập nhật trạng thái agent
      await this.agentRepository.update(id, {
        status: this.mapTemplateStatusToAgentStatus(updateStatusDto.status),
      });

      // Cập nhật trạng thái agent template
      await this.agentTemplateRepository.updateStatus(
        id,
        updateStatusDto.status,
        employeeId,
      );
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi cập nhật trạng thái agent template: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_TEMPLATE_STATUS_UPDATE_FAILED,
      );
    }
  }

  /**
   * Xóa agent template
   * @param id ID của agent template
   * @param employeeId ID của nhân viên xóa
   */
  async remove(id: string, employeeId: number): Promise<void> {
    this.logger.log(`Xóa agent template với ID: ${id}`);

    // Tìm agent template
    const template = await this.agentTemplateRepository.findById(id);

    if (!template) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND);
    }

    // Tìm agent tương ứng
    const agent = await this.agentRepository.findById(id);

    if (!agent) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }
    try {
      // Cập nhật thông tin xóa cho agent template
      await this.agentTemplateRepository.update(id, {
        deletedBy: employeeId,
      });

      // Xóa mềm agent bằng cách cập nhật trực tiếp cột deletedAt
      await this.agentRepository.update(id, {
        deletedAt: Date.now(),
      });
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi xóa agent template: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED);
    }
  }

  /**
   * Lấy danh sách agent template đã xóa với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent template đã xóa với phân trang
   */
  async findAllDeleted(
    queryDto: DeletedAgentTemplateQueryDto,
  ): Promise<PaginatedResult<AgentTemplateListItemDto>> {
    this.logger.log(
      `Lấy danh sách agent template đã xóa với query: ${JSON.stringify(queryDto)}`,
    );

    const { page, limit, search, sortBy, sortDirection } = queryDto;

    // Lấy danh sách agent đã xóa với phân trang
    const result = await this.agentRepository.findDeletedWithPagination(
      page,
      limit,
      search,
      undefined, // Không lọc theo status
      sortBy,
      sortDirection,
    );

    // Map kết quả sang DTO
    const items = result.items
      .map((agent) => this.mapToListItemDto(agent))
      .filter(Boolean) as AgentTemplateListItemDto[];

    return {
      items,
      meta: {
        totalItems: result.total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(result.total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Lấy chi tiết agent template đã xóa theo ID
   * @param id ID của agent template đã xóa
   * @returns Chi tiết agent template đã xóa
   */
  async findDeletedById(id: string): Promise<AgentTemplateDetailDto> {
    this.logger.log(`Lấy chi tiết agent template đã xóa với ID: ${id}`);

    // Tìm agent đã xóa
    const agent = await this.agentRepository.findDeletedById(id);

    if (!agent) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    // Tìm agent template
    const template = await this.agentTemplateRepository.findDeletedById(id);

    if (!template) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND);
    }

    // Tìm loại agent
    const typeAgent = await this.typeAgentRepository.findById(template.typeId);

    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // Map kết quả sang DTO
    return await this.mapToDetailDto(agent, template, typeAgent);
  }

  /**
   * Khôi phục agent template đã xóa
   * @param restoreDto Dữ liệu khôi phục
   * @param employeeId ID của nhân viên khôi phục
   * @returns Số lượng bản ghi đã được khôi phục
   */
  async restore(
    restoreDto: RestoreAgentTemplateDto,
    employeeId: number,
  ): Promise<number> {
    this.logger.log(
      `Khôi phục agent template với IDs: ${JSON.stringify(restoreDto.ids)}`,
    );

    try {
      // Khôi phục các agent
      const agentRestoreCount = await this.agentRepository.restoreAgents(restoreDto.ids);

      // Khôi phục các agent template
      const templateRestoreCount = await this.agentTemplateRepository.restoreMany(
        restoreDto.ids,
        employeeId,
      );

      // Trả về số lượng bản ghi đã được khôi phục
      return Math.min(agentRestoreCount, templateRestoreCount || 0);
    } catch (error) {
      this.logger.error(
        `Lỗi khi khôi phục agent template: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Map agent sang DTO danh sách
   * @param agent Thông tin agent
   * @returns DTO danh sách
   */
  private mapToListItemDto(agent: Agent): AgentTemplateListItemDto {
    // Tạo URL CDN cho avatar nếu có
    let avatarUrl: string | null = null;
    if (agent.avatar) {
      avatarUrl = AvatarUrlHelper.generateViewUrl(this.cdnService, agent.avatar);
    }

    return {
      id: agent.id,
      name: agent.name,
      avatar: avatarUrl || undefined,
      model: agent.modelConfig?.modelId || 'Unknown',
      status: agent.status as any, // Chuyển đổi từ AgentStatusEnum sang AgentTemplateStatus
    };
  }

  /**
   * Map agent và template sang DTO chi tiết
   * @param agent Thông tin agent
   * @param template Thông tin template
   * @param typeAgent Thông tin loại agent
   * @returns DTO chi tiết
   */
  private async mapToDetailDto(
    agent: Agent,
    template: AgentTemplate,
    typeAgent: any,
  ): Promise<AgentTemplateDetailDto> {
    // Lấy thông tin nhân viên tạo, cập nhật và xóa
    let createdByInfo: { name: string; avatar: string | null } | null = null;
    let updatedByInfo: { name: string; avatar: string | null } | null = null;
    let deletedByInfo: { name: string; avatar: string | null } | null = null;

    try {
      if (template.createdBy) {
        createdByInfo = await this.employeeInfoService.getEmployeeInfo(
          template.createdBy,
        );
      }

      if (template.updatedBy) {
        updatedByInfo = await this.employeeInfoService.getEmployeeInfo(
          template.updatedBy,
        );
      }

      if (template.deletedBy) {
        deletedByInfo = await this.employeeInfoService.getEmployeeInfo(
          template.deletedBy,
        );
      }
    } catch (error) {
      this.logger.warn(`Không thể lấy thông tin nhân viên: ${error.message}`);
    }
    // Map thông tin model config
    const modelConfig = {
      modelId: agent.modelConfig?.modelId || '',
      temperature: agent.modelConfig?.temperature || 1.0,
      top_p: agent.modelConfig?.top_p || 1.0,
      top_k: agent.modelConfig?.top_k || 1.0,
      max_tokens: agent.modelConfig?.max_tokens || 1000,
      providerName: this.getProviderFromModelId(agent.modelConfig?.modelId || ''), // Lấy provider từ model_id
    } as ModelConfigResponseDto;

    // Map thông tin loại agent
    const type: TypeAgentResponseDto = {
      id: typeAgent.id,
      name: typeAgent.name,
      description: typeAgent.description || '',
    };

    return {
      id: agent.id,
      name: agent.name,
      avatar: agent.avatar || undefined,
      modelConfig,
      profile: template.profile,
      instruction: agent.instruction || undefined,
      vector: agent.vectorStoreId
        ? await this.getVectorStoreInfo(agent.vectorStoreId)
        : undefined,
      convertConfig: template.convertConfig,
      status: template.status,
      type,
      isForSale: agent.isForSale,
      created: template.createdBy
        ? {
            employeeId: template.createdBy,
            name: createdByInfo?.name || 'Unknown',
            avatar: createdByInfo?.avatar || '',
            createdAt: agent.createdAt,
          }
        : undefined,
      updated: template.updatedBy
        ? {
            employeeId: template.updatedBy,
            name: updatedByInfo?.name || 'Unknown',
            avatar: updatedByInfo?.avatar || '',
            updatedAt: agent.updatedAt,
          }
        : undefined,
      deleted: template.deletedBy
        ? {
            employeeId: template.deletedBy,
            name: deletedByInfo?.name || 'Unknown',
            avatar: deletedByInfo?.avatar || '',
            deletedAt: agent.deletedAt || 0,
          }
        : undefined,
    };
  }

  /**
   * Chuyển đổi trạng thái template sang trạng thái agent
   * @param templateStatus Trạng thái template
   * @returns Trạng thái agent tương ứng
   */
  private mapTemplateStatusToAgentStatus(
    templateStatus: AgentTemplateStatus,
  ): AgentStatusEnum {
    switch (templateStatus) {
      case AgentTemplateStatus.DRAFT:
        return AgentStatusEnum.DRAFT;
      case AgentTemplateStatus.ACTIVE:
        return AgentStatusEnum.APPROVED;
      case AgentTemplateStatus.INACTIVE:
        return AgentStatusEnum.DELETED;
      default:
        return AgentStatusEnum.DRAFT;
    }
  }

  /**
   * Xác định provider dựa trên model_id
   * @param modelId ID của model
   * @returns Provider tương ứng với model_id
   */
  private getProviderFromModelId(modelId: string): TypeProviderEnum {
    // Xác định provider dựa trên model_id
    if (modelId.startsWith('gpt-')) {
      return TypeProviderEnum.OPENAI;
    } else if (modelId.startsWith('claude-')) {
      return TypeProviderEnum.ANTHROPIC;
    } else if (modelId.startsWith('gemini-')) {
      return TypeProviderEnum.GOOGLE;
    } else if (modelId.startsWith('llama-')) {
      return TypeProviderEnum.META;
    } else if (modelId.startsWith('deepseek-')) {
      return TypeProviderEnum.DEEPSEEK;
    } else if (modelId.startsWith('xai-')) {
      return TypeProviderEnum.XAI;
    } else {
      // Mặc định là OpenAI nếu không xác định được
      return TypeProviderEnum.OPENAI;
    }
  }

  /**
   * Lấy thông tin vector store từ ID
   * @param vectorStoreId ID của vector store
   * @returns Thông tin vector store
   */
  private async getVectorStoreInfo(vectorStoreId: string): Promise<
    | {
        vectorStoreId: string;
        vectorStoreName: string;
      }
    | undefined
  > {
    try {
      if (!vectorStoreId) {
        return undefined;
      }

      // Tìm vector store trong database
      const vectorStore = await this.vectorStoreRepository.findOne({
        where: { id: vectorStoreId },
      });

      if (!vectorStore) {
        return {
          vectorStoreId,
          vectorStoreName: 'Unknown Vector Store',
        };
      }

      return {
        vectorStoreId,
        vectorStoreName: vectorStore.name,
      };
    } catch (error) {
      this.logger.warn(`Lỗi khi lấy thông tin vector store: ${error.message}`);
      return {
        vectorStoreId,
        vectorStoreName: 'Vector Store',
      };
    }
  }

  /**
   * Kiểm tra model có tồn tại không và có trạng thái APPROVED không
   * @param modelId ID của model
   */
  private async validateModel(
    modelId: string,
  ): Promise<void> {
    try {
      // Kiểm tra model có tồn tại không bằng cách gọi service của module Model
      const model = await this.baseModelAdminService.getModelByModelId(modelId);

      if (!model) {
        throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED);
      }

      // Kiểm tra model có trạng thái APPROVED không
      if (model.status !== 'APPROVED') {
        throw new AppException(
          AGENT_ERROR_CODES.MODEL_NOT_APPROVED,
          `Model với ID ${modelId} chưa được phê duyệt, không thể sử dụng`
        );
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error validating model: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED);
    }
  }
}
