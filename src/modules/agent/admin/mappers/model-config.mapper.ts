import { ModelConfigResponseDto } from '../dto/common';
import { TypeProviderEnum } from '@modules/model-training/constants/type-provider.enum';
import { ModelConfig } from '@modules/agent/interfaces/model-config.interface';

/**
 * Mapper cho việc chuyển đổi cấu hình model sang DTO
 */
export class ModelConfigMapper {
  /**
   * Chuyển đổi cấu hình model sang DTO
   * @param modelConfig Cấu hình model
   * @param getProviderFromModelId Hàm để lấy tên provider từ model_id
   * @returns ModelConfigResponseDto
   */
  static toResponseDto(
    modelConfig: ModelConfig,
    getProviderFromModelId?: (modelId: string) => TypeProviderEnum
  ): ModelConfigResponseDto {
    // Xác định provider từ model_id
    let providerName = TypeProviderEnum.OPENAI; // Giá trị mặc định
    if (getProviderFromModelId && modelConfig.modelId) {
      providerName = getProviderFromModelId(modelConfig.modelId);
    }

    return {
      ...modelConfig,
      providerName: providerName,
    };
  }
}
