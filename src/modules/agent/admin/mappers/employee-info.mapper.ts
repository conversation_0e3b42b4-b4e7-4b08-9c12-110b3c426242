import { EmployeeInfoDto } from '../dto/common';

/**
 * Mapper cho việc chuyển đổi thông tin nhân viên sang DTO
 */
export class EmployeeInfoMapper {
  /**
   * Chuyển đổi thông tin nhân viên sang DTO
   * @param employeeId ID của nhân viên
   * @param name Tên nhân viên
   * @param avatar Avatar của nhân viên
   * @param date Thời gian (timestamp)
   * @returns EmployeeInfoDto
   */
  static toDto(
    employeeId: number,
    name?: string,
    avatar?: string | null,
    date?: number
  ): EmployeeInfoDto {
    const dto = new EmployeeInfoDto();
    dto.employeeId = employeeId;
    dto.name = name || 'Unknown';
    dto.avatar = avatar || null;
    dto.date = date;
    return dto;
  }
}
