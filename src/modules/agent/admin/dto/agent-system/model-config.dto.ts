import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  Max,
  Min,
} from 'class-validator';

/**
 * DTO cho cấu hình model AI
 */
export class ModelConfigDto {
  /**
   * ID của model
   */
  @ApiProperty({
    description: 'ID của model',
    example: 'gpt-4o',
  })
  @IsString()
  @IsNotEmpty()
  modelId: string;

  /**
   * Giá trị temperature cho model
   */
  @ApiPropertyOptional({
    description: 'Giá trị temperature cho model',
    example: 0.7,
    minimum: 0,
    maximum: 2,
  })
  @IsNumber()
  @Min(0)
  @Max(2)
  @IsOptional()
  temperature?: number;

  /**
   * Giá trị top_p cho model
   */
  @ApiPropertyOptional({
    description: 'Giá trị top_p cho model',
    example: 1.0,
    minimum: 0,
    maximum: 1,
  })
  @IsNumber()
  @Min(0)
  @Max(1)
  @IsOptional()
  top_p?: number;

  /**
   * <PERSON><PERSON><PERSON> trị top_k cho model
   */
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> trị top_k cho model',
    example: 1.0,
    minimum: 0,
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  top_k?: number;

  /**
   * Số token tối đa cho kết quả
   */
  @ApiPropertyOptional({
    description: 'Số token tối đa cho kết quả',
    example: 1000,
    minimum: 1,
  })
  @IsNumber()
  @IsPositive()
  @IsOptional()
  max_tokens?: number;

  /**
   * Giá trị frequency_penalty cho model
   */
  @ApiPropertyOptional({
    description: 'Giá trị frequency_penalty cho model',
    example: 0,
    minimum: -2,
    maximum: 2,
  })
  @IsNumber()
  @Min(-2)
  @Max(2)
  @IsOptional()
  frequency_penalty?: number;

  /**
   * Giá trị presence_penalty cho model
   */
  @ApiPropertyOptional({
    description: 'Giá trị presence_penalty cho model',
    example: 0,
    minimum: -2,
    maximum: 2,
  })
  @IsNumber()
  @Min(-2)
  @Max(2)
  @IsOptional()
  presence_penalty?: number;



  /**
   * Tên của nhà cung cấp model
   */
  @ApiPropertyOptional({
    description: 'Tên của nhà cung cấp model (sẽ được tự động xác định từ modelId nếu không được cung cấp)',
    example: 'OPENAI',
  })
  @IsString()
  @IsOptional()
  providerName?: string;
}
