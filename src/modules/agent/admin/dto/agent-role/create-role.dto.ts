import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString, MaxLength, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ModuleMcpConfigInterface } from '@modules/agent/interfaces/module-mcp-config.interface';

/**
 * DTO cho cấu hình reconnect trong MCP
 */
export class ReconnectDto {
  @ApiProperty({
    description: 'Bật/tắt tính năng reconnect',
    example: false,
  })
  @IsBoolean()
  enable: boolean = false;

  @ApiProperty({
    description: 'Thời gian delay giữa các lần reconnect (ms)',
    example: 1000,
  })
  @IsNumber()
  delayMs: number = 1000;

  @ApiProperty({
    description: 'Số lần thử reconnect tối đa',
    example: 3,
  })
  @IsNumber()
  maxAttempts: number = 3;
}

/**
 * DTO cho c<PERSON><PERSON> hình module MCP
 */
export class ModuleMcpConfigDto implements ModuleMcpConfigInterface {
  @ApiProperty({
    description: 'Tên server MCP',
    example: '',
  })
  @IsString()
  mcpNameServer: string = '';

  @ApiProperty({
    description: 'Port của server MCP',
    example: 0,
  })
  @IsNumber()
  mcpPort: number = 0;

  @ApiProperty({
    description: 'URL của server MCP',
    example: '',
  })
  @IsString()
  url: string = '';

  @ApiProperty({
    description: 'Sử dụng Node EventSource',
    example: false,
  })
  @IsBoolean()
  useNodeEventSource: boolean = false;

  @ApiProperty({
    description: 'Header cho request',
    example: {},
  })
  @IsObject()
  header: Record<string, string> = {};

  @ApiProperty({
    description: 'Cấu hình reconnect',
    type: ReconnectDto,
  })
  @ValidateNested()
  @Type(() => ReconnectDto)
  reconnect: ReconnectDto = new ReconnectDto();
}

/**
 * DTO cho request body khi tạo vai trò mới
 */
export class CreateRoleDto {
  @ApiProperty({
    description: 'Tên của vai trò',
    example: 'Admin Assistant',
    maxLength: 50,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  name: string;

  @ApiProperty({
    description: 'Mô tả của vai trò',
    example: 'Vai trò hỗ trợ quản trị viên',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Cấu hình module MCP',
    type: ModuleMcpConfigDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ModuleMcpConfigDto)
  moduleMcpConfig?: ModuleMcpConfigDto;
}
