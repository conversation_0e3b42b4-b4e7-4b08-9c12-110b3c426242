import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { EmployeeInfoDto } from '@modules/agent/admin/dto/common';
import { ModuleMcpConfigDto } from './create-role.dto';
import { ModuleMcpConfigInterface } from '@modules/agent/interfaces/module-mcp-config.interface';

/**
 * DTO đơn giản cho danh sách vai trò
 */
export class RoleListResponseDto {
  @ApiProperty({
    description: 'ID của vai trò',
    example: 'role-id-1',
  })
  id: string;

  @ApiProperty({
    description: 'Tên của vai trò',
    example: 'Admin Assistant',
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả của vai trò',
    example: 'Vai trò hỗ trợ quản trị viên',
  })
  description: string;
}

/**
 * DTO cho thông tin nhóm tool
 */
export class GroupToolInfoDto {
  @ApiProperty({
    description: 'ID của nhóm tool',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Tên của nhóm tool',
    example: 'Nhóm công cụ SEO',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Mô tả của nhóm tool',
    example: 'Các công cụ hỗ trợ tối ưu hóa SEO cho website',
  })
  description?: string;
}

/**
 * DTO cho thông tin agent sử dụng vai trò
 */
export class RoleAgentUseDto {
  @ApiProperty({
    description: 'ID của agent',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Tên của agent',
    example: 'System Assistant',
  })
  name: string;

  @ApiProperty({
    description: 'URL avatar của agent',
    example: 'https://cdn.example.com/avatars/agent-avatar.png',
    required: false,
  })
  avatar?: string | null;
}

/**
 * DTO cho response khi trả về thông tin vai trò
 */
export class RoleResponseDto {
  @ApiProperty({
    description: 'ID của vai trò',
    example: 'role-id-1',
  })
  id: string;

  @ApiProperty({
    description: 'Tên của vai trò',
    example: 'Admin Assistant',
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả của vai trò',
    example: 'Vai trò hỗ trợ quản trị viên',
  })
  description: string;

  @ApiProperty({
    description: 'Thông tin agent đang sử dụng vai trò',
    type: RoleAgentUseDto,
    required: false,
  })
  agentUse?: RoleAgentUseDto;

  @ApiPropertyOptional({
    description: 'Thông tin người tạo',
    type: EmployeeInfoDto,
  })
  created?: EmployeeInfoDto | null;

  @ApiPropertyOptional({
    description: 'Thông tin người cập nhật',
    type: EmployeeInfoDto,
  })
  updated?: EmployeeInfoDto | null;

  @ApiPropertyOptional({
    description: 'Thông tin người xóa',
    type: EmployeeInfoDto,
  })
  deleted?: EmployeeInfoDto | null;

  @ApiPropertyOptional({
    description: 'Cấu hình module MCP (chỉ được hiển thị, không được sửa hoặc tạo)',
    type: ModuleMcpConfigDto,
  })
  moduleMcpConfig?: ModuleMcpConfigInterface;
}
