import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp của cấp bậc agent
 */
export enum AgentRankSortBy {
  NAME = 'name',
  MIN_EXP = 'minExp',
  MAX_EXP = 'maxExp',
  CREATED_AT = 'createdAt', // Sẽ được xử lý đặc biệt trong repository để sắp xếp theo id
  ID = 'id',
}

/**
 * DTO cho việc truy vấn danh sách cấp bậc agent
 */
export class AgentRankQueryDto extends QueryDto {
  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: AgentRankSortBy,
    default: AgentRankSortBy.MIN_EXP,
  })
  @IsEnum(AgentRankSortBy)
  @IsOptional()
  sortBy: AgentRankSortBy = AgentRankSortBy.MIN_EXP;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.ASC,
  })
  @IsEnum(SortDirection)
  @IsOptional()
  @Type(() => String)
  sortDirection: SortDirection = SortDirection.ASC;

  /**
   * Lọc theo trạng thái kích hoạt
   */
  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái kích hoạt',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  active?: boolean;
}
