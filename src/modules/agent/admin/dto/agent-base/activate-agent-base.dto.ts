import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from 'class-validator';

/**
 * DTO cho việc kích hoạt agent base
 */
export class ActivateAgentBaseDto {
  /**
   * ID của agent base cần kích ho<PERSON>t
   */
  @ApiProperty({
    description: 'ID của agent base cần kích hoạt',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty({ message: 'ID của agent base không được để trống' })
  @IsUUID('4', { message: 'ID của agent base phải là UUID hợp lệ' })
  id: string;
}
