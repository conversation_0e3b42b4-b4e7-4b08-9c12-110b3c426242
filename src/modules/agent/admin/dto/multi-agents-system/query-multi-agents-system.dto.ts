import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsUUID } from 'class-validator';
import { QueryDto } from '@common/dto';

/**
 * Enum cho các trường sắp xếp của MultiAgentsSystem
 */
export enum MultiAgentsSystemSortBy {
  PARENT_AGENT_ID = 'parentAgentId',
  CHILD_AGENT_ID = 'childAgentId',
}

/**
 * DTO để truy vấn danh sách quan hệ đa cấp giữa các agent
 */
export class QueryMultiAgentsSystemDto extends QueryDto {
  /**
   * ID của agent cấp trên để lọc
   */
  @ApiPropertyOptional({
    description: 'ID của agent cấp trên để lọc',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID('4', { message: 'ID của agent cấp trên phải là UUID hợp lệ' })
  parentAgentId?: string;

  /**
   * Trường sắp xếp
   */
  @ApiProperty({
    enum: MultiAgentsSystemSortBy,
    default: MultiAgentsSystemSortBy.PARENT_AGENT_ID,
    description: 'Trường sắp xếp',
  })
  @IsEnum(MultiAgentsSystemSortBy, { message: 'Trường sắp xếp không hợp lệ' })
  @IsOptional()
  sortBy: MultiAgentsSystemSortBy = MultiAgentsSystemSortBy.PARENT_AGENT_ID;
}
