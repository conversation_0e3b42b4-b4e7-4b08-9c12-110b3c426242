import {ApiProperty, ApiPropertyOptional} from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import {Type} from 'class-transformer';
import {TypeAgentStatus} from '@modules/agent/constants';
import {TypeAgentConfig} from '@modules/agent/interfaces/type-agent-config.interface';

/**
 * DTO cho cấu hình loại agent
 */
export class TypeAgentConfigDto implements TypeAgentConfig {
  /**
   * <PERSON><PERSON> hồ sơ không
   */
  @ApiProperty({
    description: '<PERSON><PERSON> hồ sơ không',
    example: true,
  })
  @IsBoolean()
  hasProfile: boolean;

  /**
   * Có đầu ra không
   */
  @ApiProperty({
    description: 'Có đầu ra không',
    example: true,
  })
  @IsBoolean()
  hasOutput: boolean;

  /**
   * <PERSON><PERSON> chuyển đổi không
   */
  @ApiProperty({
    description: '<PERSON><PERSON> chuyển đổi không',
    example: false,
  })
  @IsBoolean()
  hasConversion: boolean;

  /**
   * <PERSON>ó tài nguyên không
   */
  @ApiProperty({
    description: 'Có tài nguyên không',
    example: true,
  })
  @IsBoolean()
  hasResources: boolean;

  /**
   * Có chiến lược không
   */
  @ApiProperty({
    description: 'Có chiến lược không',
    example: true,
  })
  @IsBoolean()
  hasStrategy: boolean;

  /**
   * Có đa agent không
   */
  @ApiProperty({
    description: 'Có đa agent không',
    example: true,
  })
  @IsBoolean()
  hasMultiAgent: boolean;
}

/**
 * DTO cho việc tạo loại agent mới
 */
export class CreateTypeAgentDto {
  /**
   * Tên loại agent
   */
  @ApiProperty({
    description: 'Tên loại agent',
    example: 'Chatbot Agent',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  /**
   * Mô tả chi tiết về loại agent
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về loại agent',
    example: 'Loại agent hỗ trợ chat với người dùng',
  })
  @IsString()
  @IsOptional()
  description: string | null;

  /**
   * Cấu hình mặc định cho loại agent
   */
  @ApiProperty({
    description: 'Cấu hình mặc định cho loại agent',
    example: {
      hasProfile: true,
      hasOutput: true,
      hasConversion: false,
      hasResources: true,
    },
  })
  @ValidateNested()
  @Type(() => TypeAgentConfigDto)
  defaultConfig: TypeAgentConfigDto;

  /**
   * Trạng thái của loại agent
   */
  @ApiPropertyOptional({
    description: 'Trạng thái của loại agent',
    enum: TypeAgentStatus,
    default: TypeAgentStatus.DRAFT,
  })
  @IsEnum(TypeAgentStatus)
  status: TypeAgentStatus;

  /**
   * Danh sách ID của các nhóm công cụ
   */
  @ApiProperty({
    description: 'Danh sách ID của các nhóm công cụ',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray()
  @IsNumber({}, { each: true })
  groupToolIds: number[];
}
