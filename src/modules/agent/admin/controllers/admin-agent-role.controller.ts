import { ErrorCode } from '@/common';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { CurrentEmployee } from '@modules/auth/decorators';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiExtraModels,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiTags
} from '@nestjs/swagger';
import {
  CreateRoleDto,
  RoleQueryDto,
  RoleResponseDto,
  RoleListResponseDto,
  UpdateRoleDto
} from '../dto/agent-role';
import { AdminAgentRoleService } from '../services/admin-agent-role.service';

/**
 * Controller xử lý các endpoints liên quan đến vai trò của agent
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_AGENT_ROLE)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/agent-roles')
@ApiExtraModels(
  RoleResponseDto,
  RoleListResponseDto,
  ApiResponseDto,
  PaginatedResult
)
export class AdminAgentRoleController {
  constructor(private readonly adminAgentRoleService: AdminAgentRoleService) { }

  /**
   * Lấy danh sách vai trò (đơn giản)
   * @param queryDto Tham số truy vấn
   * @returns Danh sách vai trò với phân trang (chỉ bao gồm thông tin cơ bản)
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách vai trò đơn giản',
    description: 'Lấy danh sách vai trò với phân trang và lọc (chỉ bao gồm thông tin cơ bản)'
  })
  @ApiOkResponse({
    description: 'Danh sách vai trò',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getRoles(
    @Query() queryDto: RoleQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<RoleListResponseDto>>> {
    const result = await this.adminAgentRoleService.getRoles(queryDto);
    return ApiResponseDto.success(result);
  }


  /**
   * Lấy chi tiết vai trò theo ID
   * @param id ID của vai trò
   * @returns Chi tiết vai trò đầy đủ
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết vai trò',
    description: 'Lấy thông tin chi tiết của một vai trò theo ID'
  })
  @ApiOkResponse({
    description: 'Chi tiết vai trò',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getRoleById(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ApiResponseDto<RoleResponseDto>> {
    const result = await this.adminAgentRoleService.getRoleById(id);
    return ApiResponseDto.success(result);
  }

  /**
   * Tạo vai trò mới
   * @param createDto Dữ liệu tạo vai trò
   * @param employeeId ID của nhân viên thực hiện hành động
   * @returns ID của vai trò đã tạo
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo vai trò mới',
    description: 'Tạo vai trò mới với thông tin cung cấp'
  })
  @ApiCreatedResponse({
    description: 'Vai trò đã được tạo thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_ROLE_NAME_EXISTS,
    AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async createRole(
    @Body() createDto: CreateRoleDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{ id: string }>> {
    const id = await this.adminAgentRoleService.createRole(createDto, employeeId);
    return ApiResponseDto.success({ id }, 'Tạo vai trò thành công');
  }

  /**
   * Cập nhật vai trò
   * @param id ID của vai trò
   * @param updateDto Dữ liệu cập nhật vai trò
   * @param employeeId ID của nhân viên thực hiện hành động
   * @returns Thông báo cập nhật thành công
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Cập nhật vai trò',
    description: 'Cập nhật thông tin vai trò theo ID'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của vai trò',
    type: String,
  })
  @ApiOkResponse({
    description: 'Vai trò đã được cập nhật thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async updateRole(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateRoleDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.adminAgentRoleService.updateRole(id, updateDto, employeeId);
    return ApiResponseDto.success(null, 'Cập nhật vai trò thành công');
  }

  /**
   * Xóa vai trò
   * @param id ID của vai trò
   * @param employeeId ID của nhân viên thực hiện hành động
   * @returns Thông báo xóa thành công
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa vai trò',
    description: 'Xóa vai trò theo ID (soft delete)'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của vai trò',
    type: String,
  })
  @ApiOkResponse({
    description: 'Vai trò đã được xóa thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_ROLE_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_DELETE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async deleteRole(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.adminAgentRoleService.deleteRole(id, employeeId);
    return ApiResponseDto.success(null, 'Xóa vai trò thành công');
  }
}
