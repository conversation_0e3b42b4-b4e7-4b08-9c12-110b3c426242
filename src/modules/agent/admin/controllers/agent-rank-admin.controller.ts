import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JWTPayload } from '@modules/auth/interfaces/jwt-payload.interface';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  AgentRankDetailDto,
  AgentRankListItemDto,
  AgentRankQueryDto,
  CreateAgentRankDto,
  CreateAgentRankResponseDto,
  UpdateAgentRankDto,
} from '../dto/agent-rank';
import { AgentRankAdminService } from '../services/agent-rank-admin.service';

/**
 * Controller xử lý các endpoint liên quan đến cấp bậc agent cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_AGENT_RANK)
@ApiExtraModels(ApiResponseDto, PaginatedResult, AgentRankDetailDto, AgentRankListItemDto, CreateAgentRankResponseDto)
@Controller('admin/agent-ranks')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class AgentRankAdminController {
  constructor(private readonly agentRankAdminService: AgentRankAdminService) { }

  /**
   * Tạo mới cấp bậc agent
   * @param createDto Thông tin cấp bậc cần tạo
   * @returns URL để upload ảnh badge
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới cấp bậc agent' })
  @ApiResponse({
    status: 201,
    description: 'Tạo mới cấp bậc thành công, trả về URL để upload ảnh badge',
    schema: ApiResponseDto.getSchema(CreateAgentRankResponseDto),
  })
  async createRank(
    @CurrentEmployee() employee: JWTPayload,
    @Body() createDto: CreateAgentRankDto
  ): Promise<ApiResponseDto<CreateAgentRankResponseDto>> {
    const result = await this.agentRankAdminService.createRank(createDto, employee.id);
    return ApiResponseDto.success(result);
  }

  /**
   * Cập nhật cấp bậc agent
   * @param id ID của cấp bậc cần cập nhật
   * @param updateDto Thông tin cần cập nhật
   * @returns Thông tin cấp bậc sau khi cập nhật
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật cấp bậc agent' })
  @ApiParam({ name: 'id', description: 'ID của cấp bậc', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật cấp bậc thành công',
    schema: ApiResponseDto.getSchema(AgentRankDetailDto),
  })
  async updateRank(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateAgentRankDto,
  ): Promise<ApiResponseDto<AgentRankDetailDto>> {
    const result = await this.agentRankAdminService.updateRank(id, updateDto);
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa cấp bậc agent
   * @param id ID của cấp bậc cần xóa
   * @returns Thông báo xóa thành công
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa cấp bậc agent' })
  @ApiParam({ name: 'id', description: 'ID của cấp bậc', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Xóa cấp bậc thành công',
    schema: ApiResponseDto.getSchema(Boolean),
  })
  async deleteRank(@Param('id', ParseIntPipe) id: number): Promise<ApiResponseDto<boolean>> {
    await this.agentRankAdminService.deleteRank(id);
    return ApiResponseDto.success(true);
  }

  /**
   * Lấy danh sách cấp bậc agent có phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách cấp bậc có phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách cấp bậc agent' })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách cấp bậc thành công',
    schema: ApiResponseDto.getPaginatedSchema(AgentRankListItemDto),
  })
  async getRanks(@Query() queryDto: AgentRankQueryDto): Promise<ApiResponseDto<PaginatedResult<AgentRankListItemDto>>> {
    const result = await this.agentRankAdminService.getRanks(queryDto);
    return ApiResponseDto.paginated(result);
  }


}
