import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';
import {
  CategoryFolderEnum,
  FileSizeEnum,
  TimeIntervalEnum,
} from '@shared/utils';
import { ImageType } from '@shared/utils/file/image-media_type.util';
import { generateS3Key } from '@shared/utils/generators';

/**
 * Helper class để xử lý URL avatar cho agent
 */
export class AvatarUrlHelper {
  /**
   * Tạo URL tải lên avatar
   * @param s3Service S3Service instance
   * @param prefix
   * @param mimeType MIME type của avatar
   * @param employeeId
   * @param key
   * @returns URL tải lên avatar
   */
  static async generateUploadUrl(
    s3Service: S3Service,
    employeeId: string,
    mimeType: string,
    prefix?: string,
    key?: string,
  ): Promise<{ url: string; key: string | null }> {
    // Xác định loại file dựa trên MIME type
    const fileType = ImageType.getType(mimeType);

    // Tạo S3 key cho file
    const s3Key = generateS3Key({
      baseFolder: employeeId,
      categoryFolder: CategoryFolderEnum.AGENT,
      prefix,
    });

    const url = await s3Service.createPresignedWithID(
      key || s3Key,
      TimeIntervalEnum.TEN_MINUTES,
      fileType,
      FileSizeEnum.TWO_MB,
    );

    // Tạo presigned URL với thời hạn 10 phút
    return {
      url,
      key: key || s3Key, // Trả về key đã có hoặc key mới tạo
    };
  }

  /**
   * Tạo URL xem avatar
   * @param cdnService CdnService instance
   * @param avatarKey Key của avatar
   * @returns URL xem avatar
   */
  static generateViewUrl(
    cdnService: CdnService,
    avatarKey: string | null | undefined,
  ): string | null {
    if (!avatarKey) {
      return null;
    }

    // Tạo URL xem với thời hạn 1 giờ
    return cdnService.generateUrlView(avatarKey, TimeIntervalEnum.ONE_HOUR);
  }
}
