import { ErrorCode } from '@common/exceptions';
import { HttpStatus } from '@nestjs/common';

/**
 * Mã lỗi liên quan đến cấp bậc agent
 */
export const AGENT_RANK_ERROR_CODES = {
  // Lỗi khi không tìm thấy cấp bậc
  AGENT_RANK_NOT_FOUND: new ErrorCode(
    10401,
    '<PERSON>hông tìm thấy cấp bậc agent',
    HttpStatus.NOT_FOUND,
  ),

  // Lỗi khi tên cấp bậc đã tồn tại
  AGENT_RANK_NAME_EXISTS: new ErrorCode(
    10402,
    'Tên cấp bậc agent đã tồn tại',
    HttpStatus.BAD_REQUEST,
  ),

  // Lỗi khi khoảng exp không hợp lệ
  AGENT_RANK_INVALID_EXP_RANGE: new ErrorCode(
    10403,
    'K<PERSON>ảng điểm kinh nghiệm không hợp lệ (min_exp phải nhỏ hơn max_exp)',
    HttpStatus.BAD_REQUEST,
  ),

  // Lỗi khi khoảng exp chồng chéo với khoảng exp khác
  AGENT_RANK_EXP_RANGE_OVERLAP: new ErrorCode(
    10404,
    'Khoảng điểm kinh nghiệm chồng chéo với khoảng điểm kinh nghiệm của cấp bậc khác',
    HttpStatus.BAD_REQUEST,
  ),

  // Lỗi khi tạo cấp bậc
  AGENT_RANK_CREATE_FAILED: new ErrorCode(
    10405,
    'Không thể tạo cấp bậc agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi khi cập nhật cấp bậc
  AGENT_RANK_UPDATE_FAILED: new ErrorCode(
    10406,
    'Không thể cập nhật cấp bậc agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi khi xóa cấp bậc
  AGENT_RANK_DELETE_FAILED: new ErrorCode(
    10407,
    'Không thể xóa cấp bậc agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi khi lấy danh sách cấp bậc
  AGENT_RANK_FETCH_FAILED: new ErrorCode(
    10408,
    'Không thể lấy danh sách cấp bậc agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
};
