import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AgentProduct } from '@modules/agent/entities';

/**
 * Repository cho AgentProduct
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến sản phẩm của agent
 */
@Injectable()
export class AgentProductRepository extends Repository<AgentProduct> {
  private readonly logger = new Logger(AgentProductRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentProduct, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AgentProduct
   * @returns SelectQueryBuilder cho AgentProduct
   */
  private createBaseQuery(): SelectQueryBuilder<AgentProduct> {
    return this.createQueryBuilder('agentProduct');
  }

  /**
   * Tì<PERSON> sản phẩm của agent theo ID
   * @param id ID của sản phẩm
   * @returns AgentProduct nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: number): Promise<AgentProduct | null> {
    return this.createBaseQuery()
      .where('agentProduct.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm sản phẩm của agent theo ID agent
   * @param agentId ID của agent
   * @returns Danh sách sản phẩm của agent
   */
  async findByAgentId(agentId: string): Promise<AgentProduct[]> {
    return this.createBaseQuery()
      .where('agentProduct.agentId = :agentId', { agentId })
      .getMany();
  }

  /**
   * Tìm sản phẩm của agent theo ID sản phẩm
   * @param productId ID của sản phẩm
   * @returns Danh sách sản phẩm của agent
   */
  async findByProductId(productId: number): Promise<AgentProduct[]> {
    return this.createBaseQuery()
      .where('agentProduct.productId = :productId', { productId })
      .getMany();
  }

  /**
   * Xóa sản phẩm của agent theo ID agent và ID sản phẩm
   * @param agentId ID của agent
   * @param productId ID của sản phẩm
   * @returns Số lượng bản ghi đã bị xóa
   */
  async deleteByAgentIdAndProductId(agentId: string, productId: number): Promise<number> {
    const result = await this.createQueryBuilder()
      .delete()
      .from(AgentProduct)
      .where('agentId = :agentId AND productId = :productId', { agentId, productId })
      .execute();
    
    return result.affected || 0;
  }
}
