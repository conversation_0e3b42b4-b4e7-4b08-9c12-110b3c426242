import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc truy vấn thống kê agent
 */
export class AgentStatisticsQueryDto {
  /**
   * Thời điểm bắt đầu (timestamp millis)
   */
  @ApiPropertyOptional({
    description: 'Thời điểm bắt đầu (timestamp millis)',
    example: 1672531200000,
  })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  startDate?: number;

  /**
   * Thời điểm kết thúc (timestamp millis)
   */
  @ApiPropertyOptional({
    description: 'Thời điểm kết thúc (timestamp millis)',
    example: 1675209600000,
  })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  endDate?: number;
}
