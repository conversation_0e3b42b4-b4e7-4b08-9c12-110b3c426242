import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO đơn giản cho danh sách agent
 */
export class AgentSimpleListDto {
  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: 'uuid-string',
  })
  id: string;

  /**
   * Avatar của agent (đã gán CDN URL)
   */
  @ApiProperty({
    description: 'Avatar của agent với CDN URL',
    example: 'https://cdn.example.com/avatars/agent-avatar.jpg',
    required: false,
  })
  avatar?: string;

  /**
   * Tên của agent
   */
  @ApiProperty({
    description: 'Tên của agent',
    example: 'Agent AI Assistant',
  })
  name: string;
}
