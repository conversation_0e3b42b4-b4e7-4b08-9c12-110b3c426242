import { ApiProperty } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';

/**
 * DTO cho việc tích hợp Website với Agent
 */
export class IntegrateWebsiteDto {
  /**
   * UUID của Website trong hệ thống cần tích hợp
   */
  @ApiProperty({
    description: 'UUID của Website trong hệ thống cần tích hợp',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890'
  })
  @IsUUID('4', {
    message: 'Website ID phải là UUID hợp lệ'
  })
  websiteId: string;
}
