import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AgentUserRepository } from '@modules/agent/repositories';
import { UserWebsiteRepository } from '@modules/integration/repositories';
import {
  IntegrateWebsiteDto,
  AgentWebsiteListDto,
  AgentWebsiteDto
} from '../dto/website';

/**
 * Service xử lý tích hợp Website với Agent
 */
@Injectable()
export class AgentWebsiteService {
  private readonly logger = new Logger(AgentWebsiteService.name);

  constructor(
    private readonly agentUserRepository: AgentUserRepository,
    private readonly userWebsiteRepository: UserWebsiteRepository,
  ) {}

  /**
   * Tích hợp Website vào Agent
   * @param agentId ID của Agent
   * @param userId ID của người dùng
   * @param dto Thông tin Website cần tích hợp (UUID trong hệ thống)
   */
  @Transactional()
  async integrateWebsite(
    agentId: string,
    userId: number,
    dto: IntegrateWebsiteDto,
  ): Promise<void> {
    try {
      // Kiểm tra Agent có tồn tại và thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Kiểm tra Website có tồn tại và thuộc về user không (sử dụng UUID)
      const website = await this.userWebsiteRepository.findByIdAndUserId(
        dto.websiteId,
        userId
      );

      if (!website) {
        throw new AppException(AGENT_ERROR_CODES.WEBSITE_NOT_FOUND);
      }

      // Kiểm tra Website đã được tích hợp với Agent khác chưa
      if (website.agentId && website.agentId !== agentId) {
        throw new AppException(AGENT_ERROR_CODES.WEBSITE_ALREADY_INTEGRATED);
      }

      // Nếu chưa tích hợp, thực hiện tích hợp
      if (!website.agentId) {
        // Cập nhật agentId cho Website
        await this.userWebsiteRepository.updateAgentId(website.id, agentId);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tích hợp Website vào Agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.WEBSITE_INTEGRATION_FAILED);
    }
  }

  /**
   * Lấy danh sách Website trong Agent
   * @param agentId ID của Agent
   * @param userId ID của người dùng
   * @returns Danh sách Website
   */
  async getWebsites(agentId: string, userId: number): Promise<AgentWebsiteListDto> {
    try {
      // Kiểm tra Agent có tồn tại và thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Lấy danh sách Website đã tích hợp với Agent
      const websites = await this.userWebsiteRepository.findAllByAgentId(agentId);

      // Chuyển đổi sang DTO
      const websiteDtos: AgentWebsiteDto[] = websites.map(website => ({
        id: website.id, // UUID trong hệ thống
        websiteName: website.websiteName,
        host: website.host,
        verify: website.verify,
        isActive: website.agentId !== null // Có agentId thì active
      }));

      return {
        websites: websiteDtos
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy danh sách Website: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.WEBSITE_LIST_FAILED);
    }
  }

  /**
   * Bật/tắt trạng thái active của Website (toggle)
   * @param agentId ID của Agent
   * @param websiteId UUID của Website trong hệ thống
   * @param userId ID của người dùng
   */
  @Transactional()
  async toggleWebsiteActive(
    agentId: string,
    websiteId: string,
    userId: number,
  ): Promise<void> {
    try {
      // Kiểm tra Agent có tồn tại và thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Kiểm tra Website có tồn tại và thuộc về user không (sử dụng UUID)
      const website = await this.userWebsiteRepository.findByIdAndUserId(websiteId, userId);

      if (!website) {
        throw new AppException(AGENT_ERROR_CODES.WEBSITE_NOT_FOUND);
      }

      // Kiểm tra Website có đang được tích hợp với Agent này không
      if (website.agentId !== agentId) {
        throw new AppException(AGENT_ERROR_CODES.WEBSITE_NOT_INTEGRATED);
      }

      // Toggle: Nếu đang active (có agentId) thì gỡ bỏ, nếu không thì tích hợp
      const newAgentId = website.agentId !== null ? null : agentId;

      // Cập nhật agentId
      await this.userWebsiteRepository.updateAgentId(website.id, newAgentId);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi toggle trạng thái Website: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.WEBSITE_UPDATE_FAILED);
    }
  }

  /**
   * Gỡ Website khỏi Agent
   * @param agentId ID của Agent
   * @param websiteId UUID của Website trong hệ thống
   * @param userId ID của người dùng
   */
  @Transactional()
  async removeWebsite(
    agentId: string,
    websiteId: string,
    userId: number,
  ): Promise<void> {
    try {
      // Kiểm tra Agent có tồn tại và thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Kiểm tra Website có tồn tại và đã tích hợp với Agent không (sử dụng UUID)
      const website = await this.userWebsiteRepository.findByIdAndUserId(websiteId, userId);

      if (!website || website.agentId !== agentId) {
        throw new AppException(AGENT_ERROR_CODES.WEBSITE_NOT_INTEGRATED);
      }

      // Gỡ bỏ agentId khỏi Website
      await this.userWebsiteRepository.updateAgentId(website.id, null);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi gỡ Website khỏi Agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.WEBSITE_REMOVE_FAILED);
    }
  }

  /**
   * Kiểm tra Agent có tồn tại và thuộc về user không
   * @param agentId ID của Agent
   * @param userId ID của người dùng
   */
  private async checkAgentOwnership(agentId: string, userId: number): Promise<void> {
    const agentUser = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);

    if (!agentUser) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }
  }
}
