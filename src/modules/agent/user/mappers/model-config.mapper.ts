import { ModelConfig } from '@modules/agent/interfaces/model-config.interface';
import { ModelConfigDto } from '../dto';

/**
 * Mapper cho các đối tượng ModelConfig
 */
export class ModelConfigMapper {
  /**
   * <PERSON><PERSON><PERSON><PERSON> đổi từ ModelConfig sang ModelConfigDto
   * @param modelConfig Cấu hình model
   * @returns ModelConfigDto
   */
  static toDto(modelConfig?: ModelConfig): ModelConfigDto {
    return {
      modelId: modelConfig?.modelId || null,
      provider_id: modelConfig?.provider_id || null,
      temperature: modelConfig?.temperature || 0.7,
      top_p: modelConfig?.top_p || 0.9,
      top_k: modelConfig?.top_k || 40,
      max_tokens: modelConfig?.max_tokens || 1000,
    };
  }
}
