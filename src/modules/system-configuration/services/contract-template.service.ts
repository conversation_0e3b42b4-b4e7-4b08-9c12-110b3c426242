import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '@/shared/services/redis.service';
import { S3Service } from '@/shared/services/s3.service';
import { SystemConfigurationService } from '../admin/service/system-configuration.service';
import { AppException, ErrorCode } from '@/common/exceptions';
import { TimeIntervalEnum } from '@/shared/utils/time/time-interval.util';

/**
 * Enum định nghĩa các loại hợp đồng mẫu
 */
export enum ContractTemplateType {
  RULE_CONTRACT_BUSINESS = 'RULE_CONTRACT_BUSINESS',
  RULE_CONTRACT_CUSTOMER = 'RULE_CONTRACT_CUSTOMER',
  AFFILIATE_CONTRACT_BUSINESS = 'AFFILIATE_CONTRACT_BUSINESS',
  AFFILIATE_CONTRACT_CUSTOMER = 'AFFILIATE_CONTRACT_CUSTOMER',
}

/**
 * Service quản lý các file hợp đồng mẫu
 */
@Injectable()
export class ContractTemplateService {
  private readonly logger = new Logger(ContractTemplateService.name);
  private readonly CACHE_PREFIX = 'contract_template:';
  private readonly CACHE_TTL = 24 * 60 * 60; // 24 giờ

  constructor(
    private readonly redisService: RedisService,
    private readonly s3Service: S3Service,
    private readonly systemConfigService: SystemConfigurationService,
  ) {}

  /**
   * Lấy file hợp đồng mẫu theo loại
   * @param type Loại hợp đồng mẫu
   * @returns Buffer chứa nội dung file hợp đồng
   */
  async getContractTemplate(type: ContractTemplateType): Promise<Buffer> {
    try {
      this.logger.log(`Lấy file hợp đồng mẫu loại: ${type}`);
      
      // Tạo key cho Redis cache
      const cacheKey = `${this.CACHE_PREFIX}${type}`;
      
      // Thử lấy từ cache trước
      const cachedTemplate = await this.redisService.get(cacheKey);
      if (cachedTemplate) {
        this.logger.log(`Đã tìm thấy file hợp đồng mẫu trong cache: ${type}`);
        return Buffer.from(cachedTemplate, 'base64');
      }
      
      // Nếu không có trong cache, lấy key từ cấu hình hệ thống
      const config = await this.systemConfigService.getActiveConfiguration();
      
      // Lấy key S3 tương ứng với loại hợp đồng
      let s3Key: string | null = null;
      switch (type) {
        case ContractTemplateType.RULE_CONTRACT_BUSINESS:
          s3Key = config.initialRuleContractBusiness;
          break;
        case ContractTemplateType.RULE_CONTRACT_CUSTOMER:
          s3Key = config.initialRuleContractCustomer;
          break;
        case ContractTemplateType.AFFILIATE_CONTRACT_BUSINESS:
          s3Key = config.initialAffiliateContractBusiness;
          break;
        case ContractTemplateType.AFFILIATE_CONTRACT_CUSTOMER:
          s3Key = config.initialAffiliateContractCustomer;
          break;
        default:
          throw new AppException(
            ErrorCode.VALIDATION_ERROR,
            `Loại hợp đồng mẫu không hợp lệ: ${type}`,
          );
      }
      
      if (!s3Key) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy key S3 cho loại hợp đồng mẫu: ${type}`,
        );
      }
      
      // Tải file từ S3
      this.logger.log(`Tải file hợp đồng mẫu từ S3: ${s3Key}`);
      const fileBytes = await this.s3Service.downloadFileAsBytes(s3Key);
      
      // Chuyển đổi Uint8Array thành Buffer
      const fileBuffer = Buffer.from(fileBytes);
      
      // Lưu vào cache
      await this.redisService.setWithExpiry(
        cacheKey,
        fileBuffer.toString('base64'),
        this.CACHE_TTL,
      );
      
      return fileBuffer;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy file hợp đồng mẫu ${type}: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        `Lỗi khi lấy file hợp đồng mẫu: ${error.message}`,
      );
    }
  }

  /**
   * Xóa cache của một loại hợp đồng mẫu
   * @param type Loại hợp đồng mẫu
   */
  async clearTemplateCache(type: ContractTemplateType): Promise<void> {
    try {
      const cacheKey = `${this.CACHE_PREFIX}${type}`;
      await this.redisService.del(cacheKey);
      this.logger.log(`Đã xóa cache cho loại hợp đồng mẫu: ${type}`);
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa cache cho loại hợp đồng mẫu ${type}: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Xóa cache của tất cả các loại hợp đồng mẫu
   */
  async clearAllTemplateCache(): Promise<void> {
    try {
      const keys = await this.redisService.keys(`${this.CACHE_PREFIX}*`);
      if (keys.length > 0) {
        await this.redisService.del(keys);
      }
      this.logger.log(`Đã xóa cache cho tất cả các loại hợp đồng mẫu`);
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa cache cho tất cả các loại hợp đồng mẫu: ${error.message}`,
        error.stack,
      );
    }
  }
}
