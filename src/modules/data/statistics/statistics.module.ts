import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '@modules/user/entities/user.entity';
import { KnowledgeFile } from '@modules/data/knowledge-files/entities/knowledge-file.entity';
import { Media } from '@modules/data/media/entities/media.entity';
import { Url } from '@modules/data/url/entities/url.entity';
import { VectorStore } from '@modules/data/knowledge-files/entities/vector-store.entity';
import { StatisticsController } from './controllers/statistics.controller';
import { StatisticsService } from './services/statistics.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, KnowledgeFile, Media, Url, VectorStore]),
  ],
  controllers: [StatisticsController],
  providers: [StatisticsService],
  exports: [StatisticsService],
})
export class StatisticsModule {}
