import { Injectable, Logger } from '@nestjs/common';
import { Repository, DataSource } from 'typeorm';
import { CrawlSession } from '../entities/crawl-session.entity';

/**
 * Repository cho CrawlSession với các query tối ưu
 */
@Injectable()
export class CrawlSessionRepository extends Repository<CrawlSession> {
  private readonly logger = new Logger(CrawlSessionRepository.name);

  constructor(private dataSource: DataSource) {
    super(CrawlSession, dataSource.createEntityManager());
  }

  /**
   * Tìm session theo ID
   * @param sessionId ID của session
   * @returns CrawlSession hoặc null
   */
  async findSessionById(sessionId: string): Promise<CrawlSession | null> {
    try {
      this.logger.debug(`Finding session by ID: ${sessionId}`);

      const session = await this.createQueryBuilder('session')
        .where('session.id = :sessionId', { sessionId })
        .andWhere('session.isActive = :isActive', { isActive: true })
        .getOne();

      if (session) {
        this.logger.debug(`Session found: ${sessionId}`);
      } else {
        this.logger.debug(`Session not found: ${sessionId}`);
      }

      return session;
    } catch (error) {
      this.logger.error(`Error finding session ${sessionId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy danh sách sessions của user với phân trang
   * @param userId ID của user
   * @param page Trang hiện tại (bắt đầu từ 1)
   * @param limit Số lượng records mỗi trang
   * @param status Lọc theo trạng thái (optional)
   * @returns Danh sách sessions và tổng số
   */
  async findUserSessions(
    userId: number,
    page: number = 1,
    limit: number = 20,
    status?: string
  ): Promise<{ sessions: CrawlSession[]; total: number }> {
    try {
      this.logger.debug(`Finding sessions for user ${userId}, page ${page}, limit ${limit}`);

      const queryBuilder = this.createQueryBuilder('session')
        .where('session.userId = :userId', { userId })
        .andWhere('session.isActive = :isActive', { isActive: true });

      if (status) {
        queryBuilder.andWhere('session.status = :status', { status });
      }

      queryBuilder
        .orderBy('session.createdAt', 'DESC')
        .skip((page - 1) * limit)
        .take(limit);

      const [sessions, total] = await queryBuilder.getManyAndCount();

      this.logger.debug(`Found ${sessions.length}/${total} sessions for user ${userId}`);

      return { sessions, total };
    } catch (error) {
      this.logger.error(`Error finding user sessions: ${error.message}`);
      throw error;
    }
  }

  /**
   * Lấy session đang chạy gần nhất của user
   * @param userId ID của user
   * @returns CrawlSession đang chạy hoặc null
   */
  async findLatestRunningSession(userId: number): Promise<CrawlSession | null> {
    try {
      this.logger.debug(`Finding latest running session for user ${userId}`);

      const session = await this.createQueryBuilder('session')
        .where('session.userId = :userId', { userId })
        .andWhere('session.status = :status', { status: 'running' })
        .andWhere('session.isActive = :isActive', { isActive: true })
        .orderBy('session.createdAt', 'DESC')
        .getOne();

      if (session) {
        this.logger.debug(`Latest running session found: ${session.id}`);
      } else {
        this.logger.debug(`No running session found for user ${userId}`);
      }

      return session;
    } catch (error) {
      this.logger.error(`Error finding latest running session: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cập nhật tiến độ của session
   * @param sessionId ID của session
   * @param progress Thông tin tiến độ mới
   * @returns true nếu cập nhật thành công
   */
  async updateSessionProgress(
    sessionId: string,
    progress: CrawlSession['progress']
  ): Promise<boolean> {
    try {
      this.logger.debug(`Updating progress for session ${sessionId}`);

      const result = await this.createQueryBuilder()
        .update(CrawlSession)
        .set({
          progress,
          updatedAt: new Date()
        })
        .where('id = :sessionId', { sessionId })
        .andWhere('isActive = :isActive', { isActive: true })
        .execute();

      const success = (result.affected ?? 0) > 0;

      if (success) {
        this.logger.debug(`Progress updated for session ${sessionId}`);
      } else {
        this.logger.warn(`No session updated for ${sessionId}`);
      }

      return success;
    } catch (error) {
      this.logger.error(`Error updating session progress: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cập nhật trạng thái và kết quả của session
   * @param sessionId ID của session
   * @param status Trạng thái mới
   * @param result Kết quả crawl
   * @param endTime Thời gian kết thúc
   * @returns true nếu cập nhật thành công
   */
  async updateSessionResult(
    sessionId: string,
    status: CrawlSession['status'],
    result: CrawlSession['result'],
    endTime?: number
  ): Promise<boolean> {
    try {
      this.logger.debug(`Updating result for session ${sessionId} with status ${status}`);

      const updateData: Partial<CrawlSession> = {
        status,
        result,
        updatedAt: new Date()
      };

      if (endTime) {
        updateData.endTime = endTime;
      }

      const updateResult = await this.createQueryBuilder()
        .update(CrawlSession)
        .set(updateData)
        .where('id = :sessionId', { sessionId })
        .andWhere('isActive = :isActive', { isActive: true })
        .execute();

      const success = (updateResult.affected ?? 0) > 0;

      if (success) {
        this.logger.debug(`Result updated for session ${sessionId}`);
      } else {
        this.logger.warn(`No session updated for ${sessionId}`);
      }

      return success;
    } catch (error) {
      this.logger.error(`Error updating session result: ${error.message}`);
      throw error;
    }
  }

  /**
   * Thêm lỗi vào session
   * @param sessionId ID của session
   * @param error Thông tin lỗi
   * @returns true nếu thêm thành công
   */
  async addSessionError(
    sessionId: string,
    error: {
      type: string;
      message: string;
      url: string;
      timestamp: number;
    }
  ): Promise<boolean> {
    try {
      this.logger.debug(`Adding error to session ${sessionId}`);

      // Lấy session hiện tại
      const session = await this.findSessionById(sessionId);
      if (!session) {
        this.logger.warn(`Session not found: ${sessionId}`);
        return false;
      }

      // Thêm lỗi vào danh sách
      const errors = session.errors || [];
      errors.push(error);

      // Cập nhật session
      const result = await this.createQueryBuilder()
        .update(CrawlSession)
        .set({
          errors,
          updatedAt: new Date()
        })
        .where('id = :sessionId', { sessionId })
        .execute();

      const success = result.affected > 0;

      if (success) {
        this.logger.debug(`Error added to session ${sessionId}`);
      }

      return success;
    } catch (error) {
      this.logger.error(`Error adding session error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xóa mềm session (đánh dấu isActive = false)
   * @param sessionId ID của session
   * @returns true nếu xóa thành công
   */
  async softDeleteSession(sessionId: string): Promise<boolean> {
    try {
      this.logger.debug(`Soft deleting session ${sessionId}`);

      const result = await this.createQueryBuilder()
        .update(CrawlSession)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where('id = :sessionId', { sessionId })
        .execute();

      const success = result.affected > 0;

      if (success) {
        this.logger.debug(`Session soft deleted: ${sessionId}`);
      } else {
        this.logger.warn(`No session deleted for ${sessionId}`);
      }

      return success;
    } catch (error) {
      this.logger.error(`Error soft deleting session: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cleanup sessions cũ (xóa sessions hoàn thành > 7 ngày)
   * @returns Số lượng sessions đã cleanup
   */
  async cleanupOldSessions(): Promise<number> {
    try {
      this.logger.debug(`Starting cleanup of old sessions`);

      const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);

      const result = await this.createQueryBuilder()
        .update(CrawlSession)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where('status IN (:...statuses)', { statuses: ['completed', 'error', 'cancelled'] })
        .andWhere('endTime < :sevenDaysAgo', { sevenDaysAgo })
        .andWhere('isActive = :isActive', { isActive: true })
        .execute();

      const cleanedCount = result.affected || 0;
      this.logger.debug(`Cleaned up ${cleanedCount} old sessions`);

      return cleanedCount;
    } catch (error) {
      this.logger.error(`Error cleaning up old sessions: ${error.message}`);
      throw error;
    }
  }
}
