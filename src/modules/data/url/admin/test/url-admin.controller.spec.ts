// <PERSON><PERSON><PERSON> ngh<PERSON>a các interface thay vì class để tránh trùng lặp
interface SortDirectionType {
  ASC: string;
  DESC: string;
}

interface CreateUrlAdminDtoType {
  url: string;
  title: string;
  content: string;
  type?: string;
  tags?: string[];
  ownedBy: number;
  isActive?: boolean;
}

interface UpdateUrlAdminDtoType {
  url?: string;
  title?: string;
  content?: string;
  type?: string;
  tags?: string[];
  ownedBy?: number;
  isActive?: boolean;
}

interface FindAllUrlAdminDtoType {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: string;
  keyword?: string;
  type?: string;
  tags?: string[];
  userId?: number;
  isActive?: boolean;
}

// Khởi tạo các đối tượng từ interface
const SortDirectionEnumAdmin: SortDirectionType = {
  ASC: 'ASC',
  DESC: 'DESC'
};

// Mock các module cần thiết
const ApiResponseMockAdmin = {
  success: jest.fn().mockImplementation((data, message) => ({
    success: true,
    data,
    message
  })),
  created: jest.fn().mockImplementation((data, message) => ({
    success: true,
    data,
    message
  }))
};

// Mock UrlAdminController
class UrlAdminControllerTest {
  constructor(private readonly urlAdminService: any) {}

  async findAll(queryParams: FindAllUrlAdminDtoType) {
    const result = await this.urlAdminService.findAllUrls(queryParams);
    return ApiResponseMockAdmin.success(result);
  }

  async findOne(id: string) {
    const result = await this.urlAdminService.findUrlById(id);
    return ApiResponseMockAdmin.success(result);
  }

  async create(createUrlDto: CreateUrlAdminDtoType) {
    const result = await this.urlAdminService.createUrl(createUrlDto);
    return ApiResponseMockAdmin.created(result, 'URL đã được tạo thành công');
  }

  async update(id: string, updateUrlDto: UpdateUrlAdminDtoType) {
    const result = await this.urlAdminService.updateUrl(id, updateUrlDto);
    return ApiResponseMockAdmin.success(result, 'URL đã được cập nhật thành công');
  }

  async remove(id: string) {
    await this.urlAdminService.deleteUrl(id);
    return ApiResponseMockAdmin.success(null, 'URL đã được xóa thành công');
  }

  async updateStatus(id: string, isActive: string) {
    const isActiveBoolean = ['true', '1', 'yes', 'on'].includes(isActive.toLowerCase());
    const result = await this.urlAdminService.updateUrlStatus(id, isActiveBoolean);
    return ApiResponseMockAdmin.success(result, 'Trạng thái URL đã được cập nhật thành công');
  }

  async toggleStatus(id: string) {
    const result = await this.urlAdminService.toggleUrlStatus(id);
    return ApiResponseMockAdmin.success(result, 'Trạng thái URL đã được đảo ngược thành công');
  }
}

// Mock UrlAdminService
class UrlAdminServiceTest {
  findUrlById = jest.fn();
  findAllUrls = jest.fn();
  searchUrls = jest.fn();
  createUrl = jest.fn();
  updateUrl = jest.fn();
  deleteUrl = jest.fn();
  updateUrlStatus = jest.fn();
  toggleUrlStatus = jest.fn();
}

// Rename ApiResponseMock to ApiResponse for tests
const ApiResponseAdmin = ApiResponseMockAdmin;

describe('Kiểm thử Controller URL của admin', () => {
  let controller: UrlAdminControllerTest;
  let service: UrlAdminServiceTest;

  const mockUrl = {
    id: 'test-id',
    url: 'https://example.com',
    title: 'Test URL',
    content: 'Test content',
    type: 'web',
    tags: ['test'],
    ownedBy: 1,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    urlEmbedding: null,
    titleEmbedding: null,
    contentEmbedding: null,
    isActive: true,
  };

  const mockUrls = [
    { ...mockUrl },
    {
      ...mockUrl,
      id: 'test-id-2',
      url: 'https://example.com/2',
      title: 'Test URL 2',
      isActive: false,
    },
  ];

  const mockPaginatedResult = {
    items: mockUrls,
    meta: {
      totalItems: 2,
      itemCount: 2,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  beforeEach(() => {
    // Tạo service và controller
    service = new UrlAdminServiceTest();

    // Setup mock methods
    service.findUrlById.mockResolvedValue(mockUrl);
    service.findAllUrls.mockResolvedValue(mockPaginatedResult);
    service.searchUrls.mockResolvedValue(mockUrls);
    service.createUrl.mockResolvedValue(mockUrl);
    service.updateUrl.mockResolvedValue(mockUrl);
    service.deleteUrl.mockResolvedValue(undefined);
    service.updateUrlStatus.mockResolvedValue({ ...mockUrl, isActive: false });
    service.toggleUrlStatus.mockResolvedValue({ ...mockUrl, isActive: false });

    // Tạo controller với service đã mock
    controller = new UrlAdminControllerTest(service);
  });

  it('Controller phải được định nghĩa', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll - Lấy danh sách URL', () => {
    it('Phải trả về danh sách URL có phân trang', async () => {
      const result = await controller.findAll({});
      expect(result).toEqual(ApiResponseAdmin.success(mockPaginatedResult));
      expect(service.findAllUrls).toHaveBeenCalled();
    });

    it('Phải xử lý được các tham số tìm kiếm', async () => {
      const queryParams: FindAllUrlAdminDtoType = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirectionEnumAdmin.DESC,
        keyword: 'test',
        type: 'web',
        tags: ['nestjs', 'tutorial'],
        userId: 1,
        isActive: true
      };

      await controller.findAll(queryParams);
      expect(service.findAllUrls).toHaveBeenCalledWith(queryParams);
    });
  });

  describe('findOne - Lấy thông tin chi tiết URL', () => {
    it('Phải trả về thông tin URL theo ID', async () => {
      const result = await controller.findOne('test-id');
      expect(result).toEqual(ApiResponseAdmin.success(mockUrl));
      expect(service.findUrlById).toHaveBeenCalledWith('test-id');
    });
  });

  describe('create - Tạo URL mới', () => {
    it('Phải tạo được URL mới', async () => {
      const createUrlDto: CreateUrlAdminDtoType = {
        url: 'https://example.com',
        title: 'Test URL',
        content: 'Test content',
        type: 'web',
        tags: ['test'],
        ownedBy: 1,
        isActive: true
      };

      const result = await controller.create(createUrlDto);
      expect(result).toEqual(ApiResponseAdmin.created(mockUrl, 'URL đã được tạo thành công'));
      expect(service.createUrl).toHaveBeenCalledWith(createUrlDto);
    });
  });

  describe('update - Cập nhật URL', () => {
    it('Phải cập nhật được URL đã tồn tại', async () => {
      const updateUrlDto: UpdateUrlAdminDtoType = {
        title: 'Updated URL',
        content: 'Updated content',
        isActive: false
      };

      const result = await controller.update('test-id', updateUrlDto);
      expect(result).toEqual(ApiResponseAdmin.success(mockUrl, 'URL đã được cập nhật thành công'));
      expect(service.updateUrl).toHaveBeenCalledWith('test-id', updateUrlDto);
    });
  });

  describe('remove - Xóa URL', () => {
    it('Phải xóa được URL đã tồn tại', async () => {
      const result = await controller.remove('test-id');
      expect(result).toEqual(ApiResponseAdmin.success(null, 'URL đã được xóa thành công'));
      expect(service.deleteUrl).toHaveBeenCalledWith('test-id');
    });
  });

  describe('updateStatus - Cập nhật trạng thái URL', () => {
    it('Phải cập nhật được trạng thái URL từ true sang false', async () => {
      service.updateUrlStatus.mockResolvedValueOnce({ ...mockUrl, isActive: false });
      const result = await controller.updateStatus('test-id', 'false');
      expect(result).toEqual(ApiResponseAdmin.success({ ...mockUrl, isActive: false }, 'Trạng thái URL đã được cập nhật thành công'));
      expect(service.updateUrlStatus).toHaveBeenCalledWith('test-id', false);
    });

    it('Phải cập nhật được trạng thái URL từ false sang true', async () => {
      service.updateUrlStatus.mockResolvedValueOnce({ ...mockUrl, isActive: true });
      const result = await controller.updateStatus('test-id', 'true');
      expect(result).toEqual(ApiResponseAdmin.success({ ...mockUrl, isActive: true }, 'Trạng thái URL đã được cập nhật thành công'));
      expect(service.updateUrlStatus).toHaveBeenCalledWith('test-id', true);
    });

    it('Phải xử lý được các giá trị khác nhau cho isActive', async () => {
      service.updateUrlStatus.mockResolvedValueOnce({ ...mockUrl, isActive: true });
      await controller.updateStatus('test-id', 'yes');
      expect(service.updateUrlStatus).toHaveBeenCalledWith('test-id', true);

      service.updateUrlStatus.mockResolvedValueOnce({ ...mockUrl, isActive: true });
      await controller.updateStatus('test-id', '1');
      expect(service.updateUrlStatus).toHaveBeenCalledWith('test-id', true);

      service.updateUrlStatus.mockResolvedValueOnce({ ...mockUrl, isActive: false });
      await controller.updateStatus('test-id', 'no');
      expect(service.updateUrlStatus).toHaveBeenCalledWith('test-id', false);
    });
  });

  describe('toggleStatus - Đảo ngược trạng thái URL', () => {
    it('Phải đảo ngược được trạng thái URL từ true sang false', async () => {
      service.toggleUrlStatus.mockResolvedValueOnce({ ...mockUrl, isActive: false });
      const result = await controller.toggleStatus('test-id');
      expect(result).toEqual(ApiResponseAdmin.success({ ...mockUrl, isActive: false }, 'Trạng thái URL đã được đảo ngược thành công'));
      expect(service.toggleUrlStatus).toHaveBeenCalledWith('test-id');
    });

    it('Phải đảo ngược được trạng thái URL từ false sang true', async () => {
      service.toggleUrlStatus.mockResolvedValueOnce({ ...mockUrl, isActive: true });
      const result = await controller.toggleStatus('test-id');
      expect(result).toEqual(ApiResponseAdmin.success({ ...mockUrl, isActive: true }, 'Trạng thái URL đã được đảo ngược thành công'));
      expect(service.toggleUrlStatus).toHaveBeenCalledWith('test-id');
    });
  });
});
