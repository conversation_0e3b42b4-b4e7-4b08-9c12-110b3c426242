import { UrlAdminException, UrlAdminErrorCode } from '../exceptions/url-admin.exception';

describe('<PERSON><PERSON><PERSON> thử ngoại lệ URL của admin', () => {
  it('<PERSON><PERSON><PERSON> tạo được ngoại lệ với mã lỗi và thông báo chính xác', () => {
    const errorCode = UrlAdminErrorCode.URL_NOT_FOUND;
    const errorMessage = 'URL không tồn tại';
    const exception = new UrlAdminException(errorCode, errorMessage);

    expect(exception).toBeDefined();
    expect(exception.message).toContain(errorMessage);
  });

  it('<PERSON>ải có đầy đủ các mã lỗi cần thiết', () => {
    expect(UrlAdminErrorCode.URL_NOT_FOUND).toBeDefined();
    expect(UrlAdminErrorCode.URL_INVALID_PARAMS).toBeDefined();
    expect(UrlAdminErrorCode.URL_FETCH_FAILED).toBeDefined();
    expect(UrlAdminErrorCode.URL_SEARCH_FAILED).toBeDefined();
    expect(UrlAdminErrorCode.URL_INVALID_SEARCH_PARAMS).toBeDefined();
    expect(UrlAdminErrorCode.URL_INVALID_FORMAT).toBeDefined();
    expect(UrlAdminErrorCode.URL_ALREADY_EXISTS).toBeDefined();
    expect(UrlAdminErrorCode.URL_INVALID_CONTENT).toBeDefined();
    expect(UrlAdminErrorCode.URL_CREATION_FAILED).toBeDefined();
    expect(UrlAdminErrorCode.URL_UPDATE_FAILED).toBeDefined();
    expect(UrlAdminErrorCode.URL_DELETE_FAILED).toBeDefined();
    expect(UrlAdminErrorCode.USER_NOT_FOUND).toBeDefined();
  });
});
