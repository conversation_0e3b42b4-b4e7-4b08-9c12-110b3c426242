import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUrl, MaxLength, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class CreateUrlAdminDto {
  @ApiProperty({
    description: 'Đường dẫn URL',
    example: 'https://example.com/article/how-to-use-nestjs',
  })
  @IsNotEmpty()
  @IsUrl()
  @MaxLength(2048)
  url: string;

  @ApiProperty({
    description: 'Tiêu đề của tài nguyên URL',
    example: 'Hướng dẫn sử dụng NestJS từ cơ bản đến nâng cao',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(512)
  title: string;

  @ApiProperty({
    description: 'Nội dung về tài nguyên URL',
    example: 'Bài viết này hướng dẫn cách sử dụng NestJS...',
  })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({
    description: 'Loại tài nguyên URL',
    example: 'web',
    required: false,
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiProperty({
    description: 'Các thẻ phân loại URL',
    example: ['nestjs', 'tutorial', 'backend'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  tags?: string[];

  @ApiProperty({
    description: 'ID người sở hữu URL',
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  ownedBy: number;

  @ApiProperty({
    description: 'Trạng thái kích hoạt',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean = true;
}
