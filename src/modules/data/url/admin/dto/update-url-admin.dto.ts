import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUrl, MaxLength, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class UpdateUrlAdminDto {
  @ApiProperty({
    description: 'Đường dẫn URL',
    example: 'https://example.com/article/how-to-use-nestjs',
    required: false,
  })
  @IsOptional()
  @IsUrl()
  @MaxLength(2048)
  url?: string;

  @ApiProperty({
    description: 'Tiêu đề của tài nguyên URL',
    example: 'Hướng dẫn sử dụng NestJS từ cơ bản đến nâng cao',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(512)
  title?: string;

  @ApiProperty({
    description: 'Nội dung về tài nguyên URL',
    example: 'Bài viết này hướng dẫn cách sử dụng NestJS...',
    required: false,
  })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({
    description: 'Loại tài nguyên URL',
    example: 'web',
    required: false,
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiProperty({
    description: 'Các thẻ phân loại URL',
    example: ['nestjs', 'tutorial', 'backend'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  tags?: string[];

  @ApiProperty({
    description: 'ID người sở hữu URL',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  ownedBy?: number;

  @ApiProperty({
    description: 'Trạng thái kích hoạt',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}
