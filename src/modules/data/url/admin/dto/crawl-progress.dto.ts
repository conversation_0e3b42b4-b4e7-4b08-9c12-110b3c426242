import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsInt, Min, Max } from 'class-validator';
import { Transform } from 'class-transformer';

/**
 * DTO cho việc lấy tiến độ crawl
 */
export class GetCrawlProgressDto {
  @ApiProperty({
    description: 'ID của session crawl',
    example: 'crawl_1_1748405343422'
  })
  @IsString()
  sessionId: string;
}

/**
 * DTO cho việc lấy danh sách sessions
 */
export class GetCrawlSessionsDto {
  @ApiPropertyOptional({
    description: '<PERSON><PERSON> trang (bắt đầu từ 1)',
    example: 1,
    minimum: 1
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Số lượng records mỗi trang',
    example: 20,
    minimum: 1,
    maximum: 100
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> theo trạng thái',
    enum: ['running', 'completed', 'error', 'cancelled'],
    example: 'running'
  })
  @IsOptional()
  @IsEnum(['running', 'completed', 'error', 'cancelled'])
  status?: 'running' | 'completed' | 'error' | 'cancelled';
}

/**
 * DTO response cho tiến độ crawl
 */
export class CrawlProgressResponseDto {
  @ApiProperty({
    description: 'ID của session crawl',
    example: 'crawl_1_1748405343422'
  })
  sessionId: string;

  @ApiProperty({
    description: 'Trạng thái của session',
    enum: ['running', 'completed', 'error', 'cancelled', 'not_found'],
    example: 'running'
  })
  status: 'running' | 'completed' | 'error' | 'cancelled' | 'not_found';

  @ApiProperty({
    description: 'Thông tin tiến độ chi tiết',
    type: 'object'
  })
  progress: {
    percentage: number;
    processedUrls: number;
    totalUrls: number;
    successfulUrls: number;
    failedUrls: number;
    currentDepth: number;
    currentUrl?: string;
    estimatedTimeRemaining?: number;
    elapsedTime: number;
  };

  @ApiPropertyOptional({
    description: 'Danh sách lỗi (nếu có)',
    type: 'array'
  })
  errors?: Array<{
    type: string;
    message: string;
    url: string;
    timestamp: number;
  }>;

  @ApiProperty({
    description: 'Thời gian bắt đầu (timestamp)',
    example: 1748405343422
  })
  startTime: number;

  @ApiPropertyOptional({
    description: 'Thời gian kết thúc (timestamp)',
    example: 1748405400000
  })
  endTime?: number;

  @ApiPropertyOptional({
    description: 'URL gốc được crawl',
    example: 'https://example.com'
  })
  url?: string;

  @ApiPropertyOptional({
    description: 'Cấu hình crawl',
    type: 'object'
  })
  config?: {
    depth: number;
    maxUrls: number;
    ignoreRobotsTxt?: boolean;
  };

  @ApiPropertyOptional({
    description: 'Kết quả cuối cùng (nếu hoàn thành)',
    type: 'object'
  })
  result?: {
    urlsProcessed: number;
    urlsSaved: number;
    message: string;
    errors?: string[];
  };
}

/**
 * DTO response cho danh sách sessions
 */
export class CrawlSessionsResponseDto {
  @ApiProperty({
    description: 'Danh sách sessions',
    type: [CrawlSessionItemDto]
  })
  sessions: CrawlSessionItemDto[];

  @ApiProperty({
    description: 'Tổng số sessions',
    example: 50
  })
  total: number;

  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1
  })
  page: number;

  @ApiProperty({
    description: 'Số lượng mỗi trang',
    example: 20
  })
  limit: number;

  @ApiProperty({
    description: 'Tổng số trang',
    example: 3
  })
  totalPages: number;
}

/**
 * DTO cho item trong danh sách sessions
 */
export class CrawlSessionItemDto {
  @ApiProperty({
    description: 'ID của session',
    example: 'crawl_1_1748405343422'
  })
  sessionId: string;

  @ApiProperty({
    description: 'URL được crawl',
    example: 'https://example.com'
  })
  url: string;

  @ApiProperty({
    description: 'Trạng thái',
    enum: ['running', 'completed', 'error', 'cancelled'],
    example: 'completed'
  })
  status: 'running' | 'completed' | 'error' | 'cancelled';

  @ApiProperty({
    description: 'Tiến độ (%)',
    example: 100
  })
  percentage: number;

  @ApiProperty({
    description: 'Số URLs đã xử lý',
    example: 25
  })
  processedUrls: number;

  @ApiProperty({
    description: 'Tổng số URLs',
    example: 25
  })
  totalUrls: number;

  @ApiProperty({
    description: 'Thời gian bắt đầu',
    example: '2025-01-28T10:30:00Z'
  })
  startTime: string;

  @ApiPropertyOptional({
    description: 'Thời gian kết thúc',
    example: '2025-01-28T10:35:00Z'
  })
  endTime?: string;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: '2025-01-28T10:30:00Z'
  })
  createdAt: string;

  @ApiPropertyOptional({
    description: 'Cấu hình crawl',
    type: 'object'
  })
  config?: {
    depth: number;
    maxUrls: number;
  };

  @ApiPropertyOptional({
    description: 'Kết quả (nếu hoàn thành)',
    type: 'object'
  })
  result?: {
    urlsProcessed: number;
    urlsSaved: number;
    message: string;
  };
}
