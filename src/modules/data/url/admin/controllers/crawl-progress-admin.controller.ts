import {
  Controller,
  Get,
  Post,
  Param,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtEmployeeGuard, PermissionsGuard } from '@/common';
import { CurrentEmployee } from '@/common/decorators/current-employee.decorator';
import { Roles } from '@/common/decorators/roles.decorator';
import { ApiResponseDto } from '@/common/response/api-response-dto';
import { UrlAdminService } from '../services/url-admin.service';
import {
  GetCrawlProgressDto,
  GetCrawlSessionsDto,
  CrawlProgressResponseDto,
  CrawlSessionsResponseDto,
} from '../dto/crawl-progress.dto';

/**
 * Controller cho việc theo dõi tiến độ crawl URL - Admin
 */
@ApiTags('Admin - Crawl Progress')
@Controller('admin/urls/crawl')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@ApiBearerAuth('JWT-auth')
export class CrawlProgressAdminController {
  constructor(private readonly urlAdminService: UrlAdminService) {}

  /**
   * Lấy tiến độ crawl theo sessionId
   */
  @Get('progress/:sessionId')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy tiến độ crawl URL theo sessionId',
    description: 'Lấy thông tin chi tiết về tiến độ crawl URL đang chạy hoặc đã hoàn thành',
  })
  @ApiParam({
    name: 'sessionId',
    description: 'ID của session crawl',
    example: 'crawl_1_1748405343422',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy tiến độ crawl thành công',
    type: ApiResponseDto<CrawlProgressResponseDto>,
    schema: {
      example: {
        code: 200,
        message: 'Success',
        result: {
          sessionId: 'crawl_1_1748405343422',
          status: 'running',
          progress: {
            percentage: 75,
            processedUrls: 15,
            totalUrls: 20,
            successfulUrls: 12,
            failedUrls: 3,
            currentDepth: 2,
            currentUrl: 'https://example.com/page-15',
            estimatedTimeRemaining: 30,
            elapsedTime: 120,
          },
          errors: [],
          startTime: 1748405343422,
          url: 'https://example.com',
          config: {
            depth: 3,
            maxUrls: 20,
            ignoreRobotsTxt: false,
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Session không tồn tại',
    schema: {
      example: {
        code: 404,
        message: 'Session không tồn tại',
        result: {
          sessionId: 'crawl_1_1748405343422',
          status: 'not_found',
          progress: {
            percentage: 0,
            processedUrls: 0,
            totalUrls: 0,
            successfulUrls: 0,
            failedUrls: 0,
            currentDepth: 0,
            elapsedTime: 0,
          },
          startTime: 0,
        },
      },
    },
  })
  async getCrawlProgress(
    @Param('sessionId') sessionId: string,
  ): Promise<ApiResponseDto<CrawlProgressResponseDto>> {
    const progress = await this.urlAdminService.getCrawlProgress(sessionId);
    return ApiResponseDto.success(progress);
  }

  /**
   * Lấy danh sách sessions crawl của admin
   */
  @Get('sessions')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy danh sách sessions crawl của admin',
    description: 'Lấy danh sách tất cả sessions crawl đã thực hiện bởi admin với phân trang',
  })
  @ApiQuery({
    name: 'page',
    description: 'Số trang (bắt đầu từ 1)',
    required: false,
    example: 1,
    type: 'number',
  })
  @ApiQuery({
    name: 'limit',
    description: 'Số lượng records mỗi trang (tối đa 100)',
    required: false,
    example: 20,
    type: 'number',
  })
  @ApiQuery({
    name: 'status',
    description: 'Lọc theo trạng thái session',
    required: false,
    enum: ['running', 'completed', 'error', 'cancelled'],
    example: 'completed',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách sessions thành công',
    type: ApiResponseDto<CrawlSessionsResponseDto>,
    schema: {
      example: {
        code: 200,
        message: 'Success',
        result: {
          sessions: [
            {
              sessionId: 'crawl_1_1748405343422',
              url: 'https://example.com',
              status: 'completed',
              percentage: 100,
              processedUrls: 20,
              totalUrls: 20,
              startTime: '2025-01-28T10:30:00Z',
              endTime: '2025-01-28T10:35:00Z',
              createdAt: '2025-01-28T10:30:00Z',
              config: {
                depth: 3,
                maxUrls: 20,
              },
              result: {
                urlsProcessed: 20,
                urlsSaved: 18,
                message: 'Crawl hoàn thành thành công',
              },
            },
          ],
          total: 50,
          page: 1,
          limit: 20,
          totalPages: 3,
        },
      },
    },
  })
  async getCrawlSessions(
    @CurrentEmployee('id') employeeId: number,
    @Query() queryParams: GetCrawlSessionsDto,
  ): Promise<ApiResponseDto<CrawlSessionsResponseDto>> {
    const sessions = await this.urlAdminService.getCrawlSessions(employeeId, queryParams);
    return ApiResponseDto.success(sessions);
  }

  /**
   * Hủy session crawl đang chạy
   */
  @Post('sessions/:sessionId/cancel')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Hủy session crawl đang chạy',
    description: 'Hủy session crawl đang chạy và đánh dấu trạng thái là cancelled',
  })
  @ApiParam({
    name: 'sessionId',
    description: 'ID của session crawl cần hủy',
    example: 'crawl_1_1748405343422',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: 'Hủy session thành công',
    schema: {
      example: {
        code: 200,
        message: 'Session đã được hủy thành công',
        result: {
          success: true,
          sessionId: 'crawl_1_1748405343422',
          message: 'Session đã bị hủy bởi admin',
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Session không tồn tại',
  })
  @ApiResponse({
    status: 403,
    description: 'Không có quyền hủy session này',
  })
  @ApiResponse({
    status: 400,
    description: 'Chỉ có thể hủy session đang chạy',
  })
  async cancelCrawlSession(
    @CurrentEmployee('id') employeeId: number,
    @Param('sessionId') sessionId: string,
  ): Promise<ApiResponseDto<{ success: boolean; sessionId: string; message: string }>> {
    const success = await this.urlAdminService.cancelCrawlSession(sessionId, employeeId);
    
    return ApiResponseDto.success({
      success,
      sessionId,
      message: 'Session đã được hủy thành công',
    });
  }

  /**
   * Lấy session đang chạy gần nhất của admin
   */
  @Get('sessions/latest')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Lấy session crawl đang chạy gần nhất',
    description: 'Lấy thông tin session crawl đang chạy gần nhất của admin (nếu có)',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy session gần nhất thành công',
    type: ApiResponseDto<CrawlProgressResponseDto>,
  })
  @ApiResponse({
    status: 404,
    description: 'Không có session nào đang chạy',
  })
  async getLatestRunningSession(
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<CrawlProgressResponseDto | null>> {
    // Tìm session đang chạy gần nhất
    const sessions = await this.urlAdminService.getCrawlSessions(employeeId, {
      page: 1,
      limit: 1,
      status: 'running',
    });

    if (sessions.sessions.length === 0) {
      return ApiResponseDto.success(null, 'Không có session nào đang chạy');
    }

    const latestSession = sessions.sessions[0];
    const progress = await this.urlAdminService.getCrawlProgress(latestSession.sessionId);
    
    return ApiResponseDto.success(progress);
  }
}
