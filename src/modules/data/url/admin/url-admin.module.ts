import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { UrlAdminController } from './controllers/url-admin.controller';
import { UrlAdminService } from './services/url-admin.service';
import { Url } from '../entities/url.entity';
import { UrlRepository } from '../repositories';
import { AuthModule } from '@modules/auth/auth.module';
import { ProxyRotationService } from '../shared/services/proxy-rotation.service';
import { UserAgentRotationService } from '../shared/services/user-agent-rotation.service';
import { AdvancedCrawlerService } from '../shared/services/advanced-crawler.service';

@Module({
  imports: [TypeOrmModule.forFeature([Url]), HttpModule, AuthModule],
  controllers: [UrlAdminController],
  providers: [
    UrlAdminService,
    UrlRepository,
    ProxyRotationService,
    UserAgentRotationService,
    AdvancedCrawlerService
  ],
  exports: [UrlAdminService, AdvancedCrawlerService],
})
export class UrlAdminModule {}
