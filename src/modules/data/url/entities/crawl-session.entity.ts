import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

/**
 * Entity cho việc theo dõi session crawl URL
 * Lưu trữ thông tin tiến độ và trạng thái crawl
 */
@Entity('crawl_sessions')
@Index(['userId', 'createdAt'])
@Index(['status', 'createdAt'])
export class CrawlSession {
  /**
   * ID duy nhất của session crawl
   * Format: crawl_{userId}_{timestamp}
   */
  @PrimaryColumn({ type: 'varchar', length: 100 })
  id: string;

  /**
   * ID của user thực hiện crawl
   */
  @Column({ name: 'user_id', type: 'int' })
  @Index()
  userId: number;

  /**
   * URL gốc được crawl
   */
  @Column({ type: 'varchar', length: 2000 })
  url: string;

  /**
   * Trạng thái của session crawl
   * - running: <PERSON><PERSON> chạy
   * - completed: <PERSON><PERSON><PERSON> thành
   * - error: Lỗi
   * - cancelled: Bị hủy
   */
  @Column({
    type: 'enum',
    enum: ['running', 'completed', 'error', 'cancelled'],
    default: 'running'
  })
  status: 'running' | 'completed' | 'error' | 'cancelled';

  /**
   * Thông tin tiến độ crawl (JSON)
   * Chứa: totalUrls, processedUrls, successfulUrls, failedUrls, currentDepth, etc.
   */
  @Column({ type: 'jsonb', nullable: true })
  progress: {
    totalUrls: number;
    processedUrls: number;
    successfulUrls: number;
    failedUrls: number;
    currentDepth: number;
    currentUrl?: string;
    estimatedTimeRemaining?: number;
    percentage: number;
  };

  /**
   * Cấu hình crawl (JSON)
   * Chứa: depth, maxUrls, ignoreRobotsTxt, etc.
   */
  @Column({ type: 'jsonb' })
  config: {
    depth: number;
    maxUrls: number;
    ignoreRobotsTxt?: boolean;
    [key: string]: any;
  };

  /**
   * Kết quả crawl (JSON)
   * Chứa: urlsProcessed, urlsSaved, message, errors
   */
  @Column({ type: 'jsonb', nullable: true })
  result: {
    urlsProcessed: number;
    urlsSaved: number;
    message: string;
    errors?: string[];
  };

  /**
   * Danh sách lỗi gặp phải (JSON)
   */
  @Column({ type: 'jsonb', nullable: true })
  errors: Array<{
    type: string;
    message: string;
    url: string;
    timestamp: number;
  }>;

  /**
   * Thời gian bắt đầu crawl
   */
  @Column({ name: 'start_time', type: 'bigint' })
  startTime: number;

  /**
   * Thời gian kết thúc crawl
   */
  @Column({ name: 'end_time', type: 'bigint', nullable: true })
  endTime: number;

  /**
   * Thời gian tạo record
   */
  @CreateDateColumn({ name: 'created_at', type: 'timestamp' })
  createdAt: Date;

  /**
   * Thời gian cập nhật record
   */
  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp' })
  updatedAt: Date;

  /**
   * Có phải session đang hoạt động không
   */
  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  /**
   * ID của job trong Bull queue
   */
  @Column({ name: 'job_id', type: 'varchar', length: 100, nullable: true })
  jobId: string;

  /**
   * Metadata bổ sung
   */
  @Column({ type: 'jsonb', nullable: true })
  metadata: {
    userAgent?: string;
    ipAddress?: string;
    source?: 'admin' | 'user';
    [key: string]: any;
  };
}
