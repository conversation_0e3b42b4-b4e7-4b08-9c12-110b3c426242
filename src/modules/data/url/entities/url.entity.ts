import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng url_data trong cơ sở dữ liệu
 * Bảng lưu thông tin tài nguyên URL
 */
@Entity('url_data')
export class Url {
  @PrimaryGeneratedColumn('uuid', { name: 'id' })
  id: string;

  @Column({ name: 'url', length: 2048 })
  url: string;

  /**
   * Vector embedding cho URL (vd: 1536 chiều)
   * Dùng pgvector: kiểu 'vector' và chỉ định dimension
   */
  @Column({
    name: 'url_embedding',
    type: 'simple-array', // Sử dụng simple-array cho TypeScript
    nullable: true
  })
  urlEmbedding: number[] | null;

  @Column({ name: 'title', length: 512 })
  title: string;

  @Column({
    name: 'title_embedding',
    type: 'simple-array', // Sử dụng simple-array cho TypeScript
    nullable: true
  })
  titleEmbedding: number[] | null;

  @Column({ name: 'content', type: 'text' })
  content: string;

  @Column({
    name: 'content_embedding',
    type: 'simple-array', // Sử dụng simple-array cho TypeScript
    nullable: true
  })
  contentEmbedding: number[] | null;

  @Column({ name: 'type', length: 50, nullable: true })
  type: string; // web, video, image, audio, document, other

  @Column({ name: 'tags', type: 'jsonb', nullable: true })
  tags: any;

  @Column({ name: 'owned_by' })
  ownedBy: number;

  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint"
  })
  createdAt: number;

  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint"
  })
  updatedAt: number;

  @Column({
    name: 'is_active',
    type: 'boolean',
    default: true
  })
  isActive: boolean;
}
