import { Injectable, Logger } from '@nestjs/common';
import { Process, Processor } from '@nestjs/bull';
import { CrawlUrlJobName, QueueName } from '@shared/queue/queue.constants';
import { Job } from 'bull';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Url } from '../../entities/url.entity';
import { CrawlSession } from '../../entities/crawl-session.entity';
import { UrlRepository } from '../../repositories';
import { CrawlSessionRepository } from '../../repositories/crawl-session.repository';
import { CrawlDto } from '../dto/crawl.dto';
import { ExtractedMetadata } from '../interfaces/extracted-metadata.interface';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import * as cheerio from 'cheerio';
import { backOff } from 'exponential-backoff';
import { URL_ERROR_CODES } from 'src/modules/data/url/exceptions';
import { AppException } from '@/common';
import { ProxyRotationService } from '../../shared/services/proxy-rotation.service';
import { UserAgentRotationService } from '../../shared/services/user-agent-rotation.service';
import { AdvancedCrawlerService } from '../../shared/services/advanced-crawler.service';

// Enum cho các loại lỗi crawl
enum CrawlErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  CLIENT_ERROR = 'CLIENT_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  PARSING_ERROR = 'PARSING_ERROR',
  ROBOTS_BLOCKED = 'ROBOTS_BLOCKED',
  INVALID_URL = 'INVALID_URL',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

// Interface cho thông tin lỗi chi tiết
interface CrawlError {
  type: CrawlErrorType;
  message: string;
  url: string;
  statusCode?: number;
  retryable: boolean;
  retryAfter?: number; // seconds
}

// Interface cho progress tracking
interface CrawlProgress {
  totalUrls: number;
  processedUrls: number;
  successfulUrls: number;
  failedUrls: number;
  currentDepth: number;
  startTime: number;
  estimatedTimeRemaining?: number;
  currentUrl?: string;
  errors: CrawlError[];
}

// Type cho progress callback
type ProgressCallback = (progress: CrawlProgress) => void;

@Injectable()
@Processor(QueueName.CRAWL_URL)
export class CrawlWorker {
  private readonly logger = new Logger(CrawlWorker.name);
  private progressCallbacks = new Map<string, ProgressCallback>(); // sessionId -> callback

  // Cache cho robots.txt
  private robotsCache = new Map<string, { allowed: boolean; expiry: number }>(); // domain -> cache
  private readonly ROBOTS_CACHE_TTL = 24 * 60 * 60 * 1000; // 24 giờ

  // Cache cho DNS lookups
  private dnsCache = new Map<string, { ip: string; expiry: number }>(); // hostname -> cache
  private readonly DNS_CACHE_TTL = 60 * 60 * 1000; // 1 giờ

  // Cache cho metadata
  private metadataCache = new Map<
    string,
    { metadata: ExtractedMetadata; expiry: number }
  >(); // url -> cache
  private readonly METADATA_CACHE_TTL = 30 * 60 * 1000; // 30 phút

  // Rate limiting per domain - Tối ưu cho crawling nhanh hơn với environment variables
  private domainRateLimits = new Map<
    string,
    { lastRequest: number; requestCount: number; resetTime: number }
  >(); // domain -> rate limit info
  private readonly RATE_LIMIT_WINDOW = 60 * 1000; // 1 phút
  private readonly MAX_REQUESTS_PER_MINUTE =
    parseInt(process.env.CRAWL_RATE_LIMIT_REQUESTS_PER_MINUTE || '30') || 30; // Configurable từ env
  private readonly MIN_DELAY_BETWEEN_REQUESTS =
    parseInt(process.env.CRAWL_MIN_DELAY_BETWEEN_REQUESTS || '200') || 200; // Configurable từ env

  constructor(
    @InjectRepository(Url)
    private readonly urlRepository: Repository<Url>,
    private readonly urlCustomRepository: UrlRepository,
    private readonly httpService: HttpService,
    private readonly proxyRotationService: ProxyRotationService,
    private readonly userAgentRotationService: UserAgentRotationService,
    private readonly advancedCrawlerService: AdvancedCrawlerService,
  ) {
    // Cleanup cache mỗi 30 phút
    setInterval(
      () => {
        this.cleanupExpiredCache();
      },
      30 * 60 * 1000,
    );
  }

  @Process(CrawlUrlJobName.CRAWL_URL)
  async crawlUrlJob(job: Job<{userId: number, crawlDto: CrawlDto}>) {
    return await this.crawlUrl(job.data.userId, job.data.crawlDto);
  }

  /**
   * Đăng ký progress callback cho một session crawl
   * @param sessionId ID của session crawl
   * @param callback Callback function để nhận progress updates
   */
  registerProgressCallback(
    sessionId: string,
    callback: ProgressCallback,
  ): void {
    this.progressCallbacks.set(sessionId, callback);
  }

  /**
   * Hủy đăng ký progress callback
   * @param sessionId ID của session crawl
   */
  unregisterProgressCallback(sessionId: string): void {
    this.progressCallbacks.delete(sessionId);
  }

  /**
   * Crawl URL và các URL con để lấy metadata từ thẻ head với xử lý song song và progress tracking
   * @param userId ID của người dùng
   * @param crawlDto Thông tin URL cần crawl
   * @param sessionId ID session để track progress (optional)
   * @returns Danh sách các URL với metadata
   */
  async crawlUrl(
    userId: number,
    crawlDto: CrawlDto,
    sessionId?: string,
  ): Promise<{
    status: string;
    message: string;
    urlsProcessed?: number;
    urlsSaved?: number;
    // results?: ExtractedMetadata[],
    errors?: string[];
  }> {
    // Mảng lưu trữ các lỗi gặp phải
    const errors: string[] = [];

    try {
      this.logger.log(`===== BẮT ĐẦU CRAWL URL =====`);
      this.logger.log(
        `User: ${userId}, URL: ${crawlDto.url}, Độ sâu: ${crawlDto.depth}`,
      );

      // Kiểm tra URL có hợp lệ không
      try {
        const urlObj = new URL(crawlDto.url);
        this.logger.log(`URL hợp lệ: ${urlObj.href}`);

        // Chuẩn hóa URL
        const normalizedUrl = this.normalizeUrl(crawlDto.url);
        if (normalizedUrl !== crawlDto.url) {
          this.logger.log(`URL đã được chuẩn hóa: ${normalizedUrl}`);
          crawlDto.url = normalizedUrl;
        }

        // Kiểm tra robots.txt nếu không bỏ qua
        if (!crawlDto.ignoreRobotsTxt) {
          const isAllowed = await this.checkRobotsPermission(crawlDto.url);
          if (!isAllowed) {
            const robotsMsg = `URL không được phép crawl theo robots.txt: ${crawlDto.url}`;
            this.logger.error(robotsMsg);
            errors.push(robotsMsg);
            throw new AppException(URL_ERROR_CODES.URL_CRAWL_FAILED, robotsMsg);
          }
        } else {
          this.logger.log(`Bỏ qua kiểm tra robots.txt theo yêu cầu người dùng`);
        }
      } catch (error) {
        if (error instanceof AppException) {
          throw error;
        }

        const errorMsg = `URL không đúng định dạng: ${error.message}`;
        this.logger.error(errorMsg);
        errors.push(errorMsg);
        throw new AppException(URL_ERROR_CODES.URL_INVALID_FORMAT, errorMsg);
      }

      // Giới hạn số lượng URL tối đa và concurrency - Tối ưu cho tốc độ với environment variables
      const MAX_URLS = crawlDto.maxUrls || 20;
      const CONCURRENCY_LIMIT =
        parseInt(process.env.CRAWL_CONCURRENCY_LIMIT || '5') || 5; // Configurable từ env
      this.logger.log(
        `Giới hạn tối đa: ${MAX_URLS} URLs, độ sâu tối đa: ${crawlDto.depth}, concurrency: ${CONCURRENCY_LIMIT}`,
      );

      // Khởi tạo các biến cần thiết
      const visitedUrls = new Set<string>();
      const urlsToVisit: Array<{ url: string; depth: number }> = [
        { url: crawlDto.url, depth: 0 },
      ];
      const processedUrls: ExtractedMetadata[] = [];
      const crawlErrors: CrawlError[] = [];
      let totalCrawledUrls = 0; // Tổng số URLs đã crawl thành công (có hoặc không có metadata)

      // Khởi tạo progress tracking
      const startTime = Date.now();
      const progress: CrawlProgress = {
        totalUrls: 1, // Bắt đầu với 1 URL
        processedUrls: 0,
        successfulUrls: 0,
        failedUrls: 0,
        currentDepth: 0,
        startTime,
        errors: crawlErrors,
      };

      // Tạo session ID nếu chưa có
      const trackingSessionId = sessionId || `crawl_${userId}_${Date.now()}`;

      this.logger.log(
        `===== BẮT ĐẦU VÒNG LẶP CRAWL URL VỚI XỬ LÝ SONG SONG [${trackingSessionId}] =====`,
      );

      // Cập nhật progress ban đầu
      if (sessionId) {
        this.updateProgress(trackingSessionId, progress);
      }

      // Crawl URLs với xử lý song song
      while (urlsToVisit.length > 0 && visitedUrls.size < MAX_URLS) {
        // Cập nhật total URLs estimate
        progress.totalUrls = Math.min(
          visitedUrls.size + urlsToVisit.length,
          MAX_URLS,
        );

        // Lấy batch URLs để xử lý song song
        const currentBatch: Array<{ url: string; depth: number }> = [];

        // Lấy tối đa CONCURRENCY_LIMIT URLs chưa được xử lý
        while (
          currentBatch.length < CONCURRENCY_LIMIT &&
          urlsToVisit.length > 0
        ) {
          const currentItem = urlsToVisit.shift();
          if (!currentItem) continue;

          const { url } = currentItem;

          // Bỏ qua nếu URL đã được xử lý
          if (visitedUrls.has(url)) continue;

          // Đánh dấu URL đã được xử lý
          visitedUrls.add(url);
          currentBatch.push(currentItem);
        }

        if (currentBatch.length === 0) break;

        // Cập nhật current depth và URL
        progress.currentDepth = Math.max(
          ...currentBatch.map((item) => item.depth),
        );
        progress.currentUrl = currentBatch[0].url;

        this.logger.log(
          `Xử lý batch ${currentBatch.length} URLs song song: ${currentBatch.map((item) => item.url).join(', ')}`,
        );

        // Xử lý batch URLs song song với smart crawling
        const batchResults = await this.processConcurrentUrlsWithSmartCrawling(
          currentBatch,
          userId,
          crawlDto,
          visitedUrls,
          urlsToVisit,
          errors,
        );

        // Cập nhật progress và tổng số URLs đã crawl
        progress.processedUrls += currentBatch.length;
        progress.successfulUrls += batchResults.length;
        progress.failedUrls += currentBatch.length - batchResults.length;
        totalCrawledUrls += batchResults.length; // Tất cả URLs trong batchResults đều được coi là thành công

        // Lưu batch metadata vào database
        if (batchResults.length > 0) {
          try {
            const savedCount = await this.saveBatchMetadata(
              userId,
              batchResults,
            );
            this.logger.log(
              `💾 Batch save: ${savedCount}/${batchResults.length} metadata đã lưu vào database thành công`,
            );

            if (savedCount < batchResults.length) {
              this.logger.warn(
                `⚠️ Một số metadata không được lưu thành công: ${batchResults.length - savedCount}`,
              );
            }
          } catch (saveError) {
            this.logger.error(
              `❌ Lỗi khi lưu batch metadata: ${saveError.message}`,
            );
            errors.push(`Lỗi lưu database: ${saveError.message}`);
          }
        }

        // Thêm kết quả thành công vào danh sách
        processedUrls.push(...batchResults);

        this.logger.log(
          `Hoàn thành batch: ${batchResults.length}/${currentBatch.length} URLs thành công`,
        );

        // Cập nhật progress callback
        if (sessionId) {
          this.updateProgress(trackingSessionId, progress);
        }
      }

      // Tạo thông báo kết quả
      let resultMessage = '';
      if (totalCrawledUrls > 0) {
        const savedCount = processedUrls.length;
        if (savedCount > 0) {
          resultMessage = `Đã crawl thành công ${totalCrawledUrls} URL từ ${crawlDto.url} với độ sâu ${crawlDto.depth} (giới hạn: ${MAX_URLS} URLs). Đã lưu ${savedCount} URL có metadata vào database.`;
        } else {
          resultMessage = `Đã crawl thành công ${totalCrawledUrls} URL từ ${crawlDto.url} với độ sâu ${crawlDto.depth} (giới hạn: ${MAX_URLS} URLs). Không có metadata để lưu vào database (có thể do anti-bot protection).`;
        }
      } else {
        resultMessage = `Không crawl được URL nào từ ${crawlDto.url} với độ sâu ${crawlDto.depth} (giới hạn: ${MAX_URLS} URLs)`;
      }

      this.logger.log(`===== KẾT THÚC CRAWL URL [${trackingSessionId}] =====`);
      this.logger.log(resultMessage);

      // Final progress update
      if (sessionId) {
        progress.currentUrl = undefined;
        this.updateProgress(trackingSessionId, progress);
        // Cleanup callback sau 30 giây
        setTimeout(() => {
          this.unregisterProgressCallback(trackingSessionId);
        }, 30000);
      }

      // Browser cleanup sau khi crawl hoàn thành
      try {
        this.logger.log(`🧹 Performing browser cleanup after crawl completion`);
        await this.advancedCrawlerService.closeBrowser();
      } catch (cleanupError) {
        this.logger.warn(`Browser cleanup warning: ${cleanupError.message}`);
      }

      return {
        status: 'success',
        message: resultMessage,
        urlsProcessed: totalCrawledUrls, // Số URLs đã crawl thành công
        urlsSaved: processedUrls.length, // Số URLs đã lưu vào database
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      // Xử lý lỗi
      const errorMsg =
        error instanceof AppException
          ? error.message
          : `Lỗi không xác định: ${error.message}`;
      this.logger.error(`Crawl URL thất bại: ${errorMsg}`);

      // Cleanup progress tracking on error
      if (sessionId) {
        const trackingSessionId = sessionId || `crawl_${userId}_${Date.now()}`;
        this.unregisterProgressCallback(trackingSessionId);
      }

      // Emergency browser cleanup on error
      try {
        this.logger.log(`🚨 Emergency browser cleanup due to error`);
        await this.advancedCrawlerService.closeBrowser();
      } catch (cleanupError) {
        this.logger.warn(`Emergency cleanup warning: ${cleanupError.message}`);
      }

      return {
        status: 'error',
        message: errorMsg,
        urlsProcessed: 0,
        errors: errors.length > 0 ? [...errors, errorMsg] : [errorMsg],
      };
    }
  }

  /**
   * Cleanup expired cache entries
   */
  private cleanupExpiredCache(): void {
    const now = Date.now();

    // Cleanup robots cache
    for (const [domain, cache] of this.robotsCache.entries()) {
      if (cache.expiry < now) {
        this.robotsCache.delete(domain);
      }
    }

    // Cleanup DNS cache
    for (const [hostname, cache] of this.dnsCache.entries()) {
      if (cache.expiry < now) {
        this.dnsCache.delete(hostname);
      }
    }

    // Cleanup metadata cache
    for (const [url, cache] of this.metadataCache.entries()) {
      if (cache.expiry < now) {
        this.metadataCache.delete(url);
      }
    }

    // Cleanup rate limit data
    for (const [domain, rateLimit] of this.domainRateLimits.entries()) {
      if (rateLimit.resetTime < now) {
        this.domainRateLimits.delete(domain);
      }
    }

    this.logger.debug(
      `Cache cleanup completed. Robots: ${this.robotsCache.size}, DNS: ${this.dnsCache.size}, Metadata: ${this.metadataCache.size}, RateLimit: ${this.domainRateLimits.size}`,
    );
  }

  /**
   * Kiểm tra và áp dụng rate limiting cho domain
   * @param url URL cần kiểm tra
   * @returns Số milliseconds cần chờ trước khi có thể request, 0 nếu có thể request ngay
   */
  private checkRateLimit(url: string): number {
    try {
      const domain = new URL(url).hostname;
      const now = Date.now();

      let rateLimit = this.domainRateLimits.get(domain);

      if (!rateLimit) {
        // Lần đầu tiên request domain này
        rateLimit = {
          lastRequest: now,
          requestCount: 1,
          resetTime: now + this.RATE_LIMIT_WINDOW,
        };
        this.domainRateLimits.set(domain, rateLimit);
        return 0;
      }

      // Reset counter nếu đã hết window
      if (now >= rateLimit.resetTime) {
        rateLimit.requestCount = 1;
        rateLimit.lastRequest = now;
        rateLimit.resetTime = now + this.RATE_LIMIT_WINDOW;
        return 0;
      }

      // Kiểm tra số lượng requests trong window
      if (rateLimit.requestCount >= this.MAX_REQUESTS_PER_MINUTE) {
        const waitTime = rateLimit.resetTime - now;
        this.logger.warn(
          `Rate limit exceeded for domain ${domain}, wait ${waitTime}ms`,
        );
        return waitTime;
      }

      // Kiểm tra delay tối thiểu giữa các requests
      const timeSinceLastRequest = now - rateLimit.lastRequest;
      if (timeSinceLastRequest < this.MIN_DELAY_BETWEEN_REQUESTS) {
        const waitTime = this.MIN_DELAY_BETWEEN_REQUESTS - timeSinceLastRequest;
        this.logger.debug(
          `Min delay not met for domain ${domain}, wait ${waitTime}ms`,
        );
        return waitTime;
      }

      // Cập nhật rate limit
      rateLimit.requestCount++;
      rateLimit.lastRequest = now;

      return 0;
    } catch (error) {
      this.logger.warn(
        `Error checking rate limit for ${url}: ${error.message}`,
      );
      return 0; // Cho phép request nếu có lỗi
    }
  }

  /**
   * Chờ theo rate limit nếu cần thiết
   * @param url URL cần request
   */
  private async waitForRateLimit(url: string): Promise<void> {
    const waitTime = this.checkRateLimit(url);
    if (waitTime > 0) {
      this.logger.log(
        `Waiting ${waitTime}ms for rate limit on domain: ${new URL(url).hostname}`,
      );
      await new Promise((resolve) => setTimeout(resolve, waitTime));

      // Kiểm tra lại sau khi chờ
      const remainingWait = this.checkRateLimit(url);
      if (remainingWait > 0) {
        await new Promise((resolve) => setTimeout(resolve, remainingWait));
      }
    }
  }

  /**
   * Cập nhật progress và gọi callback nếu có
   * @param sessionId ID của session crawl
   * @param progress Thông tin progress hiện tại
   */
  private updateProgress(sessionId: string, progress: CrawlProgress): void {
    const callback = this.progressCallbacks.get(sessionId);
    if (callback) {
      // Tính toán estimated time remaining
      const elapsedTime = Date.now() - progress.startTime;
      if (progress.processedUrls > 0) {
        const avgTimePerUrl = elapsedTime / progress.processedUrls;
        const remainingUrls = progress.totalUrls - progress.processedUrls;
        progress.estimatedTimeRemaining = Math.round(
          (avgTimePerUrl * remainingUrls) / 1000,
        ); // seconds
      }

      try {
        callback(progress);
      } catch (error) {
        this.logger.warn(
          `Error in progress callback for session ${sessionId}: ${error.message}`,
        );
      }
    }

    // Log progress
    const percentage =
      progress.totalUrls > 0
        ? Math.round((progress.processedUrls / progress.totalUrls) * 100)
        : 0;
    this.logger.log(
      `Progress [${sessionId}]: ${percentage}% (${progress.processedUrls}/${progress.totalUrls}) - Success: ${progress.successfulUrls}, Failed: ${progress.failedUrls}`,
    );
  }

  /**
   * Phân loại lỗi crawl để xử lý thông minh
   * @param error Lỗi gốc
   * @param url URL gây lỗi
   * @returns Thông tin lỗi đã phân loại
   */
  private categorizeError(error: any, url: string): CrawlError {
    // Lỗi timeout
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      return {
        type: CrawlErrorType.TIMEOUT_ERROR,
        message: `Timeout khi truy cập ${url}`,
        url,
        retryable: true,
        retryAfter: 5,
      };
    }

    // Lỗi network
    if (
      error.code === 'ENOTFOUND' ||
      error.code === 'ECONNREFUSED' ||
      error.code === 'ECONNRESET'
    ) {
      return {
        type: CrawlErrorType.NETWORK_ERROR,
        message: `Lỗi kết nối mạng: ${error.code}`,
        url,
        retryable: true,
        retryAfter: 10,
      };
    }

    // Lỗi HTTP response
    if (error.response?.status) {
      const status = error.response.status;

      if (status === 429) {
        const retryAfter =
          parseInt(error.response.headers['retry-after']) || 60;
        return {
          type: CrawlErrorType.RATE_LIMIT_ERROR,
          message: `Rate limit exceeded cho ${url}`,
          url,
          statusCode: status,
          retryable: true,
          retryAfter,
        };
      }

      if (status >= 400 && status < 500) {
        return {
          type: CrawlErrorType.CLIENT_ERROR,
          message: `Lỗi client ${status}: ${error.response.statusText}`,
          url,
          statusCode: status,
          retryable:
            status === 408 ||
            status === 409 ||
            status === 423 ||
            status === 424, // Chỉ retry một số lỗi 4xx
        };
      }

      if (status >= 500) {
        return {
          type: CrawlErrorType.SERVER_ERROR,
          message: `Lỗi server ${status}: ${error.response.statusText}`,
          url,
          statusCode: status,
          retryable: true,
          retryAfter: 30,
        };
      }
    }

    // Lỗi parsing
    if (
      error.message?.includes('parse') ||
      error.message?.includes('invalid')
    ) {
      return {
        type: CrawlErrorType.PARSING_ERROR,
        message: `Lỗi phân tích dữ liệu: ${error.message}`,
        url,
        retryable: false,
      };
    }

    // Lỗi không xác định
    return {
      type: CrawlErrorType.UNKNOWN_ERROR,
      message: `Lỗi không xác định: ${error.message}`,
      url,
      retryable: true,
      retryAfter: 15,
    };
  }

  /**
   * Tạo retry strategy thông minh dựa trên loại lỗi
   * @param crawlError Thông tin lỗi đã phân loại
   * @returns Retry configuration
   */
  private createRetryStrategy(crawlError: CrawlError) {
    if (!crawlError.retryable) {
      return {
        numOfAttempts: 1,
        retry: () => false,
      };
    }

    const baseConfig = {
      startingDelay: (crawlError.retryAfter || 1) * 1000,
      timeMultiple: 2,
      maxDelay: 60000,
      delayFirstAttempt: false,
      jitter: 'full' as const,
    };

    switch (crawlError.type) {
      case CrawlErrorType.RATE_LIMIT_ERROR:
        return {
          ...baseConfig,
          numOfAttempts: 2, // Ít retry hơn cho rate limit
          startingDelay: (crawlError.retryAfter || 60) * 1000,
          retry: (error: any, attemptNumber: number) => {
            this.logger.warn(
              `Rate limit retry ${attemptNumber} for ${crawlError.url}`,
            );
            return attemptNumber < 2;
          },
        };

      case CrawlErrorType.TIMEOUT_ERROR:
        return {
          ...baseConfig,
          numOfAttempts: 3,
          retry: (error: any, attemptNumber: number) => {
            this.logger.warn(
              `Timeout retry ${attemptNumber} for ${crawlError.url}`,
            );
            return attemptNumber < 3;
          },
        };

      case CrawlErrorType.SERVER_ERROR:
        return {
          ...baseConfig,
          numOfAttempts: 4,
          retry: (error: any, attemptNumber: number) => {
            this.logger.warn(
              `Server error retry ${attemptNumber} for ${crawlError.url}`,
            );
            return attemptNumber < 4;
          },
        };

      case CrawlErrorType.NETWORK_ERROR:
        return {
          ...baseConfig,
          numOfAttempts: 3,
          startingDelay: 5000, // Chờ lâu hơn cho network error
          retry: (error: any, attemptNumber: number) => {
            this.logger.warn(
              `Network error retry ${attemptNumber} for ${crawlError.url}`,
            );
            return attemptNumber < 3;
          },
        };

      default:
        return {
          ...baseConfig,
          numOfAttempts: 2,
          retry: (error: any, attemptNumber: number) => {
            this.logger.warn(
              `Generic retry ${attemptNumber} for ${crawlError.url}`,
            );
            return attemptNumber < 2;
          },
        };
    }
  }

  /**
   * Trích xuất domain chính từ hostname
   * Ví dụ: www.example.com -> example.com
   * @param hostname Hostname cần trích xuất
   * @returns Domain chính
   */
  private extractMainDomain(hostname: string): string {
    // Loại bỏ www. nếu có
    let domain = hostname.replace(/^www\./, '');

    // Trả về domain chính
    return domain;
  }

  /**
   * Kiểm tra quyền crawl theo robots.txt với cache
   * @param url URL cần kiểm tra
   * @returns true nếu được phép crawl, false nếu không
   */
  private async checkRobotsPermission(url: string): Promise<boolean> {
    try {
      const parsedUrl = new URL(url);
      const domain = parsedUrl.hostname;
      const robotsUrl = `${parsedUrl.protocol}//${domain}/robots.txt`;

      // Kiểm tra cache trước
      const cached = this.robotsCache.get(domain);
      if (cached && cached.expiry > Date.now()) {
        this.logger.debug(`Robots.txt cache hit for domain: ${domain}`);
        return cached.allowed;
      }

      this.logger.log(`Kiểm tra robots.txt: ${robotsUrl}`);

      // Sử dụng thư viện exponential-backoff để retry
      const robotsTxt = await backOff(
        async () => {
          const response = await firstValueFrom(
            this.httpService.get(robotsUrl, {
              timeout: 5000,
              headers: {
                'User-Agent': 'Mozilla/5.0',
              },
            }),
          );
          return response.data;
        },
        {
          // Cấu hình backoff
          numOfAttempts: 3,
          startingDelay: 1000, // 1 giây
          timeMultiple: 2, // Nhân đôi thời gian chờ mỗi lần retry
          maxDelay: 5000, // Tối đa 5 giây
          delayFirstAttempt: false, // Không delay lần đầu tiên
          jitter: 'full', // Thêm jitter để tránh "thundering herd problem"
          retry: (error: any, attemptNumber: number) => {
            // Log lỗi
            this.logger.warn(
              `Attempt ${attemptNumber} failed for robots.txt ${robotsUrl}: ${error.message}`,
            );

            // Nếu là lỗi 4xx (trừ 429), không retry
            if (
              error.response &&
              error.response.status >= 400 &&
              error.response.status < 500 &&
              error.response.status !== 429
            ) {
              return false;
            }

            return true;
          },
        },
      );

      let allowed = true;

      // Phân tích robots.txt đơn giản
      // Kiểm tra xem có bị disallow toàn bộ không
      if (robotsTxt.includes('Disallow: /')) {
        this.logger.warn(`Robots.txt không cho phép crawl: ${url}`);
        allowed = false;
      } else {
        // Kiểm tra xem URL cụ thể có bị disallow không
        const urlPath = parsedUrl.pathname;
        const disallowLines = robotsTxt
          .split('\n')
          .filter((line: string) => line.trim().startsWith('Disallow:'))
          .map((line: string) => line.split('Disallow:')[1].trim());

        for (const disallowPath of disallowLines) {
          if (disallowPath && urlPath.startsWith(disallowPath)) {
            this.logger.warn(
              `Robots.txt không cho phép crawl path: ${urlPath}`,
            );
            allowed = false;
            break;
          }
        }
      }

      // Cache kết quả
      this.robotsCache.set(domain, {
        allowed,
        expiry: Date.now() + this.ROBOTS_CACHE_TTL,
      });

      this.logger.debug(
        `Robots.txt cached for domain: ${domain}, allowed: ${allowed}`,
      );
      return allowed;
    } catch (error) {
      // Nếu không tìm thấy robots.txt, cho phép crawl và cache kết quả
      this.logger.warn(`Không thể kiểm tra robots.txt: ${error.message}`);

      const parsedUrl = new URL(url);
      const domain = parsedUrl.hostname;

      // Cache cho phép crawl khi không có robots.txt
      this.robotsCache.set(domain, {
        allowed: true,
        expiry: Date.now() + this.ROBOTS_CACHE_TTL,
      });

      return true;
    }
  }

  /**
   * Chuẩn hóa URL
   * @param url URL cần chuẩn hóa
   * @returns URL đã chuẩn hóa
   */
  private normalizeUrl(url: string): string {
    try {
      const urlObj = new URL(url);

      // Loại bỏ fragment
      urlObj.hash = '';

      // Loại bỏ các query params không cần thiết
      const paramsToRemove = [
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'fbclid',
        'gclid',
      ];
      const params = new URLSearchParams(urlObj.search);

      paramsToRemove.forEach((param) => {
        if (params.has(param)) {
          params.delete(param);
        }
      });

      urlObj.search = params.toString();

      // Đảm bảo URL kết thúc bằng / nếu không có path
      if (urlObj.pathname === '') {
        urlObj.pathname = '/';
      }

      return urlObj.toString();
    } catch (error) {
      // Nếu không thể chuẩn hóa, trả về URL gốc
      return url;
    }
  }

  /**
   * Thực hiện HTTP request với retry
   * @param url URL cần request
   * @param maxRetries Số lần retry tối đa
   * @returns Response data
   */
  private async fetchWithRetry(url: string, maxRetries = 3): Promise<any> {
    try {
      // Sử dụng thư viện exponential-backoff
      const result = await backOff(
        async () => {
          this.logger.log(`Fetching data from URL: ${url}`);

          // Tạo headers với User-Agent rotation
          const headers = this.userAgentRotationService.createHeaders({
            browser: 'chrome',
            referer: url,
          });

          // Tạo config với proxy rotation
          const requestConfig = this.proxyRotationService.createProxyConfig({
            headers,
            timeout: 60000, // 60 giây timeout - Tăng để tránh timeout
            maxRedirects: 5, // Cho phép tối đa 5 lần chuyển hướng
          });

          const response = await firstValueFrom(
            this.httpService.get(url, requestConfig),
          );

          return response.data;
        },
        {
          // Cấu hình backoff
          numOfAttempts: maxRetries,
          startingDelay: 1000, // 1 giây
          timeMultiple: 2, // Nhân đôi thời gian chờ mỗi lần retry
          maxDelay: 30000, // Tối đa 30 giây
          delayFirstAttempt: false, // Không delay lần đầu tiên
          jitter: 'full', // Thêm jitter để tránh "thundering herd problem"
          retry: (error: any, attemptNumber: number) => {
            // Log lỗi
            this.logger.warn(
              `Attempt ${attemptNumber} failed for ${url}: ${error.message}`,
            );

            // Nếu là lỗi 4xx (trừ 429), không retry
            if (
              error.response &&
              error.response.status >= 400 &&
              error.response.status < 500 &&
              error.response.status !== 429
            ) {
              this.logger.warn(
                `Not retrying due to status code: ${error.response.status}`,
              );
              return false;
            }

            // Retry cho các lỗi khác
            this.logger.log(
              `Retrying... (attempt ${attemptNumber + 1}/${maxRetries})`,
            );
            return true;
          },
        },
      );

      return result;
    } catch (error) {
      this.logger.error(
        `All retry attempts failed for ${url}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Fetch HTML từ URL với retry, chỉ lấy thẻ head
   * @param url URL cần fetch
   * @param maxRetries Số lần retry tối đa
   * @returns HTML của thẻ head
   */
  private async fetchHeadWithRetry(
    url: string,
    maxRetries = 3,
  ): Promise<string> {
    try {
      // Sử dụng thư viện exponential-backoff
      const result = await backOff(
        async () => {
          this.logger.log(`Fetching head from URL: ${url}`);

          // Tạo headers với User-Agent rotation
          const headers = this.userAgentRotationService.createHeaders({
            browser: 'firefox',
            referer: url,
          });

          // Tạo config với proxy rotation
          const requestConfig = this.proxyRotationService.createProxyConfig({
            headers,
            timeout: 30000, // 30 giây timeout - Tăng để tránh timeout
            maxRedirects: 5,
            responseType: 'text',
          });

          // Sử dụng axios với responseType: 'text' để lấy HTML
          const response = await firstValueFrom(
            this.httpService.get(url, requestConfig),
          );

          const html = response.data;

          // Chỉ trích xuất phần head từ HTML
          const headMatch = html.match(/<head[^>]*>([\s\S]*?)<\/head>/i);
          if (headMatch && headMatch[1]) {
            return `<head>${headMatch[1]}</head>`;
          }

          // Nếu không tìm thấy thẻ head, trả về toàn bộ HTML
          return html;
        },
        {
          // Cấu hình backoff
          numOfAttempts: maxRetries,
          startingDelay: 1000, // 1 giây
          timeMultiple: 2, // Nhân đôi thời gian chờ mỗi lần retry
          maxDelay: 30000, // Tối đa 30 giây
          delayFirstAttempt: false, // Không delay lần đầu tiên
          jitter: 'full', // Thêm jitter để tránh "thundering herd problem"
          retry: (error: any, attemptNumber: number) => {
            // Log lỗi
            this.logger.warn(
              `Attempt ${attemptNumber} failed for ${url}: ${error.message}`,
            );

            // Nếu là lỗi 4xx (trừ 429), không retry
            if (
              error.response &&
              error.response.status >= 400 &&
              error.response.status < 500 &&
              error.response.status !== 429
            ) {
              this.logger.warn(
                `Not retrying due to status code: ${error.response.status}`,
              );
              return false;
            }

            // Retry cho các lỗi khác
            this.logger.log(
              `Retrying... (attempt ${attemptNumber + 1}/${maxRetries})`,
            );
            return true;
          },
        },
      );

      return result;
    } catch (error) {
      this.logger.error(
        `All retry attempts failed for ${url}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Fetch HTML có giới hạn kích thước với streaming
   * @param url URL cần fetch
   * @param maxSize Kích thước tối đa (bytes)
   * @param maxRetries Số lần retry tối đa
   * @returns HTML đã giới hạn kích thước
   */
  private async fetchLimitedHtml(
    url: string,
    maxSize = 50 * 1024,
    maxRetries = 3,
  ): Promise<string> {
    try {
      // Sử dụng thư viện exponential-backoff
      const result = await backOff(
        async () => {
          this.logger.log(
            `Fetching limited HTML (${maxSize} bytes) from URL: ${url}`,
          );

          // Tạo headers với User-Agent rotation
          const headers = this.userAgentRotationService.createHeaders({
            browser: 'safari',
            referer: url,
          });

          // Tạo config với proxy rotation
          const requestConfig = this.proxyRotationService.createProxyConfig({
            headers,
            timeout: 15000, // 15 giây timeout
            maxRedirects: 5,
            responseType: 'stream',
          });

          // Sử dụng axios với responseType: 'stream' để streaming
          const response = await firstValueFrom(
            this.httpService.get(url, requestConfig),
          );

          return new Promise<string>((resolve, reject) => {
            let htmlBuffer = '';
            let bytesReceived = 0;
            let isComplete = false;

            response.data.on('data', (chunk: Buffer) => {
              if (isComplete) return;

              const chunkStr = chunk.toString('utf8');
              bytesReceived += chunk.length;

              // Kiểm tra nếu đã đạt giới hạn
              if (bytesReceived >= maxSize) {
                const remainingBytes = maxSize - (bytesReceived - chunk.length);
                if (remainingBytes > 0) {
                  htmlBuffer += chunkStr.substring(0, remainingBytes);
                }
                isComplete = true;
                response.data.destroy(); // Dừng stream
                this.logger.log(
                  `Stream stopped at ${maxSize} bytes for memory optimization`,
                );
                resolve(htmlBuffer);
                return;
              }

              htmlBuffer += chunkStr;

              // Kiểm tra xem đã có đủ thông tin metadata chưa (head tag)
              if (
                htmlBuffer.includes('</head>') &&
                htmlBuffer.includes('<title>')
              ) {
                isComplete = true;
                response.data.destroy(); // Dừng stream sớm
                this.logger.log(
                  `Stream stopped early after finding head tag (${bytesReceived} bytes)`,
                );
                resolve(htmlBuffer);
                return;
              }
            });

            response.data.on('end', () => {
              if (!isComplete) {
                resolve(htmlBuffer);
              }
            });

            response.data.on('error', (error: Error) => {
              reject(error);
            });

            // Timeout cho stream
            setTimeout(() => {
              if (!isComplete) {
                isComplete = true;
                response.data.destroy();
                this.logger.warn(
                  `Stream timeout for ${url}, using partial data (${bytesReceived} bytes)`,
                );
                resolve(htmlBuffer);
              }
            }, 10000); // 10 giây timeout cho stream
          });
        },
        {
          // Cấu hình backoff
          numOfAttempts: maxRetries,
          startingDelay: 1000, // 1 giây
          timeMultiple: 2, // Nhân đôi thời gian chờ mỗi lần retry
          maxDelay: 30000, // Tối đa 30 giây
          delayFirstAttempt: false, // Không delay lần đầu tiên
          jitter: 'full', // Thêm jitter để tránh "thundering herd problem"
          retry: (error: any, attemptNumber: number) => {
            // Log lỗi
            this.logger.warn(
              `Attempt ${attemptNumber} failed for ${url}: ${error.message}`,
            );

            // Nếu là lỗi 4xx (trừ 429), không retry
            if (
              error.response &&
              error.response.status >= 400 &&
              error.response.status < 500 &&
              error.response.status !== 429
            ) {
              this.logger.warn(
                `Not retrying due to status code: ${error.response.status}`,
              );
              return false;
            }

            // Retry cho các lỗi khác
            this.logger.log(
              `Retrying... (attempt ${attemptNumber + 1}/${maxRetries})`,
            );
            return true;
          },
        },
      );

      return result;
    } catch (error) {
      this.logger.error(
        `All retry attempts failed for ${url}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Lưu batch metadata vào cơ sở dữ liệu với hiệu suất cao và logging chi tiết
   * @param userId ID của người dùng
   * @param metadataList Danh sách metadata cần lưu
   * @returns Số lượng metadata đã lưu thành công
   */
  private async saveBatchMetadata(
    userId: number,
    metadataList: ExtractedMetadata[],
  ): Promise<number> {
    if (!metadataList || metadataList.length === 0) {
      this.logger.warn(`saveBatchMetadata: Không có metadata để lưu`);
      return 0;
    }

    try {
      this.logger.log(
        `🔄 Bắt đầu lưu batch ${metadataList.length} metadata cho user ${userId}`,
      );

      // Log URLs sẽ được xử lý
      const urlsToProcess = metadataList.map((m) => m.url);
      this.logger.debug(
        `URLs cần xử lý: ${urlsToProcess.slice(0, 5).join(', ')}${urlsToProcess.length > 5 ? '...' : ''}`,
      );

      // Lọc metadata hợp lệ
      const validMetadata = metadataList.filter((metadata) => {
        const isValid = this.isValidMetadata(metadata);
        if (!isValid) {
          this.logger.warn(
            `❌ Metadata không hợp lệ cho URL: ${metadata.url} - title: "${metadata.title}", content: "${metadata.content?.substring(0, 50)}..."`,
          );
        }
        return isValid;
      });

      this.logger.log(
        `✅ Metadata hợp lệ: ${validMetadata.length}/${metadataList.length}`,
      );

      if (validMetadata.length === 0) {
        this.logger.warn(`❌ Không có metadata hợp lệ nào để lưu`);
        return 0;
      }

      // Lấy danh sách URL đã tồn tại
      const urls = validMetadata.map((m) => m.url);
      this.logger.debug(
        `🔍 Kiểm tra ${urls.length} URLs đã tồn tại trong DB...`,
      );

      const existingUrls = await this.urlRepository.find({
        where: { url: In(urls), ownedBy: userId },
      });

      this.logger.log(
        `📊 Tìm thấy ${existingUrls.length} URLs đã tồn tại trong DB`,
      );

      if (existingUrls.length > 0) {
        this.logger.debug(
          `URLs đã tồn tại: ${existingUrls
            .map((u) => u.url)
            .slice(0, 3)
            .join(', ')}${existingUrls.length > 3 ? '...' : ''}`,
        );
      }

      // Tạo map để tra cứu nhanh
      const existingUrlMap = new Map(existingUrls.map((url) => [url.url, url]));

      // Phân loại URLs cần cập nhật và tạo mới
      const urlsToUpdate: Url[] = [];
      const urlsToCreate: Url[] = [];
      const currentTime = Date.now();

      for (const metadata of validMetadata) {
        const existingUrl = existingUrlMap.get(metadata.url);

        if (existingUrl) {
          // Cập nhật URL đã tồn tại
          existingUrl.title = metadata.title;
          existingUrl.content = metadata.content;
          existingUrl.tags = metadata.tags; // Lưu tags vào cột tags riêng biệt
          existingUrl.updatedAt = currentTime;
          urlsToUpdate.push(existingUrl);
          this.logger.debug(`🔄 Sẽ cập nhật URL: ${metadata.url}`);
        } else {
          // Tạo URL mới
          const newUrl = new Url();
          newUrl.url = metadata.url;
          newUrl.title = metadata.title;
          newUrl.content = metadata.content;
          newUrl.tags = metadata.tags; // Lưu tags vào cột tags riêng biệt
          newUrl.ownedBy = userId;
          newUrl.createdAt = currentTime;
          newUrl.updatedAt = currentTime;
          urlsToCreate.push(newUrl);
          this.logger.debug(`➕ Sẽ tạo mới URL: ${metadata.url}`);
        }
      }

      this.logger.log(
        `📝 Phân loại hoàn thành: ${urlsToUpdate.length} URLs cập nhật, ${urlsToCreate.length} URLs tạo mới`,
      );

      // Thực hiện batch operations
      let savedCount = 0;

      if (urlsToUpdate.length > 0) {
        try {
          await this.urlRepository.save(urlsToUpdate);
          savedCount += urlsToUpdate.length;
          this.logger.log(`✅ Đã cập nhật ${urlsToUpdate.length} URLs`);
        } catch (updateError) {
          this.logger.error(`❌ Lỗi khi cập nhật URLs: ${updateError.message}`);
        }
      }

      if (urlsToCreate.length > 0) {
        try {
          await this.urlRepository.save(urlsToCreate);
          savedCount += urlsToCreate.length;
          this.logger.log(`✅ Đã tạo mới ${urlsToCreate.length} URLs`);
        } catch (createError) {
          this.logger.error(`❌ Lỗi khi tạo mới URLs: ${createError.message}`);
        }
      }

      this.logger.log(
        `🎉 Hoàn thành lưu batch: ${savedCount}/${metadataList.length} metadata thành công (${validMetadata.length} hợp lệ)`,
      );

      // Log chi tiết nếu có sự khác biệt
      if (savedCount !== metadataList.length) {
        const invalidCount = metadataList.length - validMetadata.length;
        const failedCount = validMetadata.length - savedCount;
        this.logger.warn(
          `⚠️  Phân tích: ${invalidCount} metadata không hợp lệ, ${failedCount} metadata lưu thất bại`,
        );
      }

      return savedCount;
    } catch (error) {
      this.logger.error(`❌ Lỗi khi lưu batch metadata: ${error.message}`);
      this.logger.error(`Stack trace: ${error.stack}`);
      return 0;
    }
  }

  /**
   * Clean up metadata để đảm bảo tags không bị lưu vào content
   * @param metadata Metadata cần clean up
   * @returns Metadata đã được clean up
   */
  private cleanupMetadata(metadata: ExtractedMetadata): ExtractedMetadata {
    if (!metadata.content) {
      return metadata;
    }

    // Kiểm tra và loại bỏ tags từ cuối content nếu có
    const tagsPattern = /\n\nTags:\s*(.+)$/;
    const match = metadata.content.match(tagsPattern);

    if (match) {
      // Nếu tìm thấy tags trong content, loại bỏ chúng
      metadata.content = metadata.content.replace(tagsPattern, '').trim();

      // Nếu chưa có tags riêng biệt, sử dụng tags từ content
      if (!metadata.tags && match[1]) {
        metadata.tags = match[1].trim();
      }

      this.logger.debug(`Cleaned up metadata: removed tags from content and moved to tags field`);
    }

    return metadata;
  }

  /**
   * Kiểm tra metadata có hợp lệ để lưu vào database không
   * @param metadata Metadata cần kiểm tra
   * @returns true nếu có đủ thông tin để lưu, false nếu không
   */
  private isValidMetadata(metadata: ExtractedMetadata): boolean {
    // Kiểm tra URL có tồn tại
    const hasUrl = Boolean(
      metadata &&
      metadata.url &&
      typeof metadata.url === 'string' &&
      metadata.url.trim() !== ''
    );

    // Kiểm tra title có tồn tại và không rỗng
    const hasTitle = Boolean(
      metadata.title &&
      typeof metadata.title === 'string' &&
      metadata.title.trim() !== ''
    );

    // Kiểm tra content có tồn tại và không rỗng
    const hasContent = Boolean(
      metadata.content &&
      typeof metadata.content === 'string' &&
      metadata.content.trim() !== ''
    );

    // Chỉ lưu vào DB nếu có URL và ít nhất title HOẶC content
    const isValid = hasUrl && (hasTitle || hasContent);

    // Log kết quả kiểm tra chi tiết
    if (!isValid) {
      if (!hasUrl) {
        this.logger.warn(`❌ Invalid metadata: missing URL`);
      } else if (!hasTitle && !hasContent) {
        this.logger.warn(`⚠️ URL ${metadata.url} không có title và content - không lưu vào DB nhưng vẫn báo thành công`);
      }
    } else {
      this.logger.debug(`✅ Valid metadata for URL ${metadata.url}: hasTitle=${hasTitle}, hasContent=${hasContent}`);
    }

    return isValid;
  }

  /**
   * Xử lý batch URLs song song với smart crawling
   * @param batch Batch URLs cần xử lý
   * @param userId ID người dùng
   * @param crawlDto Thông tin crawl
   * @param visitedUrls Set các URL đã xử lý
   * @param urlsToVisit Queue các URL cần xử lý
   * @param errors Mảng lỗi
   * @returns Array metadata đã trích xuất
   */
  private async processConcurrentUrlsWithSmartCrawling(
    batch: Array<{ url: string; depth: number }>,
    userId: number,
    crawlDto: CrawlDto,
    visitedUrls: Set<string>,
    urlsToVisit: Array<{ url: string; depth: number }>,
    errors: string[],
  ): Promise<ExtractedMetadata[]> {
    const results: ExtractedMetadata[] = [];

    // Xử lý song song với Promise.allSettled để không bị block bởi lỗi
    const promises = batch.map(({ url, depth }) =>
      this.processSingleUrlWithSmartCrawling(
        url,
        depth,
        userId,
        crawlDto,
        visitedUrls,
        urlsToVisit,
        errors,
      ),
    );

    const settledResults = await Promise.allSettled(promises);

    // Xử lý kết quả
    settledResults.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        results.push(result.value);
      } else if (result.status === 'rejected') {
        const errorMsg = `Lỗi xử lý URL ${batch[index].url}: ${result.reason}`;
        this.logger.warn(errorMsg);
        errors.push(errorMsg);
      }
    });

    return results;
  }

  /**
   * Xử lý một URL đơn lẻ với smart crawling và metadata extraction
   * @param url URL cần xử lý
   * @param depth Độ sâu hiện tại
   * @param userId ID người dùng
   * @param crawlDto Thông tin crawl
   * @param visitedUrls Set các URL đã xử lý
   * @param urlsToVisit Queue các URL cần xử lý
   * @param errors Mảng lỗi
   * @returns Metadata đã trích xuất hoặc null nếu thất bại
   */
  private async processSingleUrlWithSmartCrawling(
    url: string,
    depth: number,
    userId: number,
    crawlDto: CrawlDto,
    visitedUrls: Set<string>,
    urlsToVisit: Array<{ url: string; depth: number }>,
    errors: string[],
  ): Promise<ExtractedMetadata | null> {
    try {
      this.logger.log(`🧠 Smart processing URL: ${url} (độ sâu: ${depth})`);

      // Kiểm tra metadata cache trước
      const normalizedUrl = this.normalizeUrl(url);
      const cached = this.metadataCache.get(normalizedUrl);
      if (cached && cached.expiry > Date.now()) {
        this.logger.debug(`Metadata cache hit for URL: ${normalizedUrl}`);

        // Vẫn cần trích xuất child URLs nếu chưa đạt độ sâu tối đa
        if (depth < crawlDto.depth) {
          try {
            const childUrls = await this.extractChildUrls(url);
            for (const childUrl of childUrls) {
              if (
                !visitedUrls.has(childUrl) &&
                urlsToVisit.length + visitedUrls.size < (crawlDto.maxUrls || 20)
              ) {
                urlsToVisit.push({ url: childUrl, depth: depth + 1 });
              }
            }
          } catch (childError) {
            this.logger.warn(
              `Lỗi khi trích xuất child URLs từ cache cho ${url}: ${childError.message}`,
            );
          }
        }

        return cached.metadata;
      }

      // Áp dụng rate limiting trước khi crawl
      await this.waitForRateLimit(url);

      // Sử dụng smart crawling thay vì fetchOptimalHtml
      const crawlResult = await this.smartCrawl(url);

      // Trích xuất metadata từ HTML
      const metadata = this.extractHeadMetadata(crawlResult.html, url);

      // Trích xuất metadata từ JSON-LD nếu có
      const jsonLdData = this.extractJsonLdMetadata(crawlResult.html);

      // Kết hợp metadata từ JSON-LD nếu có
      if (jsonLdData && jsonLdData.length > 0) {
        const jsonLd = jsonLdData[0];

        if (!metadata.title && jsonLd.name) {
          metadata.title = jsonLd.name;
        }

        if (!metadata.content && jsonLd.description) {
          metadata.content = jsonLd.description;
        }

        if (!metadata.tags && jsonLd.keywords) {
          if (Array.isArray(jsonLd.keywords)) {
            metadata.tags = jsonLd.keywords.join(', ');
          } else if (typeof jsonLd.keywords === 'string') {
            metadata.tags = jsonLd.keywords;
          }
        }
      }

      // Clean up metadata để đảm bảo tags không bị lưu vào content
      const cleanedMetadata = this.cleanupMetadata(metadata);

      // Cache metadata (với format mới - tags riêng biệt)
      this.metadataCache.set(normalizedUrl, {
        metadata: cleanedMetadata,
        expiry: Date.now() + this.METADATA_CACHE_TTL,
      });

      this.logger.debug(
        `Metadata extracted for ${url}: title="${cleanedMetadata.title}", content="${cleanedMetadata.content?.substring(0, 50)}..."`,
      );

      // Nếu chưa đạt độ sâu tối đa, trích xuất các URL con
      if (depth < crawlDto.depth) {
        try {
          // Sử dụng URLs từ smart crawl result nếu có
          let childUrls: string[] = [];
          if (crawlResult.urls && crawlResult.urls.length > 0) {
            childUrls = crawlResult.urls;
            this.logger.log(
              `Smart crawl extracted ${childUrls.length} child URLs from ${url}`,
            );
          } else {
            // Fallback: trích xuất từ HTML
            childUrls = await this.extractChildUrls(url);
            this.logger.log(
              `Fallback extraction: ${childUrls.length} child URLs from ${url}`,
            );
          }

          // Thêm các URL con vào hàng đợi
          for (const childUrl of childUrls) {
            if (
              !visitedUrls.has(childUrl) &&
              urlsToVisit.length + visitedUrls.size < (crawlDto.maxUrls || 20)
            ) {
              urlsToVisit.push({ url: childUrl, depth: depth + 1 });
            }
          }

          this.logger.debug(
            `Added ${Math.min(childUrls.length, (crawlDto.maxUrls || 20) - urlsToVisit.length - visitedUrls.size)} child URLs to queue`,
          );
        } catch (childError) {
          this.logger.warn(
            `Lỗi khi trích xuất child URLs cho ${url}: ${childError.message}`,
          );
          errors.push(`Lỗi trích xuất child URLs: ${childError.message}`);
        }
      }

      return cleanedMetadata;
    } catch (error) {
      const errorMsg = `Lỗi xử lý URL ${url}: ${error.message}`;
      this.logger.error(errorMsg);
      errors.push(errorMsg);
      return null;
    }
  }

  /**
   * Trích xuất metadata từ thẻ head
   * @param html HTML của trang web
   * @param url URL của trang web
   * @returns Metadata đã trích xuất
   */
  private extractHeadMetadata(html: string, url: string): ExtractedMetadata {
    const $ = cheerio.load(html);

    // Trích xuất title từ nhiều nguồn
    let title = $('title').text().trim();
    if (!title) {
      title =
        $('meta[property="og:title"]').attr('content') ||
        $('meta[name="twitter:title"]').attr('content') ||
        $('meta[itemprop="name"]').attr('content') ||
        $('h1').first().text().trim() ||
        '';
    }

    // Trích xuất content (description) từ nhiều nguồn
    let content = $('meta[name="description"]').attr('content') || '';
    if (!content) {
      content =
        $('meta[property="og:description"]').attr('content') ||
        $('meta[name="twitter:description"]').attr('content') ||
        $('meta[itemprop="description"]').attr('content') ||
        $('meta[property="description"]').attr('content') ||
        '';
    }

    // Trích xuất tags (keywords) từ nhiều nguồn
    const tags =
      $('meta[name="keywords"]').attr('content') ||
      $('meta[property="article:tag"]').attr('content') ||
      $('meta[property="keywords"]').attr('content') ||
      '';

    this.logger.debug(
      `Extracted metadata from head: title="${title}", content="${content.substring(0, 50)}...", tags="${tags}"`,
    );

    return {
      url,
      title,
      content,
      tags,
    };
  }

  /**
   * Trích xuất metadata từ JSON-LD
   * @param html HTML của trang web
   * @returns Dữ liệu JSON-LD đã phân tích
   */
  private extractJsonLdMetadata(html: string): Array<Record<string, any>> {
    const $ = cheerio.load(html);
    const jsonLdScripts = $('script[type="application/ld+json"]');

    if (jsonLdScripts.length === 0) return [];

    try {
      const jsonLdData: Array<Record<string, any>> = [];
      jsonLdScripts.each((_, element) => {
        try {
          const jsonContent = $(element).html();
          if (jsonContent) {
            const parsed = JSON.parse(jsonContent);
            jsonLdData.push(parsed);
          }
        } catch (e) {
          this.logger.warn(`Failed to parse JSON-LD: ${e.message}`);
        }
      });

      this.logger.debug(`Extracted ${jsonLdData.length} JSON-LD objects`);
      return jsonLdData;
    } catch (error) {
      this.logger.warn(`Error extracting JSON-LD: ${error.message}`);
      return [];
    }
  }

  /**
   * Smart crawling với auto-detection của loại trang web
   * @param url URL cần crawl
   * @param maxRetries Số lần retry tối đa
   * @returns HTML content và URLs
   */
  private async smartCrawl(
    url: string,
    maxRetries = 3,
  ): Promise<{
    html: string;
    urls: string[];
    metadata?: any;
  }> {
    try {
      this.logger.log(`🧠 Smart crawling: ${url}`);

      // Phát hiện loại trang web
      const websiteType =
        await this.advancedCrawlerService.detectWebsiteType(url);
      this.logger.log(
        `📊 Website type detected: ${websiteType.type} (${websiteType.framework || 'unknown'}) - Confidence: ${Math.round(websiteType.confidence * 100)}%`,
      );

      if (websiteType.needsBrowser && websiteType.confidence > 0.4) {
        // Giảm threshold từ 0.7 xuống 0.4
        // Sử dụng browser automation cho trang web phức tạp
        this.logger.log(
          `🚀 Using browser automation for ${websiteType.type} website (confidence: ${Math.round(websiteType.confidence * 100)}%)`,
        );

        const result = await this.advancedCrawlerService.crawlWithBrowser(url, {
          waitTime: this.getOptimalWaitTime(websiteType),
          scrollToBottom: this.shouldScrollToBottom(websiteType),
          extractUrls: true,
          takeScreenshot: false,
          waitForSelector: this.getWaitSelector(websiteType),
        });

        return {
          html: result.html,
          urls: result.urls,
          metadata: result.metadata,
        };
      } else {
        // Sử dụng traditional HTTP crawling
        this.logger.log(
          `📄 Using traditional HTTP crawling for ${websiteType.type} website (confidence: ${Math.round(websiteType.confidence * 100)}%)`,
        );
        const html = await this.fetchOptimalHtml(url, maxRetries);
        const urls = await this.extractChildUrlsFromHtml(html, url);

        return { html, urls };
      }
    } catch (error) {
      this.logger.warn(
        `Smart crawl failed for ${url}, falling back to traditional method: ${error.message}`,
      );

      // Fallback to traditional method
      const html = await this.fetchOptimalHtml(url, maxRetries);
      const urls = await this.extractChildUrlsFromHtml(html, url);

      return { html, urls };
    }
  }

  /**
   * Trích xuất các URL con từ một URL với smart crawling
   * @param url URL cần trích xuất
   * @returns Danh sách các URL con
   */
  private async extractChildUrls(url: string): Promise<string[]> {
    try {
      // Sử dụng smart crawling
      const result = await this.smartCrawl(url);

      if (result.urls && result.urls.length > 0) {
        // Nếu smart crawl đã trích xuất URLs, sử dụng kết quả đó
        this.logger.log(
          `Smart crawl extracted ${result.urls.length} URLs from ${url}`,
        );
        return result.urls;
      } else {
        // Fallback: sử dụng cả normal và aggressive extraction
        this.logger.log(`Fallback: extracting URLs from HTML for ${url}`);
        const normalUrls = await this.extractChildUrlsFromHtml(
          result.html,
          url,
        );
        const aggressiveUrls = await this.extractChildUrlsAggressive(
          result.html,
          url,
        );

        // Combine và deduplicate
        const allUrls = [...new Set([...normalUrls, ...aggressiveUrls])];
        this.logger.log(
          `Combined extraction: ${normalUrls.length} normal + ${aggressiveUrls.length} aggressive = ${allUrls.length} total URLs`,
        );
        return allUrls;
      }
    } catch (error) {
      this.logger.error(`Error extracting child URLs: ${error.message}`);
      return [];
    }
  }

  // Placeholder methods - cần implement đầy đủ từ file gốc
  private async fetchOptimalHtml(url: string, maxRetries = 3): Promise<string> {
    return await this.fetchHeadWithRetry(url, maxRetries);
  }

  private async extractChildUrlsFromHtml(html: string, baseUrl: string): Promise<string[]> {
    // Placeholder - cần implement đầy đủ
    return [];
  }

  private async extractChildUrlsAggressive(html: string, baseUrl: string): Promise<string[]> {
    // Placeholder - cần implement đầy đủ
    return [];
  }

  private getOptimalWaitTime(websiteType: any): number {
    return 3000; // Placeholder
  }

  private shouldScrollToBottom(websiteType: any): boolean {
    return false; // Placeholder
  }

  private getWaitSelector(websiteType: any): string | undefined {
    return undefined; // Placeholder
  }
}