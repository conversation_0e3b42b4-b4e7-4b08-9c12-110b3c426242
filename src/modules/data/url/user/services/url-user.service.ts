import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { Url } from '../../entities/url.entity';
import { CrawlSession } from '../../entities/crawl-session.entity';
import { UrlRepository } from '../../repositories';
import { CrawlSessionRepository } from '../../repositories/crawl-session.repository';
import { CreateUrlDto } from '../../schemas/create-url.dto';
import { UpdateUrlDto } from '../../schemas/update-url.dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { SortDirection } from '@common/dto/query.dto';
import { FindAllUrlDto } from '../dto/find-all-url.dto';
import { CrawlDto } from '../dto/crawl.dto';
import { StartCrawlWithTrackingDto, CrawlProgressResponseDto, CrawlSessionListResponseDto, StartCrawlResponseDto } from '../dto/crawl-progress.dto';
import { URL_ERROR_CODES } from 'src/modules/data/url/exceptions';
import { AppException } from '@/common';
import { QueueService } from '@shared/queue';

@Injectable()
export class UrlUserService {
  private readonly logger = new Logger(UrlUserService.name);

  constructor(
    @InjectRepository(Url)
    private readonly urlRepository: Repository<Url>,
    private readonly urlCustomRepository: UrlRepository,
    private readonly queueService: QueueService,
  ) {}

  /**
   * Enqueue crawl job to queue for background processing
   * @param userId ID của người dùng
   * @param crawlDto Thông tin crawl
   * @returns Job ID
   */
  async enqueueCrawlJob(userId: number, crawlDto: CrawlDto): Promise<string | number> {
    return await this.queueService.addCrawlUrlJob({
      userId,
      crawlDto,
    });
  }

  /**
   * Tìm URL theo ID và kiểm tra quyền truy cập
   * @param userId ID của người dùng
   * @param id ID của URL
   * @returns URL nếu tìm thấy và có quyền truy cập
   */
  async findUrlById(userId: number, id: string): Promise<Url> {
    try {
      const url = await this.urlRepository.findOne({
        where: { id },
      });

      if (!url) {
        throw new AppException(
          URL_ERROR_CODES.URL_NOT_FOUND,
          'Không tìm thấy URL',
        );
      }

      // Kiểm tra quyền truy cập
      if (url.ownedBy !== userId) {
        throw new AppException(
          URL_ERROR_CODES.URL_ACCESS_DENIED,
          'Bạn không có quyền truy cập URL này',
        );
      }

      return url;
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý các lỗi khác
      throw new AppException(
        URL_ERROR_CODES.URL_SEARCH_FAILED,
        `Không thể tìm URL: ${error.message}`,
      );
    }
  }

  /**
   * Tìm tất cả URL của người dùng với phân trang và tìm kiếm
   * @param userId ID của người dùng
   * @param findAllUrlDto Thông tin tìm kiếm và phân trang
   * @returns Danh sách URL với phân trang
   */
  async findUrlsByOwner(
    userId: number,
    findAllUrlDto: FindAllUrlDto,
  ): Promise<PaginatedResult<Url>> {
    try {
      const { page = 1, limit = 10, search, sortBy = 'createdAt', sortDirection = SortDirection.DESC } = findAllUrlDto;

      // Tính toán offset
      const offset = (page - 1) * limit;

      // Tạo query builder
      const queryBuilder = this.urlRepository
        .createQueryBuilder('url')
        .where('url.ownedBy = :userId', { userId });

      // Thêm điều kiện tìm kiếm nếu có
      if (search && search.trim() !== '') {
        queryBuilder.andWhere(
          '(url.title ILIKE :search OR url.content ILIKE :search OR url.url ILIKE :search)',
          { search: `%${search.trim()}%` }
        );
      }

      // Thêm sắp xếp
      queryBuilder.orderBy(`url.${sortBy}`, sortDirection);

      // Thêm phân trang
      queryBuilder.skip(offset).take(limit);

      // Thực hiện query
      const [urls, total] = await queryBuilder.getManyAndCount();

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(total / limit);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      return {
        items: urls,
        meta: {
          totalItems: total,
          itemCount: urls.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Error finding URLs for user ${userId}: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_SEARCH_FAILED,
        `Không thể tìm danh sách URL: ${error.message}`,
      );
    }
  }

  /**
   * Tìm kiếm URL với từ khóa
   * @param userId ID của người dùng
   * @param searchQuery Từ khóa tìm kiếm
   * @param page Trang hiện tại
   * @param limit Số lượng kết quả mỗi trang
   * @returns Danh sách URL phù hợp
   */
  async searchUrls(
    userId: number,
    searchQuery: string,
    page = 1,
    limit = 10,
  ): Promise<PaginatedResult<Url>> {
    try {
      if (!searchQuery || searchQuery.trim() === '') {
        // Nếu không có từ khóa, trả về danh sách rỗng
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: limit,
            totalPages: 0,
            currentPage: page,
          },
        };
      }

      const offset = (page - 1) * limit;

      const queryBuilder = this.urlRepository
        .createQueryBuilder('url')
        .where('url.ownedBy = :userId', { userId })
        .andWhere(
          '(url.title ILIKE :search OR url.content ILIKE :search OR url.url ILIKE :search)',
          { search: `%${searchQuery.trim()}%` }
        )
        .orderBy('url.createdAt', 'DESC')
        .skip(offset)
        .take(limit);

      const [urls, total] = await queryBuilder.getManyAndCount();

      const totalPages = Math.ceil(total / limit);

      return {
        items: urls,
        meta: {
          totalItems: total,
          itemCount: urls.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Error searching URLs: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_SEARCH_FAILED,
        `Không thể tìm kiếm URL: ${error.message}`,
      );
    }
  }

  /**
   * Tạo URL mới
   * @param userId ID của người dùng
   * @param createUrlDto Thông tin URL cần tạo
   * @returns URL đã tạo
   */
  async createUrl(userId: number, createUrlDto: CreateUrlDto): Promise<Url> {
    try {
      // Kiểm tra URL có hợp lệ không
      try {
        new URL(createUrlDto.url);
      } catch (error) {
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_FORMAT,
          'URL không đúng định dạng',
        );
      }

      // Kiểm tra URL đã tồn tại chưa
      const existingUrl = await this.urlRepository.findOne({
        where: {
          url: createUrlDto.url,
          ownedBy: userId,
        },
      });

      if (existingUrl) {
        throw new AppException(
          URL_ERROR_CODES.URL_ALREADY_EXISTS,
          'URL này đã tồn tại trong hệ thống của bạn',
        );
      }

      // Tạo URL mới
      const newUrl = new Url();
      newUrl.url = createUrlDto.url;
      newUrl.title = createUrlDto.title || '';
      newUrl.content = createUrlDto.content || '';
      newUrl.ownedBy = userId;
      newUrl.createdAt = Date.now();
      newUrl.updatedAt = Date.now();

      // Lưu URL
      return this.urlRepository.save(newUrl);
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý các lỗi khác
      throw new AppException(
        URL_ERROR_CODES.URL_CREATION_FAILED,
        `Không thể tạo URL: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật URL
   * @param id ID của URL
   * @param userId ID của người dùng
   * @param updateUrlDto Thông tin cập nhật
   * @returns URL đã cập nhật
   */
  async updateUrl(
    id: string,
    userId: number,
    updateUrlDto: UpdateUrlDto,
  ): Promise<Url> {
    try {
      // Tìm URL theo ID và kiểm tra quyền truy cập
      const url = await this.findUrlById(userId, id);

      // Kiểm tra URL có hợp lệ không (nếu có cập nhật URL)
      if (updateUrlDto.url) {
        try {
          new URL(updateUrlDto.url);
        } catch (error) {
          throw new AppException(
            URL_ERROR_CODES.URL_INVALID_FORMAT,
            'URL không đúng định dạng',
          );
        }

        // Kiểm tra URL mới đã tồn tại chưa (trừ URL hiện tại)
        const existingUrl = await this.urlRepository.findOne({
          where: {
            url: updateUrlDto.url,
          },
        });

        if (existingUrl && existingUrl.id !== id) {
          throw new AppException(
            URL_ERROR_CODES.URL_ALREADY_EXISTS,
            'URL này đã tồn tại trong hệ thống',
          );
        }
      }

      // Cập nhật thông tin URL
      Object.assign(url, updateUrlDto);
      url.updatedAt = Date.now();

      // Lưu URL đã cập nhật
      return this.urlRepository.save(url);
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý các lỗi khác
      throw new AppException(
        URL_ERROR_CODES.URL_UPDATE_FAILED,
        `Không thể cập nhật URL: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều URL cùng lúc
   * @param ids Danh sách ID của các URL cần xóa
   * @param userId ID của người dùng
   */
  async deleteUrls(ids: string[], userId: number): Promise<void> {
    try {
      this.logger.log(`Deleting URLs with IDs: ${ids.join(', ')} for user: ${userId}`);

      if (!ids || ids.length === 0) {
        this.logger.error('URL IDs are required');
        throw new AppException(
          URL_ERROR_CODES.URL_INVALID_PARAMS,
          'Danh sách ID URL là bắt buộc',
        );
      }

      // Tìm tất cả URL theo IDs
      const urls = await this.urlRepository.find({
        where: { id: In(ids) },
      });

      if (urls.length === 0) {
        this.logger.warn(`No URLs found with IDs: ${ids.join(', ')}`);
        throw new AppException(
          URL_ERROR_CODES.URL_NOT_FOUND,
          'Không tìm thấy URL nào để xóa',
        );
      }

      // Kiểm tra quyền truy cập cho tất cả URL
      const unauthorizedUrls = urls.filter((url) => url.ownedBy !== userId);
      if (unauthorizedUrls.length > 0) {
        this.logger.warn(
          `User ${userId} does not have access to URLs: ${unauthorizedUrls.map((u) => u.id).join(', ')}`,
        );
        throw new AppException(
          URL_ERROR_CODES.URL_ACCESS_DENIED,
          'Bạn không có quyền xóa một số URL này',
        );
      }

      // Xóa tất cả URL
      await this.urlRepository.remove(urls);

      this.logger.log(
        `Successfully deleted ${urls.length} URLs for user: ${userId}`,
      );
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error deleting URLs: ${error.message}`, error.stack);
      throw new AppException(
        URL_ERROR_CODES.URL_DELETE_FAILED,
        `Không thể xóa URL: ${error.message}`,
      );
    }
  }

  /**
   * Xóa URL (deprecated - sử dụng deleteUrls thay thế)
   * @param id ID của URL
   * @param userId ID của người dùng
   */
  async deleteUrl(id: string, userId: number): Promise<void> {
    return this.deleteUrls([id], userId);
  }
}
