import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUrl, IsInt, Min, Max, IsOptional, IsBoolean } from 'class-validator';

/**
 * DTO cho request bắt đầu crawl với tracking
 */
export class StartCrawlWithTrackingDto {
  @ApiProperty({
    description: 'URL gốc để bắt đầu crawl',
    example: 'https://example.com',
  })
  @IsNotEmpty({ message: 'URL không được để trống' })
  @IsUrl({}, { message: 'URL không hợp lệ' })
  url: string;

  @ApiProperty({
    description: 'Độ sâu tìm kiếm URL (1-3)',
    example: 2,
    minimum: 1,
    maximum: 3,
  })
  @IsNotEmpty({ message: 'Độ sâu không được để trống' })
  @IsInt({ message: 'Độ sâu phải là số nguyên' })
  @Min(1, { message: '<PERSON><PERSON> sâu tối thiểu là 1' })
  @Max(3, { message: '<PERSON><PERSON> sâu tối đa là 3' })
  depth: number;

  @ApiProperty({
    description: 'Bỏ qua kiểm tra robots.txt (mặc định: false)',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'ignoreRobotsTxt phải là boolean' })
  ignoreRobotsTxt?: boolean;

  @ApiProperty({
    description: 'Số lượng URL tối đa cần crawl (mặc định: không giới hạn)',
    example: 100,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'maxUrls phải là số nguyên' })
  @Min(1, { message: 'maxUrls tối thiểu là 1' })
  maxUrls?: number;
}

/**
 * DTO cho response tiến độ crawl
 */
export class CrawlProgressResponseDto {
  @ApiProperty({
    description: 'ID của session crawl',
    example: 'crawl_1_1748405343422'
  })
  sessionId: string;

  @ApiProperty({
    description: 'URL được crawl',
    example: 'https://example.com'
  })
  url: string;

  @ApiProperty({
    description: 'Trạng thái crawl',
    enum: ['running', 'completed', 'error', 'cancelled'],
    example: 'running'
  })
  status: 'running' | 'completed' | 'error' | 'cancelled';

  @ApiProperty({
    description: 'Thông tin tiến độ crawl',
    example: {
      totalUrls: 50,
      processedUrls: 25,
      successfulUrls: 23,
      failedUrls: 2,
      currentDepth: 2,
      currentUrl: 'https://example.com/page1',
      estimatedTimeRemaining: 120,
      percentage: 50
    }
  })
  progress: {
    totalUrls: number;
    processedUrls: number;
    successfulUrls: number;
    failedUrls: number;
    currentDepth: number;
    currentUrl?: string;
    estimatedTimeRemaining?: number;
    percentage: number;
  };

  @ApiProperty({
    description: 'Cấu hình crawl',
    example: {
      depth: 2,
      maxUrls: 100,
      ignoreRobotsTxt: false
    }
  })
  config: {
    depth: number;
    maxUrls: number;
    ignoreRobotsTxt?: boolean;
    [key: string]: any;
  };

  @ApiProperty({
    description: 'Kết quả crawl (chỉ có khi hoàn thành)',
    required: false,
    example: {
      urlsProcessed: 25,
      urlsSaved: 23,
      message: 'Crawl hoàn thành thành công',
      errors: ['Không thể truy cập https://example.com/error']
    }
  })
  result?: {
    urlsProcessed: number;
    urlsSaved: number;
    message: string;
    errors?: string[];
  };

  @ApiProperty({
    description: 'Danh sách lỗi chi tiết',
    required: false,
    example: [
      {
        type: 'FETCH_ERROR',
        message: 'Không thể kết nối đến server',
        url: 'https://example.com/error',
        timestamp: 1748405343422
      }
    ]
  })
  errors?: Array<{
    type: string;
    message: string;
    url: string;
    timestamp: number;
  }>;

  @ApiProperty({
    description: 'Thời gian bắt đầu crawl (timestamp)',
    example: 1748405343422
  })
  startTime: number;

  @ApiProperty({
    description: 'Thời gian kết thúc crawl (timestamp)',
    required: false,
    example: 1748405343522
  })
  endTime?: number;

  @ApiProperty({
    description: 'Thời gian tạo session',
    example: '2024-01-28T10:30:00Z'
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Thời gian cập nhật cuối',
    example: '2024-01-28T10:35:00Z'
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Metadata bổ sung',
    required: false,
    example: {
      userAgent: 'Mozilla/5.0...',
      ipAddress: '***********',
      source: 'user'
    }
  })
  metadata?: {
    userAgent?: string;
    ipAddress?: string;
    source?: 'admin' | 'user';
    [key: string]: any;
  };
}

/**
 * DTO cho item trong danh sách sessions
 */
export class CrawlSessionItemDto {
  @ApiProperty({
    description: 'ID của session',
    example: 'crawl_1_1748405343422'
  })
  sessionId: string;

  @ApiProperty({
    description: 'URL được crawl',
    example: 'https://example.com'
  })
  url: string;

  @ApiProperty({
    description: 'Trạng thái',
    enum: ['running', 'completed', 'error', 'cancelled'],
    example: 'completed'
  })
  status: 'running' | 'completed' | 'error' | 'cancelled';

  @ApiProperty({
    description: 'Tiến độ (%)',
    example: 100
  })
  percentage: number;

  @ApiProperty({
    description: 'Số URLs đã xử lý',
    example: 25
  })
  processedUrls: number;

  @ApiProperty({
    description: 'Tổng số URLs',
    example: 25
  })
  totalUrls: number;

  @ApiProperty({
    description: 'Thời gian bắt đầu',
    example: '2024-01-28T10:30:00Z'
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Thời gian kết thúc',
    required: false,
    example: '2024-01-28T10:35:00Z'
  })
  endTime?: number;

  @ApiProperty({
    description: 'Thông báo kết quả',
    required: false,
    example: 'Crawl hoàn thành thành công'
  })
  message?: string;
}

/**
 * DTO cho response danh sách sessions
 */
export class CrawlSessionListResponseDto {
  @ApiProperty({
    description: 'Danh sách sessions',
    type: [CrawlSessionItemDto]
  })
  sessions: CrawlSessionItemDto[];

  @ApiProperty({
    description: 'Tổng số sessions',
    example: 10
  })
  total: number;

  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1
  })
  page: number;

  @ApiProperty({
    description: 'Số items mỗi trang',
    example: 20
  })
  limit: number;

  @ApiProperty({
    description: 'Tổng số trang',
    example: 1
  })
  totalPages: number;
}

/**
 * DTO cho response bắt đầu crawl
 */
export class StartCrawlResponseDto {
  @ApiProperty({
    description: 'ID của session crawl được tạo',
    example: 'crawl_1_1748405343422'
  })
  sessionId: string;

  @ApiProperty({
    description: 'ID của job trong queue',
    example: '12345'
  })
  jobId: string | number;

  @ApiProperty({
    description: 'Thông báo',
    example: 'Đã bắt đầu crawl URL. Sử dụng sessionId để theo dõi tiến độ.'
  })
  message: string;
}
