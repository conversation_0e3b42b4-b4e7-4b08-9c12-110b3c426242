import { QueryDto } from "@common/dto";
import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString, IsArray } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class FindAllUrlDto extends QueryDto {
  @ApiProperty({
    description: 'Từ khóa tìm kiếm URL, tìm kiếm theo title, content và url',
    example: 'google',
    required: false,
  })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({
    description: 'Loại URL cần lọc',
    example: 'web',
    required: false,
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiProperty({
    description: 'Các thẻ cần lọc',
    example: ['nestjs', 'tutorial'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @Type(() => String)
  @Transform(({ value }) => {
    // Xử lý trường hợp tags=tag1,tag2,tag3
    if (typeof value === 'string' && value.includes(',')) {
      return value.split(',').map(tag => tag.trim());
    }
    // <PERSON><PERSON> lý trường hợp tags là một string
    if (typeof value === 'string') {
      return [value];
    }
    // Trường hợp tags đã là mảng
    return value;
  })
  tags?: string[];
}
