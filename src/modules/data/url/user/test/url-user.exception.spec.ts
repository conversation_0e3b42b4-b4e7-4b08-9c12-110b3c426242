import { UrlUserException, UrlUserErrorCode } from '@modules/data/url/exceptions/url-user.exception';

describe('<PERSON><PERSON><PERSON> thử ngoại lệ URL của người dùng', () => {
  it('<PERSON>ải tạo được ngoại lệ với mã lỗi và thông báo chính xác', () => {
    const errorCode = UrlUserErrorCode.URL_NOT_FOUND;
    const errorMessage = 'URL không tồn tại';
    const exception = new UrlUserException(errorCode, errorMessage);

    expect(exception).toBeDefined();
    // UrlUserException extends HttpException which doesn't have getErrorCode method
    // Instead, we can check if the exception was created with the right parameters
    expect(exception.message).toContain(errorMessage);
  });

  it('Phải có đầy đủ các mã lỗi cần thiết', () => {
    expect(UrlUserErrorCode.URL_NOT_FOUND).toBeDefined();
    expect(UrlUserErrorCode.URL_ACCESS_DENIED).toBeDefined();
    expect(UrlUserErrorCode.URL_INVALID_PARAMS).toBeDefined();
    expect(UrlUserErrorCode.URL_FETCH_FAILED).toBeDefined();
    expect(UrlUserErrorCode.URL_SEARCH_FAILED).toBeDefined();
    expect(UrlUserErrorCode.URL_INVALID_SEARCH_PARAMS).toBeDefined();
    expect(UrlUserErrorCode.URL_INVALID_FORMAT).toBeDefined();
    expect(UrlUserErrorCode.URL_ALREADY_EXISTS).toBeDefined();
    expect(UrlUserErrorCode.URL_INVALID_CONTENT).toBeDefined();
    expect(UrlUserErrorCode.URL_CREATION_FAILED).toBeDefined();
    expect(UrlUserErrorCode.URL_UPDATE_FAILED).toBeDefined();
    expect(UrlUserErrorCode.URL_DELETE_FAILED).toBeDefined();
  });
});
