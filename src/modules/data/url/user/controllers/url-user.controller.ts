import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { Body, Controller, Delete, Get, Logger, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { SortDirection } from '@common/dto/query.dto';
import {
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse as SwaggerApiResponse,
  ApiSecurity,
  ApiTags,
} from '@nestjs/swagger';
import { ApiResponseDto as ApiResponse } from '@common/response/api-response-dto';
import { UrlListResponseSchema, UrlSchema } from '../../schemas/url.schema';
import { CreateUrlDto } from '../../schemas/create-url.dto';
import { UpdateUrlDto } from '../../schemas/update-url.dto';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { UrlUserService } from '../services/url-user.service';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { FindAllUrlDto } from '../dto/find-all-url.dto';
import { CrawlDto } from '../dto/crawl.dto';
import { DeleteUrlsUserDto } from '../dto/delete-urls-user.dto';
import {
  StartCrawlWithTrackingDto,
  CrawlProgressResponseDto,
  CrawlSessionListResponseDto,
  StartCrawlResponseDto
} from '../dto/crawl-progress.dto';

/**
 * Controller xử lý các endpoint liên quan đến URL cho người dùng
 */
@ApiTags(SWAGGER_API_TAGS.USER_URL)
@ApiSecurity('JWT-auth')
@Controller('data/url')
@UseGuards(JwtUserGuard)
export class UrlUserController {
  private readonly logger = new Logger(UrlUserController.name);

  constructor(private readonly urlUserService: UrlUserService) {}

  /**
   * Lấy danh sách URL của người dùng với phân trang và tìm kiếm
   */
  @ApiOperation({ summary: 'Lấy danh sách URL của người dùng' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Số trang',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng kết quả mỗi trang',
    type: Number,
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    description: 'Trường sắp xếp',
    type: String,
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    description: 'Hướng sắp xếp',
    enum: ['ASC', 'DESC'],
  })
  @ApiQuery({
    name: 'keyword',
    required: false,
    description: 'Từ khóa tìm kiếm',
    type: String,
  })
  @ApiQuery({
    name: 'type',
    required: false,
    description: 'Loại URL cần lọc',
    type: String,
  })
  @ApiQuery({
    name: 'tags',
    required: false,
    description:
      'Các thẻ cần lọc (có thể dùng dấu phẩy để phân tách nhiều thẻ, ví dụ: tags=nestjs,tutorial)',
    type: String,
  })
  @SwaggerApiResponse({
    status: 200,
    description: 'Danh sách URL',
    type: UrlListResponseSchema,
  })
  @Get()
  async findAll(@CurrentUser() user: any, @Query() queryParams: FindAllUrlDto) {
    const userId = user.id;
    this.logger.log(
      `Finding all URLs for user: ${userId}, page: ${queryParams.page}, limit: ${queryParams.limit}`,
    );

    // Convert string parameters to appropriate types if needed
    const page = queryParams.page || 1;
    const limit = queryParams.limit || 10;
    const sortBy = queryParams.sortBy || 'createdAt';
    const sortDirection = queryParams.sortDirection || SortDirection.DESC;

    // Đảm bảo tags luôn là mảng
    const tags = queryParams.tags || [];

    const findAllUrlDto = {
      page,
      limit,
      search: queryParams.keyword,
      sortBy,
      sortDirection,
    };

    const result = await this.urlUserService.findUrlsByOwner(
      userId,
      findAllUrlDto,
    );

    this.logger.log(`Found ${result.items.length} URLs for user: ${userId}`);

    return ApiResponse.success(result);
  }

  /**
   * Lấy thông tin chi tiết URL theo ID
   */
  @ApiOperation({ summary: 'Lấy thông tin chi tiết URL theo ID' })
  @ApiParam({ name: 'id', description: 'ID của URL', type: String })
  @SwaggerApiResponse({
    status: 200,
    description: 'Thông tin chi tiết URL',
    type: UrlSchema,
  })
  @SwaggerApiResponse({ status: 404, description: 'URL không tồn tại' })
  @Get(':id')
  async findOne(@CurrentUser() user: any, @Param('id') id: string) {
    const userId = user.id;
    this.logger.log(`Finding URL with ID: ${id} for user: ${userId}`);

    const result = await this.urlUserService.findUrlById(userId, id);

    this.logger.log(`URL with ID: ${id} found for user: ${userId}`);

    return ApiResponse.success(result);
  }

  /**
   * Tạo URL mới
   */
  @ApiOperation({ summary: 'Tạo URL mới' })
  @SwaggerApiResponse({
    status: 201,
    description: 'URL đã được tạo thành công',
    type: UrlSchema,
  })
  @SwaggerApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @Post()
  async create(@CurrentUser() user: any, @Body() createUrlDto: CreateUrlDto) {
    const userId = user.id;
    this.logger.log(`Creating new URL for user: ${userId}`);
    this.logger.debug(`URL data: ${JSON.stringify(createUrlDto)}`);

    const result = await this.urlUserService.createUrl(userId, createUrlDto);

    this.logger.log(`URL created successfully with ID: ${result.id}`);

    return ApiResponse.created(result, 'URL đã được tạo thành công');
  }

  /**
   * Cập nhật thông tin URL
   */
  @ApiOperation({ summary: 'Cập nhật thông tin URL' })
  @ApiParam({ name: 'id', description: 'ID của URL', type: String })
  @SwaggerApiResponse({
    status: 200,
    description: 'URL đã được cập nhật thành công',
    type: UrlSchema,
  })
  @SwaggerApiResponse({ status: 404, description: 'URL không tồn tại' })
  @Put(':id')
  async update(
    @CurrentUser() user: any,
    @Param('id') id: string,
    @Body() updateUrlDto: UpdateUrlDto,
  ) {
    const userId = user.id;
    this.logger.log(`Updating URL with ID: ${id} for user: ${userId}`);
    this.logger.debug(`Update data: ${JSON.stringify(updateUrlDto)}`);

    const result = await this.urlUserService.updateUrl(
      id,
      userId,
      updateUrlDto,
    );

    this.logger.log(`URL with ID: ${id} updated successfully`);

    return ApiResponse.success(result, 'URL đã được cập nhật thành công');
  }

  /**
   * Xóa nhiều URL
   */
  @ApiOperation({ summary: 'Xóa nhiều URL' })
  @SwaggerApiResponse({
    status: 200,
    description: 'URL đã được xóa thành công',
    examples: {
      success: {
        summary: 'Xóa thành công',
        value: {
          success: true,
          message: 'Đã xóa thành công 3 URL',
          data: null,
        },
      },
    },
  })
  @SwaggerApiResponse({
    status: 400,
    description: 'Dữ liệu không hợp lệ',
    examples: {
      invalidData: {
        summary: 'Dữ liệu không hợp lệ',
        value: {
          success: false,
          message: 'IDs phải là một mảng',
          errorCode: 30203,
        },
      },
    },
  })
  @SwaggerApiResponse({
    status: 404,
    description:
      'Một hoặc nhiều URL không tồn tại hoặc không có quyền truy cập',
    examples: {
      notFound: {
        summary: 'URL không tồn tại hoặc không có quyền',
        value: {
          success: false,
          message:
            'Không tìm thấy URL với ID: 550e8400-e29b-41d4-a716-446655440999 hoặc bạn không có quyền truy cập',
          errorCode: 30001,
        },
      },
    },
  })
  @Delete('batch')
  async remove(
    @CurrentUser() user: any,
    @Body() deleteUrlsDto: DeleteUrlsUserDto,
  ) {
    const userId = user.id;
    this.logger.log(
      `Deleting URLs with IDs: ${JSON.stringify(deleteUrlsDto.ids)} for user: ${userId}`,
    );

    await this.urlUserService.deleteUrls(deleteUrlsDto.ids, userId);

    this.logger.log(
      `Successfully deleted ${deleteUrlsDto.ids.length} URLs for user: ${userId}`,
    );

    return ApiResponse.success(
      null,
      `Đã xóa thành công ${deleteUrlsDto.ids.length} URL`,
    );
  }

  /**
   * Tự động crawl URL theo độ sâu và lấy metadata từ thẻ head
   * @param user Thông tin người dùng hiện tại
   * @param crawlDto Thông tin URL cần crawl
   * @returns Kết quả crawl URL với metadata
   */
  @ApiOperation({
    summary: 'Tự động crawl URL theo độ sâu và lấy metadata',
    description:
      'Crawl URL và lấy metadata (title, description, keywords) từ thẻ head',
  })
  @SwaggerApiResponse({ status: 200, description: 'Đã crawl URL thành công' })
  @SwaggerApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @Post('crawl')
  async crawlUrl(@CurrentUser() user: any, @Body() crawlDto: CrawlDto) {
    const userId = user.id;
    this.logger.log(
      `Bắt đầu crawl URL cho user: ${userId}, URL: ${crawlDto.url}, độ sâu: ${crawlDto.depth}`,
    );

    // Log các tùy chọn
    if (crawlDto.ignoreRobotsTxt) {
      this.logger.log(`Tùy chọn: Bỏ qua kiểm tra robots.txt`);
    }

    if (crawlDto.maxUrls) {
      this.logger.log(
        `Tùy chọn: Giới hạn số lượng URL tối đa: ${crawlDto.maxUrls}`,
      );
    }

    // Gọi service để crawl URL
    const result = await this.urlUserService.enqueueCrawlJob(userId, crawlDto);
    //
    // // Tạo thông báo chi tiết hơn
    // let message = '';
    // if (result.status === 'error') {
    //   // Nếu có lỗi, hiển thị thông báo lỗi
    //   message = result.message;
    //   this.logger.error(`Crawl URL thất bại: ${message}`);
    //
    //   // Nếu có danh sách lỗi chi tiết, hiển thị chúng
    //   if (result.errors && result.errors.length > 0) {
    //     this.logger.error(`Chi tiết lỗi:`);
    //     result.errors.forEach((error, index) => {
    //       this.logger.error(`${index + 1}. ${error}`);
    //     });
    //   }
    //
    //   // Sử dụng success nhưng với status là error trong result
    //   return ApiResponse.success(result, message);
    // } else if (result.urlsProcessed && result.urlsProcessed > 0) {
    //   // Nếu crawl thành công
    //   message = `Đã crawl thành công ${result.urlsProcessed} URL`;
    //   return ApiResponse.success(result, message);
    // } else {
    //   // Nếu không crawl được URL nào
    //   message = 'Không crawl được URL nào.';
    //
    //   // Nếu có danh sách lỗi chi tiết, thêm vào thông báo
    //   if (result.errors && result.errors.length > 0) {
    //     message += ' Lỗi: ' + result.errors[0];
    //     if (result.errors.length > 1) {
    //       message += ` và ${result.errors.length - 1} lỗi khác`;
    //     }
    //   }

    return ApiResponse.success(result, 'Nhận yêu cầu crawl thành công');
  }

  // ==================== CRAWL TRACKING ENDPOINTS ====================

  /**
   * Bắt đầu crawl với tracking session
   */
  @ApiOperation({
    summary: 'Bắt đầu crawl URL với theo dõi tiến độ',
    description: 'Tạo session crawl và theo dõi tiến độ thời gian thực'
  })
  @SwaggerApiResponse({
    status: 200,
    description: 'Đã bắt đầu crawl thành công',
    type: StartCrawlResponseDto
  })
  @SwaggerApiResponse({ status: 400, description: 'Dữ liệu không hợp lệ' })
  @Post('crawl/start')
  async startCrawlWithTracking(
    @CurrentUser() user: any,
    @Body() crawlDto: StartCrawlWithTrackingDto
  ) {
    const userId = user.id;
    this.logger.log(
      `Bắt đầu crawl với tracking cho user: ${userId}, URL: ${crawlDto.url}`
    );

    const result = await this.urlUserService.startCrawlWithTracking(userId, crawlDto);

    this.logger.log(`Đã tạo crawl session: ${result.sessionId}`);

    return ApiResponse.success(result, result.message);
  }

  /**
   * Lấy tiến độ crawl theo session ID
   */
  @ApiOperation({
    summary: 'Lấy tiến độ crawl theo session ID',
    description: 'Theo dõi tiến độ crawl thời gian thực'
  })
  @ApiParam({ name: 'sessionId', description: 'ID của session crawl', type: String })
  @SwaggerApiResponse({
    status: 200,
    description: 'Thông tin tiến độ crawl',
    type: CrawlProgressResponseDto
  })
  @SwaggerApiResponse({ status: 404, description: 'Session không tồn tại' })
  @SwaggerApiResponse({ status: 403, description: 'Không có quyền truy cập session' })
  @Get('crawl/progress/:sessionId')
  async getCrawlProgress(
    @CurrentUser() user: any,
    @Param('sessionId') sessionId: string
  ) {
    const userId = user.id;
    this.logger.debug(`Lấy tiến độ crawl cho session: ${sessionId}, user: ${userId}`);

    const result = await this.urlUserService.getCrawlProgress(userId, sessionId);

    return ApiResponse.success(result);
  }

  /**
   * Lấy danh sách sessions crawl của user
   */
  @ApiOperation({
    summary: 'Lấy danh sách sessions crawl',
    description: 'Lấy danh sách tất cả sessions crawl của người dùng'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Số trang (mặc định: 1)',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng items mỗi trang (mặc định: 20)',
    type: Number,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Lọc theo trạng thái',
    enum: ['running', 'completed', 'error', 'cancelled'],
  })
  @SwaggerApiResponse({
    status: 200,
    description: 'Danh sách sessions crawl',
    type: CrawlSessionListResponseDto
  })
  @Get('crawl/sessions')
  async getUserCrawlSessions(
    @CurrentUser() user: any,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('status') status?: string
  ) {
    const userId = user.id;
    this.logger.debug(
      `Lấy danh sách sessions cho user: ${userId}, page: ${page}, limit: ${limit}, status: ${status}`
    );

    const result = await this.urlUserService.getUserCrawlSessions(
      userId,
      page || 1,
      limit || 20,
      status
    );

    this.logger.debug(`Tìm thấy ${result.total} sessions cho user: ${userId}`);

    return ApiResponse.success(result);
  }

  /**
   * Hủy session crawl đang chạy
   */
  @ApiOperation({
    summary: 'Hủy session crawl đang chạy',
    description: 'Hủy session crawl đang trong trạng thái running'
  })
  @ApiParam({ name: 'sessionId', description: 'ID của session crawl', type: String })
  @SwaggerApiResponse({
    status: 200,
    description: 'Đã hủy session thành công',
    examples: {
      success: {
        summary: 'Hủy thành công',
        value: {
          success: true,
          message: 'Đã hủy session crawl thành công',
          data: true,
        },
      },
    },
  })
  @SwaggerApiResponse({ status: 404, description: 'Session không tồn tại' })
  @SwaggerApiResponse({ status: 403, description: 'Không có quyền hủy session' })
  @SwaggerApiResponse({ status: 400, description: 'Chỉ có thể hủy session đang chạy' })
  @Delete('crawl/sessions/:sessionId')
  async cancelCrawlSession(
    @CurrentUser() user: any,
    @Param('sessionId') sessionId: string
  ) {
    const userId = user.id;
    this.logger.log(`Hủy crawl session: ${sessionId} cho user: ${userId}`);

    const result = await this.urlUserService.cancelCrawlSession(userId, sessionId);

    if (result) {
      this.logger.log(`Đã hủy session thành công: ${sessionId}`);
      return ApiResponse.success(result, 'Đã hủy session crawl thành công');
    } else {
      this.logger.warn(`Không thể hủy session: ${sessionId}`);
      return ApiResponse.success(false, 'Không thể hủy session');
    }
  }
}
