import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class UserAgentRotationService {
  private readonly logger = new Logger(UserAgentRotationService.name);
  
  private currentIndex = 0;
  
  // <PERSON>h sách User-Agent phổ biến để tránh bị detect
  private readonly userAgents = [
    // Chrome on Windows
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',
    
    // Chrome on macOS
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    
    // Firefox on Windows
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0',
    
    // Firefox on macOS
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/20100101 Firefox/120.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:119.0) Gecko/20100101 Firefox/119.0',
    
    // Safari on macOS
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
    
    // Edge on Windows
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    
    // Chrome on Linux
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    
    // Firefox on Linux
    'Mozilla/5.0 (X11; Linux x86_64; rv:120.0) Gecko/20100101 Firefox/120.0',
    'Mozilla/5.0 (X11; Linux x86_64; rv:119.0) Gecko/20100101 Firefox/119.0',
    
    // Mobile User Agents
    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (iPad; CPU OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (Linux; Android 14; SM-G998B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
    'Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36'
  ];

  /**
   * Lấy User-Agent tiếp theo trong rotation
   */
  public getNextUserAgent(): string {
    const userAgent = this.userAgents[this.currentIndex];
    this.currentIndex = (this.currentIndex + 1) % this.userAgents.length;
    
    this.logger.debug(`Using User-Agent: ${userAgent.substring(0, 50)}...`);
    return userAgent;
  }

  /**
   * Lấy User-Agent ngẫu nhiên
   */
  public getRandomUserAgent(): string {
    const randomIndex = Math.floor(Math.random() * this.userAgents.length);
    const userAgent = this.userAgents[randomIndex];
    
    this.logger.debug(`Using random User-Agent: ${userAgent.substring(0, 50)}...`);
    return userAgent;
  }

  /**
   * Lấy User-Agent theo loại browser
   */
  public getUserAgentByBrowser(browser: 'chrome' | 'firefox' | 'safari' | 'edge' | 'mobile' = 'chrome'): string {
    let filteredUserAgents: string[];
    
    switch (browser) {
      case 'chrome':
        filteredUserAgents = this.userAgents.filter(ua => ua.includes('Chrome') && !ua.includes('Edg'));
        break;
      case 'firefox':
        filteredUserAgents = this.userAgents.filter(ua => ua.includes('Firefox'));
        break;
      case 'safari':
        filteredUserAgents = this.userAgents.filter(ua => ua.includes('Safari') && !ua.includes('Chrome'));
        break;
      case 'edge':
        filteredUserAgents = this.userAgents.filter(ua => ua.includes('Edg'));
        break;
      case 'mobile':
        filteredUserAgents = this.userAgents.filter(ua => ua.includes('Mobile') || ua.includes('iPhone') || ua.includes('iPad'));
        break;
      default:
        filteredUserAgents = this.userAgents;
    }
    
    const randomIndex = Math.floor(Math.random() * filteredUserAgents.length);
    const userAgent = filteredUserAgents[randomIndex] || this.userAgents[0];
    
    this.logger.debug(`Using ${browser} User-Agent: ${userAgent.substring(0, 50)}...`);
    return userAgent;
  }

  /**
   * Tạo headers HTTP với User-Agent và các headers phổ biến
   */
  public createHeaders(options: {
    userAgent?: string;
    acceptLanguage?: string;
    acceptEncoding?: string;
    referer?: string;
    browser?: 'chrome' | 'firefox' | 'safari' | 'edge' | 'mobile';
  } = {}): Record<string, string> {
    const userAgent = options.userAgent || 
                     (options.browser ? this.getUserAgentByBrowser(options.browser) : this.getNextUserAgent());
    
    const headers: Record<string, string> = {
      'User-Agent': userAgent,
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
      'Accept-Language': options.acceptLanguage || 'en-US,en;q=0.9,vi;q=0.8',
      'Accept-Encoding': options.acceptEncoding || 'gzip, deflate, br',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'none',
      'Sec-Fetch-User': '?1',
      'Upgrade-Insecure-Requests': '1'
    };

    // Thêm referer nếu có
    if (options.referer) {
      headers['Referer'] = options.referer;
    }

    // Thêm headers đặc biệt cho từng browser
    if (userAgent.includes('Chrome')) {
      headers['sec-ch-ua'] = '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"';
      headers['sec-ch-ua-mobile'] = userAgent.includes('Mobile') ? '?1' : '?0';
      headers['sec-ch-ua-platform'] = this.getPlatformFromUserAgent(userAgent);
    }

    return headers;
  }

  /**
   * Lấy platform từ User-Agent
   */
  private getPlatformFromUserAgent(userAgent: string): string {
    if (userAgent.includes('Windows')) return '"Windows"';
    if (userAgent.includes('Macintosh')) return '"macOS"';
    if (userAgent.includes('Linux')) return '"Linux"';
    if (userAgent.includes('iPhone') || userAgent.includes('iPad')) return '"iOS"';
    if (userAgent.includes('Android')) return '"Android"';
    return '"Unknown"';
  }

  /**
   * Lấy thống kê User-Agent
   */
  public getStats(): any {
    return {
      total: this.userAgents.length,
      currentIndex: this.currentIndex,
      browsers: {
        chrome: this.userAgents.filter(ua => ua.includes('Chrome') && !ua.includes('Edg')).length,
        firefox: this.userAgents.filter(ua => ua.includes('Firefox')).length,
        safari: this.userAgents.filter(ua => ua.includes('Safari') && !ua.includes('Chrome')).length,
        edge: this.userAgents.filter(ua => ua.includes('Edg')).length,
        mobile: this.userAgents.filter(ua => ua.includes('Mobile') || ua.includes('iPhone') || ua.includes('iPad')).length
      }
    };
  }

  /**
   * Thêm User-Agent mới
   */
  public addUserAgent(userAgent: string): void {
    if (!this.userAgents.includes(userAgent)) {
      this.userAgents.push(userAgent);
      this.logger.log(`Added new User-Agent: ${userAgent.substring(0, 50)}...`);
    }
  }

  /**
   * Xóa User-Agent
   */
  public removeUserAgent(userAgent: string): void {
    const index = this.userAgents.indexOf(userAgent);
    if (index !== -1) {
      this.userAgents.splice(index, 1);
      this.logger.log(`Removed User-Agent: ${userAgent.substring(0, 50)}...`);
      
      // Điều chỉnh currentIndex nếu cần
      if (this.currentIndex >= this.userAgents.length) {
        this.currentIndex = 0;
      }
    }
  }
}
