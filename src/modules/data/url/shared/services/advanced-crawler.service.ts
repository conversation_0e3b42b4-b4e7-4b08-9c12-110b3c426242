import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import * as puppeteer from 'puppeteer';
import { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';

/**
 * Advanced Crawler Service sử dụng Puppeteer để crawl các trang web phức tạp
 * Hỗ trợ JavaScript, React, SPA, và dynamic content
 */
@Injectable()
export class AdvancedCrawlerService {
  private readonly logger = new Logger(AdvancedCrawlerService.name);
  private browser: Browser | null = null;
  private readonly MAX_CONCURRENT_PAGES = 5;
  private activePagesCount = 0;

  constructor(private readonly httpService: HttpService) {}

  /**
   * Khởi tạo browser instance
   */
  async initBrowser(): Promise<void> {
    if (!this.browser) {
      this.logger.log('🚀 Initializing Puppeteer browser...');

      this.browser = await puppeteer.launch({
        headless: true, // Chạy ẩn
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor'
        ],
        defaultViewport: {
          width: 1366,
          height: 768
        }
      });

      this.logger.log('✅ Puppeteer browser initialized successfully');
    }
  }

  /**
   * Đóng browser
   */
  async closeBrowser(): Promise<void> {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.logger.log('🔒 Browser closed');
    }
  }

  /**
   * Phát hiện loại trang web
   * @param url URL cần kiểm tra
   * @returns Loại trang web
   */
  async detectWebsiteType(url: string): Promise<{
    type: 'static' | 'spa' | 'dynamic' | 'complex';
    framework?: string;
    needsBrowser: boolean;
    confidence: number;
  }> {
    try {
      this.logger.log(`🔍 Detecting website type for: ${url}`);

      // Kiểm tra URL patterns
      const urlLower = url.toLowerCase();

      // SPA frameworks patterns
      if (urlLower.includes('/#/') || urlLower.includes('#!')) {
        this.logger.log(`📱 SPA pattern detected in URL: ${url}`);
        return { type: 'spa', needsBrowser: true, confidence: 0.9 };
      }

      // Fetch HTML đầu tiên để phân tích
      this.logger.log(`📥 Fetching HTML for analysis: ${url}`);
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      if (!response.ok) {
        this.logger.warn(`❌ HTTP ${response.status} when fetching ${url}`);
        return { type: 'static', needsBrowser: false, confidence: 0.3 };
      }

      const html = await response.text();
      this.logger.log(`📄 HTML fetched successfully, length: ${html.length} chars`);

      // Phân tích HTML content
      const analysis = this.analyzeHtmlContent(html);
      this.logger.log(`🧠 Analysis complete: type=${analysis.type}, framework=${analysis.framework}, needsBrowser=${analysis.needsBrowser}, confidence=${Math.round(analysis.confidence * 100)}%`);

      return analysis;
    } catch (error) {
      this.logger.error(`❌ Error detecting website type for ${url}: ${error.message}`);
      this.logger.error(`Stack trace: ${error.stack}`);
      return { type: 'static', needsBrowser: false, confidence: 0.5 };
    }
  }

  /**
   * Phân tích HTML content để xác định loại trang web
   */
  private analyzeHtmlContent(html: string): {
    type: 'static' | 'spa' | 'dynamic' | 'complex';
    framework?: string;
    needsBrowser: boolean;
    confidence: number;
  } {
    let score = 0;
    let framework = '';
    const detectedFeatures: string[] = [];

    this.logger.log(`🔬 Starting HTML analysis, content length: ${html.length}`);

    // E-commerce platforms - Cần browser để load dynamic content
    if (html.includes('hstatic.net') || html.includes('haravan') || html.includes('Haravan')) {
      score += 40;
      framework = 'Haravan/Shopify';
      detectedFeatures.push('Haravan/Shopify (+40)');
      this.logger.log(`🛒 E-commerce platform detected: Haravan/Shopify`);
    }

    if (html.includes('shopify') || html.includes('Shopify') || html.includes('cdn.shopify.com')) {
      score += 40;
      framework = 'Shopify';
      detectedFeatures.push('Shopify (+40)');
      this.logger.log(`🛒 E-commerce platform detected: Shopify`);
    }

    if (html.includes('woocommerce') || html.includes('WooCommerce')) {
      score += 35;
      framework = 'WooCommerce';
      detectedFeatures.push('WooCommerce (+35)');
      this.logger.log(`🛒 E-commerce platform detected: WooCommerce`);
    }

    if (html.includes('magento') || html.includes('Magento')) {
      score += 35;
      framework = 'Magento';
      detectedFeatures.push('Magento (+35)');
      this.logger.log(`🛒 E-commerce platform detected: Magento`);
    }

    // Server-side rendered với jQuery - Cần browser cho dynamic navigation
    if (html.includes('jquery') || html.includes('jQuery') || html.includes('$')) {
      const jqueryScore = (html.match(/jquery|jQuery|\$/g) || []).length;
      this.logger.log(`📚 jQuery references found: ${jqueryScore}`);
      if (jqueryScore > 3) {
        score += 25;
        framework = framework || 'jQuery';
        detectedFeatures.push(`jQuery (+25, ${jqueryScore} refs)`);
        this.logger.log(`📚 jQuery framework detected with ${jqueryScore} references`);
      }
    }

    // React indicators
    if (html.includes('react') || html.includes('React') || html.includes('__REACT_DEVTOOLS_GLOBAL_HOOK__')) {
      score += 30;
      framework = 'React';
      detectedFeatures.push('React (+30)');
      this.logger.log(`⚛️ React framework detected`);
    }

    // Vue indicators
    if (html.includes('vue') || html.includes('Vue') || html.includes('v-')) {
      score += 30;
      framework = 'Vue';
      detectedFeatures.push('Vue (+30)');
      this.logger.log(`🟢 Vue framework detected`);
    }

    // Angular indicators
    if (html.includes('angular') || html.includes('ng-') || html.includes('Angular')) {
      score += 30;
      framework = 'Angular';
      detectedFeatures.push('Angular (+30)');
      this.logger.log(`🔺 Angular framework detected`);
    }

    // Next.js indicators
    if (html.includes('__NEXT_DATA__') || html.includes('_next/')) {
      score += 35;
      framework = 'Next.js';
      detectedFeatures.push('Next.js (+35)');
      this.logger.log(`▲ Next.js framework detected`);
    }

    // Nuxt.js indicators
    if (html.includes('__NUXT__') || html.includes('nuxt')) {
      score += 35;
      framework = 'Nuxt.js';
      detectedFeatures.push('Nuxt.js (+35)');
      this.logger.log(`💚 Nuxt.js framework detected`);
    }

    // JavaScript heavy indicators
    const scriptTags = (html.match(/<script/g) || []).length;
    this.logger.log(`📜 Script tags found: ${scriptTags}`);
    if (scriptTags > 5) {
      score += 10;
      detectedFeatures.push(`Script tags 5+ (+10)`);
    }
    if (scriptTags > 10) {
      score += 15;
      detectedFeatures.push(`Script tags 10+ (+15)`);
    }
    if (scriptTags > 15) {
      score += 10;
      detectedFeatures.push(`Script tags 15+ (+10)`);
    }

    // Dynamic content indicators
    if (html.includes('data-reactroot') || html.includes('id="root"') || html.includes('id="app"')) {
      score += 20;
      detectedFeatures.push('SPA root elements (+20)');
      this.logger.log(`🎯 SPA root elements detected`);
    }

    // AJAX/Fetch indicators
    if (html.includes('fetch(') || html.includes('axios') || html.includes('$.ajax')) {
      score += 15;
      detectedFeatures.push('AJAX/Fetch (+15)');
      this.logger.log(`🌐 AJAX/Fetch patterns detected`);
    }

    // Bootstrap/CSS frameworks - Thường đi kèm với dynamic content
    if (html.includes('bootstrap') || html.includes('Bootstrap')) {
      score += 10;
      detectedFeatures.push('Bootstrap (+10)');
      this.logger.log(`🎨 Bootstrap framework detected`);
    }

    // Navigation patterns - Thường cần browser để load
    if (html.includes('navbar') || html.includes('nav-') || html.includes('menu')) {
      score += 10;
      detectedFeatures.push('Navigation patterns (+10)');
      this.logger.log(`🧭 Navigation patterns detected`);
    }

    // Form handling - Cần browser cho validation và submission
    if (html.includes('<form') && html.includes('action=')) {
      score += 10;
      detectedFeatures.push('Form handling (+10)');
      this.logger.log(`📝 Form handling detected`);
    }

    // Determine type based on score với threshold thấp hơn cho server-side rendered
    let type: 'static' | 'spa' | 'dynamic' | 'complex';
    let needsBrowser: boolean;

    this.logger.log(`📊 Final score: ${score}, detected features: [${detectedFeatures.join(', ')}]`);

    if (score >= 50) {
      type = 'complex';
      needsBrowser = true;
      this.logger.log(`🔥 Classified as COMPLEX website (score: ${score} >= 50) - WILL USE BROWSER`);
    } else if (score >= 30) {
      type = 'dynamic'; // Thay đổi từ 'spa' thành 'dynamic' cho server-side rendered
      needsBrowser = true;
      this.logger.log(`⚡ Classified as DYNAMIC website (score: ${score} >= 30) - WILL USE BROWSER`);
    } else if (score >= 20) { // Giảm threshold từ 15 xuống 20
      type = 'dynamic';
      needsBrowser = true;
      this.logger.log(`🔄 Classified as DYNAMIC website (score: ${score} >= 20) - WILL USE BROWSER`);
    } else {
      type = 'static';
      needsBrowser = false;
      this.logger.log(`📄 Classified as STATIC website (score: ${score} < 20) - WILL USE HTTP ONLY`);
    }

    const confidence = Math.min(score / 60, 1); // Tăng max score để có confidence chính xác hơn

    this.logger.log(`🎯 Final classification: type=${type}, framework=${framework || 'none'}, needsBrowser=${needsBrowser}, confidence=${Math.round(confidence * 100)}%`);

    return { type, framework, needsBrowser, confidence };
  }

  /**
   * Crawl trang web với Puppeteer - Enhanced for anti-bot websites
   * @param url URL cần crawl
   * @param options Tùy chọn crawl
   * @returns HTML content và URLs
   */
  async crawlWithBrowser(url: string, options: {
    waitForSelector?: string;
    waitTime?: number;
    scrollToBottom?: boolean;
    extractUrls?: boolean;
    takeScreenshot?: boolean;
  } = {}): Promise<{
    html: string;
    urls: string[];
    metadata: any;
    screenshot?: Buffer;
  }> {
    if (this.activePagesCount >= this.MAX_CONCURRENT_PAGES) {
      throw new Error('Too many concurrent pages. Please try again later.');
    }

    await this.initBrowser();

    const page = await this.browser!.newPage();
    this.activePagesCount++;

    try {
      this.logger.log(`🌐 Crawling with browser: ${url}`);

      // Enhanced stealth configuration for anti-bot websites
      await this.setupStealthMode(page);

      // Navigate to page với retry logic
      await this.navigateWithRetry(page, url);

      // Wait for specific selector if provided với timeout dài hơn
      if (options.waitForSelector) {
        try {
          await page.waitForSelector(options.waitForSelector, { timeout: 20000 });
        } catch (e) {
          this.logger.warn(`Waiting for selector \`${options.waitForSelector}\` failed: ${e.message}, continuing anyway...`);
        }
      }

      // Enhanced wait strategy for dynamic content
      await this.waitForDynamicContent(page, options.waitTime);

      // Scroll to bottom to trigger lazy loading
      if (options.scrollToBottom) {
        await this.autoScroll(page);
      }

      // Check for anti-bot challenges
      await this.handleAntiBot(page, url);

      // Debug: Log page info
      await this.debugPageInfo(page, url);

      // Extract content
      const html = await page.content();

      // Extract URLs if requested với enhanced extraction
      let urls: string[] = [];
      if (options.extractUrls) {
        this.logger.log(`🔗 Starting enhanced URL extraction from page...`);
        urls = await this.extractUrlsFromPageEnhanced(page, url);
        this.logger.log(`🔗 URL extraction completed: ${urls.length} URLs found`);

        if (urls.length > 0) {
          this.logger.log(`📋 First 5 URLs found: ${urls.slice(0, 5).join(', ')}`);
        } else {
          this.logger.warn(`⚠️ No URLs extracted from page - trying fallback methods...`);
          // Fallback URL extraction from HTML
          urls = await this.extractUrlsFromHtmlFallback(html, url);
          this.logger.log(`🔄 Fallback extraction found: ${urls.length} URLs`);
        }
      }

      // Enhanced metadata extraction
      this.logger.log(`📊 Extracting enhanced metadata from page...`);
      const metadata = await this.extractMetadataFromPageEnhanced(page);
      this.logger.log(`📊 Metadata extracted: title="${metadata.title}", description="${metadata.description?.substring(0, 50)}..."`);

      // Take screenshot if requested
      let screenshot: Buffer | undefined;
      if (options.takeScreenshot) {
        this.logger.log(`📸 Taking screenshot...`);
        const screenshotBuffer = await page.screenshot({
          fullPage: true,
          type: 'png'
        });
        screenshot = Buffer.from(screenshotBuffer);
        this.logger.log(`📸 Screenshot taken successfully`);
      }

      this.logger.log(`✅ Successfully crawled: ${url} - Found ${urls.length} URLs, metadata: ${metadata.title ? 'yes' : 'no'}`);

      return { html, urls, metadata, screenshot };

    } catch (error) {
      this.logger.error(`❌ Error crawling ${url}: ${error.message}`);
      throw error;
    } finally {
      await page.close();
      this.activePagesCount--;
    }
  }

  /**
   * Enhanced stealth mode setup for anti-bot websites
   */
  private async setupStealthMode(page: Page): Promise<void> {
    // Set realistic user agent
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

    // Set viewport
    await page.setViewport({ width: 1366, height: 768 });

    // Block unnecessary resources để tăng tốc và giảm detection
    await page.setRequestInterception(true);
    page.on('request', (req) => {
      const resourceType = req.resourceType();
      if (['image', 'stylesheet', 'font', 'media'].includes(resourceType)) {
        req.abort();
      } else {
        req.continue();
      }
    });

    // Override navigator properties to avoid detection
    await page.evaluateOnNewDocument(() => {
      // Override the `plugins` property to use a custom getter.
      Object.defineProperty(navigator, 'plugins', {
        get: () => [1, 2, 3, 4, 5],
      });

      // Override the `languages` property to use a custom getter.
      Object.defineProperty(navigator, 'languages', {
        get: () => ['en-US', 'en'],
      });

      // Override the `webdriver` property to remove it.
      Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
      });
    });
  }

  /**
   * Navigate with retry logic for better reliability
   */
  private async navigateWithRetry(page: Page, url: string, maxRetries = 3): Promise<void> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        this.logger.log(`🚀 Navigation attempt ${attempt}/${maxRetries} to: ${url}`);

        await page.goto(url, {
          waitUntil: 'networkidle2',
          timeout: 45000 // Tăng timeout lên 45s
        });

        this.logger.log(`✅ Navigation successful on attempt ${attempt}`);
        return;
      } catch (error) {
        this.logger.warn(`❌ Navigation attempt ${attempt} failed: ${error.message}`);

        if (attempt === maxRetries) {
          throw new Error(`Failed to navigate after ${maxRetries} attempts: ${error.message}`);
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 2000 * attempt));
      }
    }
  }

  /**
   * Enhanced wait strategy for dynamic content - Special handling for complex sites
   */
  private async waitForDynamicContent(page: Page, customWaitTime?: number): Promise<void> {
    // Wait for initial load
    await new Promise(resolve => setTimeout(resolve, 3000)); // Tăng từ 2s lên 3s

    // Custom wait time if provided
    if (customWaitTime) {
      await new Promise(resolve => setTimeout(resolve, customWaitTime));
    }

    // Wait for common dynamic content indicators với timeout dài hơn
    const dynamicSelectors = [
      // E-commerce specific selectors
      '.shopee-header', '.navbar', '.navigation', '.header',
      '.product-list', '.category-list', '.main-content',
      // General selectors
      'main', '.content', '.container', '.wrapper',
      '#app', '#root', '[data-testid]'
    ];

    let foundSelector = false;
    for (const selector of dynamicSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 5000 }); // Tăng từ 3s lên 5s
        this.logger.debug(`Found dynamic content indicator: ${selector}`);
        foundSelector = true;
        break;
      } catch (e) {
        // Continue to next selector
      }
    }

    // Nếu không tìm thấy selector nào, chờ thêm cho JavaScript load
    if (!foundSelector) {
      this.logger.warn(`No dynamic content selectors found, waiting additional time for JS...`);
      await new Promise(resolve => setTimeout(resolve, 5000));
    }

    // Wait for network to be mostly idle (Puppeteer doesn't have waitForLoadState)
    try {
      await page.waitForFunction(
        () => {
          return (performance as any).timing.loadEventEnd > 0;
        },
        { timeout: 8000 }
      );
      this.logger.debug(`Page load event completed`);
    } catch (e) {
      this.logger.debug(`Page load wait timeout: ${e.message}`);
    }

    // Additional wait for heavy JavaScript apps
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  /**
   * Debug page information for troubleshooting
   */
  private async debugPageInfo(page: Page, url: string): Promise<void> {
    try {
      const pageInfo = await page.evaluate(() => {
        return {
          title: document.title,
          bodyText: document.body.innerText.substring(0, 200),
          anchorsCount: document.querySelectorAll('a[href]').length,
          scriptsCount: document.querySelectorAll('script').length,
          hasAngular: !!(window as any).ng || document.querySelector('[ng-version]'),
          hasReact: !!(window as any).React || document.querySelector('[data-reactroot]'),
          hasVue: !!(window as any).Vue || document.querySelector('[data-v-]'),
          hasShopee: document.querySelector('.shopee-header, .shopee-navbar, [class*="shopee"]'),
          readyState: document.readyState,
          url: window.location.href
        };
      });

      this.logger.debug(`🔍 Page Debug Info for ${url}:`);
      this.logger.debug(`   Title: "${pageInfo.title}"`);
      this.logger.debug(`   Body text: "${pageInfo.bodyText}..."`);
      this.logger.debug(`   Anchors: ${pageInfo.anchorsCount}, Scripts: ${pageInfo.scriptsCount}`);
      this.logger.debug(`   Frameworks: Angular=${pageInfo.hasAngular}, React=${pageInfo.hasReact}, Vue=${pageInfo.hasVue}`);
      this.logger.debug(`   Shopee elements: ${!!pageInfo.hasShopee}`);
      this.logger.debug(`   Ready state: ${pageInfo.readyState}`);
      this.logger.debug(`   Current URL: ${pageInfo.url}`);
    } catch (error) {
      this.logger.debug(`Debug page info failed: ${error.message}`);
    }
  }

  /**
   * Handle anti-bot challenges
   */
  private async handleAntiBot(page: Page, url: string): Promise<void> {
    try {
      // Check for common anti-bot indicators
      const antiBot = await page.evaluate(() => {
        const indicators = [
          'cloudflare', 'captcha', 'challenge', 'verification',
          'bot-detection', 'access-denied', 'blocked'
        ];

        const bodyText = document.body.innerText.toLowerCase();
        const title = document.title.toLowerCase();

        return indicators.some(indicator =>
          bodyText.includes(indicator) || title.includes(indicator)
        );
      });

      if (antiBot) {
        this.logger.warn(`🚫 Anti-bot challenge detected for ${url}`);

        // Wait longer for potential auto-resolution
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Try to find and click continue buttons
        const continueSelectors = [
          'button[type="submit"]',
          'input[type="submit"]',
          '.continue-btn',
          '.proceed-btn',
          '#continue',
          '[data-testid="continue"]'
        ];

        for (const selector of continueSelectors) {
          try {
            await page.click(selector);
            this.logger.log(`🔄 Clicked continue button: ${selector}`);
            await new Promise(resolve => setTimeout(resolve, 3000));
            break;
          } catch (e) {
            // Continue to next selector
          }
        }
      }
    } catch (error) {
      this.logger.debug(`Anti-bot check failed: ${error.message}`);
    }
  }

  /**
   * Auto scroll để trigger lazy loading
   */
  private async autoScroll(page: Page): Promise<void> {
    await page.evaluate(async () => {
      await new Promise<void>((resolve) => {
        let totalHeight = 0;
        const distance = 100;
        const maxScrolls = 50; // Giới hạn số lần scroll
        let scrollCount = 0;

        const timer = setInterval(() => {
          const scrollHeight = document.body.scrollHeight;
          window.scrollBy(0, distance);
          totalHeight += distance;
          scrollCount++;

          if (totalHeight >= scrollHeight || scrollCount >= maxScrolls) {
            clearInterval(timer);
            resolve();
          }
        }, 100);
      });
    });
  }

  /**
   * Trích xuất URLs từ page với JavaScript - Cải thiện cho server-side rendered
   */
  private async extractUrlsFromPage(page: Page, baseUrl: string): Promise<string[]> {
    return await page.evaluate((baseUrl) => {
      const urls = new Set<string>();
      const base = new URL(baseUrl);

      // Extract from various sources - Mở rộng cho server-side rendered
      const selectors = [
        'a[href]',
        '[data-href]',
        '[data-url]',
        '[data-link]',
        '[data-target]',
        'area[href]',
        'link[href]',
        'form[action]',
        'iframe[src]',
        // Navigation specific selectors
        'nav a[href]',
        '.navbar a[href]',
        '.menu a[href]',
        '.navigation a[href]',
        // E-commerce specific selectors
        '.product-item a[href]',
        '.product-link[href]',
        '.category-link[href]',
        // Pagination selectors
        '.pagination a[href]',
        '.pager a[href]',
        '.page-numbers a[href]'
      ];

      selectors.forEach(selector => {
        document.querySelectorAll(selector).forEach(el => {
          const href = el.getAttribute('href') || el.getAttribute('data-href') ||
                      el.getAttribute('data-url') || el.getAttribute('data-link') ||
                      el.getAttribute('data-target') || el.getAttribute('action') ||
                      el.getAttribute('src');
          if (href) {
            try {
              // Skip invalid URLs
              if (href.startsWith('#') || href.startsWith('javascript:') ||
                  href.startsWith('mailto:') || href.startsWith('tel:') ||
                  href.includes('void(0)') || href.trim() === '') {
                return;
              }

              const url = new URL(href, baseUrl);
              if (url.hostname === base.hostname) {
                urls.add(url.href);
              }
            } catch (e) {
              // Invalid URL
            }
          }
        });
      });

      // Extract URLs from onclick events
      document.querySelectorAll('[onclick]').forEach(el => {
        const onclick = el.getAttribute('onclick');
        if (onclick) {
          const patterns = [
            /(?:location\.href|window\.location|goto|redirect)\s*=\s*['"`]([^'"`]+)['"`]/gi,
            /window\.open\s*\(\s*['"`]([^'"`]+)['"`]/gi,
            /navigate\s*\(\s*['"`]([^'"`]+)['"`]/gi,
            /href\s*=\s*['"`]([^'"`]+)['"`]/gi
          ];

          patterns.forEach(pattern => {
            const matches = onclick.match(pattern);
            if (matches) {
              matches.forEach(match => {
                const urlMatch = match.match(/['"`]([^'"`]+)['"`]/);
                if (urlMatch && urlMatch[1]) {
                  try {
                    const url = new URL(urlMatch[1], baseUrl);
                    if (url.hostname === base.hostname) {
                      urls.add(url.href);
                    }
                  } catch (e) {
                    // Invalid URL
                  }
                }
              });
            }
          });
        }
      });

      // Extract URLs from JavaScript variables (common in server-side rendered sites)
      const scripts = document.querySelectorAll('script');
      scripts.forEach(script => {
        const content = script.textContent || script.innerHTML;
        if (content) {
          const patterns = [
            /['"`](https?:\/\/[^'"`\s]+)['"`]/g,
            /url\s*:\s*['"`]([^'"`]+)['"`]/g,
            /href\s*:\s*['"`]([^'"`]+)['"`]/g,
            /link\s*:\s*['"`]([^'"`]+)['"`]/g
          ];

          patterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
              const foundUrl = match[1];
              if (foundUrl && (foundUrl.startsWith('http://') || foundUrl.startsWith('https://') || foundUrl.startsWith('/'))) {
                try {
                  const url = new URL(foundUrl, baseUrl);
                  if (url.hostname === base.hostname) {
                    urls.add(url.href);
                  }
                } catch (e) {
                  // Invalid URL
                }
              }
            }
          });
        }
      });

      return Array.from(urls);
    }, baseUrl);
  }

  /**
   * Enhanced URL extraction from page với multiple strategies - Optimized for SPA/Angular
   */
  private async extractUrlsFromPageEnhanced(page: Page, baseUrl: string): Promise<string[]> {
    try {
      // Wait a bit more for dynamic content to load
      await new Promise(resolve => setTimeout(resolve, 2000));

      const urls = await page.evaluate((base) => {
        const links: string[] = [];
        const baseUrlObj = new URL(base);

        // Strategy 1: Standard anchor tags với comprehensive selectors
        const anchorSelectors = [
          'a[href]', 'a[routerLink]', 'a[ng-href]', 'a[ui-sref]',
          '.link[href]', '.nav-link[href]', '.menu-item[href]'
        ];

        anchorSelectors.forEach(selector => {
          document.querySelectorAll(selector).forEach((anchor) => {
            const href = anchor.getAttribute('href') ||
                        anchor.getAttribute('routerLink') ||
                        anchor.getAttribute('ng-href') ||
                        anchor.getAttribute('ui-sref');
            if (href && !href.startsWith('#') && !href.startsWith('javascript:')) {
              try {
                const fullUrl = new URL(href, base).href;
                const urlObj = new URL(fullUrl);
                if (urlObj.hostname === baseUrlObj.hostname) {
                  links.push(fullUrl);
                }
              } catch (e) {
                // Ignore invalid URLs
              }
            }
          });
        });

        // Strategy 2: Data attributes và Angular/React specific
        const dataSelectors = [
          '[data-href]', '[data-url]', '[data-link]', '[data-route]',
          '[data-testid*="link"]', '[data-cy*="link"]',
          '[routerLink]', '[ng-href]', '[ui-sref]'
        ];

        dataSelectors.forEach(selector => {
          document.querySelectorAll(selector).forEach((element) => {
            const dataHref = element.getAttribute('data-href') ||
                            element.getAttribute('data-url') ||
                            element.getAttribute('data-link') ||
                            element.getAttribute('data-route') ||
                            element.getAttribute('routerLink') ||
                            element.getAttribute('ng-href') ||
                            element.getAttribute('ui-sref');
            if (dataHref && !dataHref.startsWith('#')) {
              try {
                const fullUrl = new URL(dataHref, base).href;
                const urlObj = new URL(fullUrl);
                if (urlObj.hostname === baseUrlObj.hostname) {
                  links.push(fullUrl);
                }
              } catch (e) {
                // Ignore invalid URLs
              }
            }
          });
        });

        // Strategy 3: Enhanced JavaScript extraction for SPA
        const scripts = document.querySelectorAll('script');
        scripts.forEach((script) => {
          const content = script.textContent || '';

          // More comprehensive URL patterns for SPA
          const patterns = [
            // Standard URL patterns
            /["']([^"']*\/[^"']*?)["']/g,
            // Route patterns
            /route[s]?\s*:\s*["']([^"']+)["']/gi,
            /path[s]?\s*:\s*["']([^"']+)["']/gi,
            // Navigation patterns
            /navigate\s*\(\s*["']([^"']+)["']/gi,
            /router\s*\.\s*push\s*\(\s*["']([^"']+)["']/gi,
            // API endpoints that might be pages
            /api\/[^"'\s]+/g,
            // Relative paths
            /\/[a-zA-Z0-9\-_\/]+(?=["'\s])/g
          ];

          patterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
              const foundUrl = match[1] || match[0];
              if (foundUrl && (foundUrl.startsWith('/') || foundUrl.includes(baseUrlObj.hostname))) {
                // Skip common non-page URLs
                if (!foundUrl.includes('.js') && !foundUrl.includes('.css') &&
                    !foundUrl.includes('.png') && !foundUrl.includes('.jpg') &&
                    !foundUrl.includes('api/') && !foundUrl.includes('_next/')) {
                  try {
                    const fullUrl = new URL(foundUrl, base).href;
                    const urlObj = new URL(fullUrl);
                    if (urlObj.hostname === baseUrlObj.hostname) {
                      links.push(fullUrl);
                    }
                  } catch (e) {
                    // Ignore invalid URLs
                  }
                }
              }
            }
          });
        });

        // Strategy 4: Enhanced navigation và interactive elements
        const interactiveSelectors = [
          'nav a', '.navbar a', '.navigation a', '.menu a',
          '.header a', '.footer a', '.sidebar a',
          'button[onclick]', '[role="button"][onclick]',
          '.btn[href]', '.button[href]',
          // E-commerce specific
          '.category-link', '.product-link', '.brand-link',
          '.shopee-header a', '.shopee-navbar a'
        ];

        interactiveSelectors.forEach(selector => {
          document.querySelectorAll(selector).forEach((element) => {
            if (element.tagName === 'BUTTON' || element.hasAttribute('onclick')) {
              const onclick = element.getAttribute('onclick') || '';
              const urlPatterns = [
                /location\.href\s*=\s*["']([^"']+)["']/,
                /window\.location\s*=\s*["']([^"']+)["']/,
                /navigate\s*\(\s*["']([^"']+)["']/,
                /goto\s*\(\s*["']([^"']+)["']/
              ];

              urlPatterns.forEach(pattern => {
                const urlMatch = onclick.match(pattern);
                if (urlMatch && urlMatch[1]) {
                  try {
                    const fullUrl = new URL(urlMatch[1], base).href;
                    const urlObj = new URL(fullUrl);
                    if (urlObj.hostname === baseUrlObj.hostname) {
                      links.push(fullUrl);
                    }
                  } catch (e) {
                    // Ignore invalid URLs
                  }
                }
              });
            }
          });
        });

        // Strategy 5: Extract from window object và global variables
        try {
          // Check for common SPA routing data
          const windowObj = window as any;
          if (windowObj.__NEXT_DATA__?.props?.pageProps) {
            // Next.js routing data
            const pageProps = windowObj.__NEXT_DATA__.props.pageProps;
            // Extract any URL-like properties
          }

          if (windowObj.__NUXT__) {
            // Nuxt.js routing data
          }

          // Check for Angular routing
          if (windowObj.ng) {
            // Angular routing data
          }
        } catch (e) {
          // Ignore errors accessing window object
        }

        return [...new Set(links)]; // Remove duplicates
      }, baseUrl);

      this.logger.debug(`Enhanced extraction found ${urls.length} URLs`);

      // Log some sample URLs for debugging
      if (urls.length > 0) {
        this.logger.debug(`Sample URLs: ${urls.slice(0, 3).join(', ')}`);
      } else {
        this.logger.warn(`No URLs found - this might indicate heavy anti-bot protection`);
      }

      return urls;
    } catch (error) {
      this.logger.error(`Error in enhanced URL extraction: ${error.message}`);
      return [];
    }
  }

  /**
   * Fallback URL extraction from HTML string
   */
  private async extractUrlsFromHtmlFallback(html: string, baseUrl: string): Promise<string[]> {
    try {
      const cheerio = require('cheerio');
      const $ = cheerio.load(html);
      const baseUrlObj = new URL(baseUrl);
      const urls: string[] = [];

      // Extract from anchor tags
      $('a[href]').each((_, element) => {
        const href = $(element).attr('href');
        if (href) {
          try {
            const fullUrl = new URL(href, baseUrl).href;
            const urlObj = new URL(fullUrl);
            if (urlObj.hostname === baseUrlObj.hostname) {
              urls.push(fullUrl);
            }
          } catch (e) {
            // Ignore invalid URLs
          }
        }
      });

      // Extract from data attributes
      $('[data-href], [data-url], [data-link]').each((_, element) => {
        const dataHref = $(element).attr('data-href') ||
                        $(element).attr('data-url') ||
                        $(element).attr('data-link');
        if (dataHref) {
          try {
            const fullUrl = new URL(dataHref, baseUrl).href;
            const urlObj = new URL(fullUrl);
            if (urlObj.hostname === baseUrlObj.hostname) {
              urls.push(fullUrl);
            }
          } catch (e) {
            // Ignore invalid URLs
          }
        }
      });

      return [...new Set(urls)];
    } catch (error) {
      this.logger.error(`Error in fallback URL extraction: ${error.message}`);
      return [];
    }
  }

  /**
   * Enhanced metadata extraction from page
   */
  private async extractMetadataFromPageEnhanced(page: Page): Promise<any> {
    return await page.evaluate(() => {
      const getMetaContent = (name: string) => {
        const meta = document.querySelector(`meta[name="${name}"], meta[property="${name}"]`);
        return meta ? meta.getAttribute('content') : null;
      };

      // Try to get title from multiple sources
      let title = document.title;
      if (!title || title.trim() === '') {
        title = getMetaContent('og:title') ||
                getMetaContent('twitter:title') ||
                document.querySelector('h1')?.textContent || '';
      }

      // Try to get description from multiple sources
      let description = getMetaContent('description') ||
                       getMetaContent('og:description') ||
                       getMetaContent('twitter:description') || '';

      // If still no description, try to extract from page content
      if (!description || description.trim() === '') {
        const contentSelectors = [
          '.description', '.summary', '.excerpt',
          '.intro', '.lead', 'p:first-of-type'
        ];

        for (const selector of contentSelectors) {
          const element = document.querySelector(selector);
          if (element && element.textContent) {
            description = element.textContent.trim().substring(0, 200);
            break;
          }
        }
      }

      return {
        title: title.trim(),
        description: description.trim(),
        keywords: getMetaContent('keywords'),
        ogTitle: getMetaContent('og:title'),
        ogDescription: getMetaContent('og:description'),
        ogImage: getMetaContent('og:image'),
        canonical: document.querySelector('link[rel="canonical"]')?.getAttribute('href'),
        lang: document.documentElement.lang,
        charset: document.characterSet
      };
    });
  }

  /**
   * Trích xuất metadata từ page
   */
  private async extractMetadataFromPage(page: Page): Promise<any> {
    return await page.evaluate(() => {
      const getMetaContent = (name: string) => {
        const meta = document.querySelector(`meta[name="${name}"], meta[property="${name}"]`);
        return meta ? meta.getAttribute('content') : null;
      };

      return {
        title: document.title,
        description: getMetaContent('description') || getMetaContent('og:description'),
        keywords: getMetaContent('keywords'),
        ogTitle: getMetaContent('og:title'),
        ogDescription: getMetaContent('og:description'),
        ogImage: getMetaContent('og:image'),
        canonical: document.querySelector('link[rel="canonical"]')?.getAttribute('href'),
        lang: document.documentElement.lang,
        charset: document.characterSet
      };
    });
  }
}
