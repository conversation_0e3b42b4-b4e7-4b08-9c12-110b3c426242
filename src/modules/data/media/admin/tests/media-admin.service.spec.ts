import { Test, TestingModule } from '@nestjs/testing';
import { MediaAdminService } from '../services/media-admin.service';
import { MediaRepository } from '../../repositories';
import { S3Service } from '@shared/services/s3.service';
import { AgentMediaRepository } from '@modules/agent/repositories';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from '@modules/user/entities';
import { Repository, DeleteResult } from 'typeorm';
import { MediaQueryDto } from '@/modules/data/media/dto/media-query.dto';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { AdminMediaResponseDto } from '@/modules/data/media/dto/media-user.dto';
import { Media } from '@modules/data/media/entities/media.entity';
import { AppException, ErrorCode } from '@/common';
import { SortDirection } from '@common/dto';
import { MediaStatusEnum } from '@modules/data/media/enums/media-status.enum';

describe('MediaAdminService', () => {
  let service: MediaAdminService;
  let mediaRepository: jest.Mocked<MediaRepository>;
  let s3Service: jest.Mocked<S3Service>;
  let agentMediaRepository: jest.Mocked<AgentMediaRepository>;
  let userRepository: jest.Mocked<Repository<User>>;

  const mockMedia: Media = {
    id: 'media-id-1',
    name: 'Test Media',
    description: 'Test Description',
    status: MediaStatusEnum.APPROVED,
    ownedBy: 1,
    createdAt: new Date().getTime(),
    updatedAt: new Date().getTime(),
    storageKey: 'test-storage-key',
    size: 1024,
    tags: ['test', 'image'],
    nameEmbedding: [],
    descriptionEmbedding: [],
  };

  const mockUser = {
    id: 1,
    fullName: 'Test User',
    avatar: 'https://example.com/avatar.jpg',
    email: '<EMAIL>',
    phoneNumber: '**********',
    isActive: true,
    isVerifyEmail: true,
  } as User;

  beforeEach(async () => {
    // Create mocks
    const mediaRepositoryMock = {
      createQueryBuilder: jest.fn(),
      findOne: jest.fn(),
    };

    const s3ServiceMock = {
      getDownloadUrl: jest.fn(),
    };

    const agentMediaRepositoryMock = {
      delete: jest.fn(),
    };

    const userRepositoryMock = {
      findBy: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MediaAdminService,
        {
          provide: MediaRepository,
          useValue: mediaRepositoryMock,
        },
        {
          provide: S3Service,
          useValue: s3ServiceMock,
        },
        {
          provide: AgentMediaRepository,
          useValue: agentMediaRepositoryMock,
        },
        {
          provide: getRepositoryToken(User),
          useValue: userRepositoryMock,
        },
      ],
    }).compile();

    service = module.get<MediaAdminService>(MediaAdminService);
    mediaRepository = module.get(MediaRepository) as jest.Mocked<MediaRepository>;
    s3Service = module.get(S3Service) as jest.Mocked<S3Service>;
    agentMediaRepository = module.get(AgentMediaRepository) as jest.Mocked<AgentMediaRepository>;
    userRepository = module.get(getRepositoryToken(User)) as jest.Mocked<Repository<User>>;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAllForAdmin', () => {
    it('should return paginated media list', async () => {
      // Arrange
      const query: MediaQueryDto = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirection.DESC,
      };

      const mockQueryBuilder = {
        leftJoin: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockMedia], 1]),
      };

      mediaRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);
      userRepository.findBy.mockResolvedValue([mockUser]);

      // Act
      const result = await service.findAllForAdmin(query, true);

      // Assert
      expect(mediaRepository.createQueryBuilder).toHaveBeenCalledWith('media');
      expect(mockQueryBuilder.leftJoin).toHaveBeenCalledWith('users', 'user', 'user.id = media.owned_by');
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(`media.${query.sortBy}`, query.sortDirection);
      expect(userRepository.findBy).toHaveBeenCalledWith({ id: expect.any(Object) });
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result?.items[0]).toHaveProperty('author', mockUser.fullName);
      expect(result.result?.items[0]).toHaveProperty('avatar', mockUser.avatar);
    });

    it('should filter by search term when provided', async () => {
      // Arrange
      const query: MediaQueryDto = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirection.DESC,
        search: 'test',
      };

      const mockQueryBuilder = {
        leftJoin: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockMedia], 1]),
      };

      mediaRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);
      userRepository.findBy.mockResolvedValue([mockUser]);

      // Act
      await service.findAllForAdmin(query, true);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'media.name ILIKE :search OR media.description ILIKE :search',
        { search: `%${query.search}%` }
      );
    });

    it('should filter by status when provided', async () => {
      // Arrange
      const query: MediaQueryDto = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirection.DESC,
        status: MediaStatusEnum.APPROVED,
      };

      const mockQueryBuilder = {
        leftJoin: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockMedia], 1]),
      };

      mediaRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);
      userRepository.findBy.mockResolvedValue([mockUser]);

      // Act
      await service.findAllForAdmin(query, true);

      // Assert
      expect(mockQueryBuilder.andWhere).toHaveBeenCalledWith(
        'media.status = :status',
        { status: query.status }
      );
    });
  });

  describe('findByIdForAdmin', () => {
    it('should return media by id', async () => {
      // Arrange
      const mediaId = 'media-id-1';

      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(mockMedia),
      };

      mediaRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

      // Act
      const result = await service.findByIdForAdmin(mediaId, true);

      // Assert
      expect(mediaRepository.createQueryBuilder).toHaveBeenCalledWith('media');
      expect(mockQueryBuilder.where).toHaveBeenCalledWith('media.id = :id', { id: mediaId });
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.result).toEqual(mockMedia);
    });

    it('should throw exception when media not found', async () => {
      // Arrange
      const mediaId = 'non-existent-id';

      const mockQueryBuilder = {
        where: jest.fn().mockReturnThis(),
        getOne: jest.fn().mockResolvedValue(null),
      };

      mediaRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder as any);

      // Act & Assert
      await expect(service.findByIdForAdmin(mediaId, true)).rejects.toThrow(AppException);
      await expect(service.findByIdForAdmin(mediaId, true)).rejects.toThrow(
        new AppException(ErrorCode.MEDIA_NOT_FOUND, `Media với id ${mediaId} không tồn tại.`)
      );
    });
  });

  describe('deleteAgentMediaByAdmin', () => {
    it('should delete agent media records', async () => {
      // Arrange
      const mediaIds = ['media-id-1', 'media-id-2'];

      const mockMedia1 = { ...mockMedia, id: 'media-id-1' };
      const mockMedia2 = { ...mockMedia, id: 'media-id-2' };

      mediaRepository.findOne.mockResolvedValueOnce(mockMedia1);
      mediaRepository.findOne.mockResolvedValueOnce(mockMedia2);

      const deleteResult: Partial<DeleteResult> = { affected: 1, raw: {} };
      agentMediaRepository.delete.mockResolvedValueOnce(deleteResult as DeleteResult);
      agentMediaRepository.delete.mockResolvedValueOnce(deleteResult as DeleteResult);

      // Act
      const result = await service.deleteAgentMediaByAdmin(mediaIds, true);

      // Assert
      expect(mediaRepository.findOne).toHaveBeenCalledTimes(2);
      expect(agentMediaRepository.delete).toHaveBeenCalledTimes(2);
      expect(agentMediaRepository.delete).toHaveBeenCalledWith({ mediaId: 'media-id-1' });
      expect(agentMediaRepository.delete).toHaveBeenCalledWith({ mediaId: 'media-id-2' });
      expect(result).toEqual({
        deletedIds: ['media-id-1', 'media-id-2'],
        skippedIds: [],
        failedIds: [],
      });
    });

    it('should handle media not found', async () => {
      // Arrange
      const mediaIds = ['media-id-1', 'non-existent-id'];

      const mockMedia1 = { ...mockMedia, id: 'media-id-1' };

      mediaRepository.findOne.mockResolvedValueOnce(mockMedia1);
      mediaRepository.findOne.mockResolvedValueOnce(null);

      const deleteResult: Partial<DeleteResult> = { affected: 1, raw: {} };
      agentMediaRepository.delete.mockResolvedValueOnce(deleteResult as DeleteResult);

      // Act
      const result = await service.deleteAgentMediaByAdmin(mediaIds, true);

      // Assert
      expect(mediaRepository.findOne).toHaveBeenCalledTimes(2);
      expect(agentMediaRepository.delete).toHaveBeenCalledTimes(1);
      expect(result).toEqual({
        deletedIds: ['media-id-1'],
        skippedIds: ['non-existent-id'],
        failedIds: [],
      });
    });

    it('should handle deletion exceptions', async () => {
      // Arrange
      const mediaIds = ['media-id-1', 'error-id'];

      const mockMedia1 = { ...mockMedia, id: 'media-id-1' };
      const mockMedia2 = { ...mockMedia, id: 'error-id' };

      mediaRepository.findOne.mockResolvedValueOnce(mockMedia1);
      mediaRepository.findOne.mockResolvedValueOnce(mockMedia2);

      const deleteResult: Partial<DeleteResult> = { affected: 1, raw: {} };
      agentMediaRepository.delete.mockResolvedValueOnce(deleteResult as DeleteResult);
      agentMediaRepository.delete.mockRejectedValueOnce(new Error('Database error'));

      // Act
      const result = await service.deleteAgentMediaByAdmin(mediaIds, true);

      // Assert
      expect(mediaRepository.findOne).toHaveBeenCalledTimes(2);
      expect(agentMediaRepository.delete).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        deletedIds: ['media-id-1'],
        skippedIds: [],
        failedIds: ['error-id'],
      });
    });

    it('should handle no affected rows', async () => {
      // Arrange
      const mediaIds = ['media-id-1', 'no-relation-id'];

      const mockMedia1 = { ...mockMedia, id: 'media-id-1' };
      const mockMedia2 = { ...mockMedia, id: 'no-relation-id' };

      mediaRepository.findOne.mockResolvedValueOnce(mockMedia1);
      mediaRepository.findOne.mockResolvedValueOnce(mockMedia2);

      const deleteResult1: Partial<DeleteResult> = { affected: 1, raw: {} };
      const deleteResult2: Partial<DeleteResult> = { affected: 0, raw: {} };
      agentMediaRepository.delete.mockResolvedValueOnce(deleteResult1 as DeleteResult);
      agentMediaRepository.delete.mockResolvedValueOnce(deleteResult2 as DeleteResult);

      // Act
      const result = await service.deleteAgentMediaByAdmin(mediaIds, true);

      // Assert
      expect(mediaRepository.findOne).toHaveBeenCalledTimes(2);
      expect(agentMediaRepository.delete).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        deletedIds: ['media-id-1'],
        skippedIds: ['no-relation-id'],
        failedIds: [],
      });
    });
  });
});
