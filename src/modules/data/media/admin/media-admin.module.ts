import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MediaAdminController } from './controllers/media-admin.controller';
import { MediaAdminService } from './services/media-admin.service';
import { Media } from '../entities/media.entity';
import { MediaRepository } from '../repositories';
import {DataSource} from "typeorm";
import {User} from "@modules/user/entities";
import { AgentMediaRepository } from '@modules/agent/repositories/agent-media.repository';
import { MediaValidationHelper } from '../helpers/validation.helper';

@Module({
  imports: [TypeOrmModule.forFeature([Media,User])],
  controllers: [MediaAdminController],
  providers: [MediaAdminService,
    {
      provide: MediaRepository,
      useFactory: (dataSource: DataSource) => {
        return new MediaRepository(dataSource);
      },
      inject: [DataSource],
    },
    {
      provide: AgentMediaRepository,
      useFactory: (dataSource: DataSource) => {
        return new AgentMediaRepository(dataSource);
      },
      inject: [DataSource],
    },
    MediaValidationHelper,
    
  ],
  exports: [MediaAdminService],
})
export class MediaAdminModule {}
