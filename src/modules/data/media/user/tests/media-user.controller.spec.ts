// import { Test, TestingModule } from '@nestjs/testing';
// import { MediaUserController } from '../controllers/media-user.controller';
// import { MediaUserService } from '../services/media-user.service';
// import { MediaRepository } from '../../repositories';
// import { MediaQueryDto } from '../../dto/media-query.dto';
// import { ApiResponseDto, PaginatedResult } from '@common/response';
// import { Media } from '../../entities/media.entity';
// import { JwtUserGuard } from '@modules/auth/guards';
// import { JwtPayload, TokenType } from '@modules/auth/guards/jwt.util';
// import { MediaDto, MediaResponseDto } from '../../dto/media-user.dto';
// import { SortDirection } from '@common/dto';
// import { MediaStatusEnum } from '@modules/data/media/enums/media-status.enum';
// import { AppException, ErrorCode } from '@/common';

// describe('MediaUserController', () => {
//   let controller: MediaUserController;
//   let mediaUserService: jest.Mocked<MediaUserService>;
//   let mediaRepository: jest.Mocked<MediaRepository>;

//   const mockMedia: Media = {
//     id: 'media-id-1',
//     name: 'Test Media',
//     description: 'Test Description',
//     status: MediaStatusEnum.APPROVED,
//     ownedBy: 1,
//     createdAt: new Date().getTime(),
//     updatedAt: new Date().getTime(),
//     storageKey: 'test-storage-key',
//     size: 1024,
//     tags: ['test', 'image'],
//     nameEmbedding: [],
//     descriptionEmbedding: [],
//   };

//   const mockUser: JwtPayload = {
//     id: 1,
//     sub: 1,
//     username: '<EMAIL>',
//     isAdmin: false,
//     typeToken: TokenType.ACCESS,
//   };

//   const mockMediaResponse: MediaResponseDto = {
//     id: 'media-id-1',
//     name: 'Test Media',
//     description: 'Test Description',
//     tags: ['test', 'image'],
//     author: 'Test User',
//     avatar: 'https://example.com/avatar.jpg',
//     downloadURL: 'https://example.com/presigned',
//     viewURL: 'https://example.com/download',
//   };

//   beforeEach(async () => {
//     // Create mocks
//     const mediaUserServiceMock = {
//       findAllByUser: jest.fn(),
//       findById: jest.fn(),
//       createPresignedUrlsFromMediaList: jest.fn(),
//       deleteManyByUser: jest.fn(),
//       handleTransitionAndDelete: jest.fn(),
//       deleteAgentMediaByUser: jest.fn(),
//     };

//     const mediaRepositoryMock = {
//       createQueryBuilder: jest.fn(),
//     };

//     const module: TestingModule = await Test.createTestingModule({
//       controllers: [MediaUserController],
//       providers: [
//         {
//           provide: MediaUserService,
//           useValue: mediaUserServiceMock,
//         },
//         {
//           provide: MediaRepository,
//           useValue: mediaRepositoryMock,
//         },
//       ],
//     })
//       .overrideGuard(JwtUserGuard)
//       .useValue({ canActivate: jest.fn(() => true) })
//       .compile();

//     controller = module.get<MediaUserController>(MediaUserController);
//     mediaUserService = module.get(MediaUserService) as jest.Mocked<MediaUserService>;
//     mediaRepository = module.get(MediaRepository) as jest.Mocked<MediaRepository>;
//   });

//   it('should be defined', () => {
//     expect(controller).toBeDefined();
//   });

//   describe('findMyMedia', () => {
//     it('should return paginated media list for user', async () => {
//       // Arrange
//       const query: MediaQueryDto = {
//         page: 1,
//         limit: 10,
//         sortBy: 'createdAt',
//         sortDirection: SortDirection.DESC,
//       };

//       const mockPaginatedResult: PaginatedResult<MediaResponseDto> = {
//         items: [mockMediaResponse],
//         meta: {
//           totalItems: 1,
//           itemCount: 1,
//           itemsPerPage: 10,
//           totalPages: 1,
//           currentPage: 1,
//         },
//       };

//       const apiResponse = ApiResponseDto.paginated(mockPaginatedResult);
//       mediaUserService.findAllByUser.mockResolvedValue(apiResponse);

//       // Act
//       const result = await controller.findMyMedia(mockUser, query);

//       // Assert
//       expect(mediaUserService.findAllByUser).toHaveBeenCalledWith(mockUser.sub, query);
//       expect(result).toBe(apiResponse);
//     });
//   });

//   describe('findOne', () => {
//     it('should return media by id', async () => {
//       // Arrange
//       const mediaId = 'media-id-1';
//       const apiResponse = ApiResponseDto.success(mockMediaResponse);

//       mediaUserService.findById.mockResolvedValue(apiResponse);

//       // Act
//       const result = await controller.findOne(mockUser, mediaId);

//       // Assert
//       expect(mediaUserService.findById).toHaveBeenCalledWith(mediaId, mockUser.sub);
//       expect(result).toBe(apiResponse);
//     });
//   });

//   describe('createPresignedUrlsFromMediaList', () => {
//     it('should create presigned URLs for media records', async () => {
//       // Arrange
//       const mediaDto: MediaDto[] = [
//         {
//           name: 'Test Media',
//           description: 'Test Description',
//           size: 1024,
//           tags: ['test', 'image'],
//           type: 'image/jpeg',
//           ownedBy: mockUser.sub,
//         },
//       ];

//       const createResult = [
//         {
//           id: 'media-id-1',
//           presignedUrl: 'https://example.com/upload',
//         },
//       ];

//       const apiResponse = ApiResponseDto.created(createResult);
//       mediaUserService.createPresignedUrlsFromMediaList.mockResolvedValue(apiResponse);

//       // Act
//       const result = await controller.createPresignedUrlsFromMediaList(mockUser, mediaDto);

//       // Assert
//       expect(mediaUserService.createPresignedUrlsFromMediaList).toHaveBeenCalledWith(mediaDto, mockUser.sub);
//       expect(result).toBe(apiResponse);
//     });

//     it('should handle exceptions from service', async () => {
//       // Arrange
//       const mediaDto: MediaDto[] = [
//         {
//           name: 'Test Media',
//           description: 'Test Description',
//           size: 1024 * 1024 * 1024, // 1GB - too large
//           tags: ['test', 'image'],
//           type: 'image/jpeg',
//           ownedBy: mockUser.sub,
//         },
//       ];

//       const error = new AppException(ErrorCode.FILE_SIZE_EXCEEDED);
//       mediaUserService.createPresignedUrlsFromMediaList.mockRejectedValue(error);

//       // Act & Assert
//       await expect(controller.createPresignedUrlsFromMediaList(mockUser, mediaDto)).rejects.toThrow(AppException);
//     });
//   });

//   describe('deleteManyMyMedia', () => {
//     it('should delete multiple media records', async () => {
//       // Arrange
//       const mediaIds = ['media-id-1', 'media-id-2'];
//       const deleteResult = {
//         deleted: ['media-id-1', 'media-id-2'],
//         skipped: [],
//         errors: [],
//       };
//       const apiResponse = ApiResponseDto.success(deleteResult);

//       mediaUserService.deleteManyByUser.mockResolvedValue(apiResponse);

//       // Act
//       const result = await controller.deleteManyMyMedia(mockUser, mediaIds);

//       // Assert
//       expect(mediaUserService.deleteManyByUser).toHaveBeenCalledWith(mockUser.sub, mediaIds);
//       expect(result).toBe(apiResponse);
//     });
//   });

//   describe('handleTransitionAndDelete', () => {
//     it('should transition media status and delete error records', async () => {
//       // Arrange
//       const mediaIds = ['media-id-1', 'media-id-2'];
//       const transitionResult = {
//         successIds: ['media-id-1', 'media-id-2'],
//         skippedIds: [],
//       };

//       mediaUserService.handleTransitionAndDelete.mockResolvedValue(transitionResult);

//       // Act
//       const result = await controller.handleTransitionAndDelete(mockUser, mediaIds);

//       // Assert
//       expect(mediaUserService.handleTransitionAndDelete).toHaveBeenCalledWith(mediaIds, mockUser.sub);
//       expect(result).toEqual(transitionResult);
//     });
//   });
  
//   describe('deleteAgentMediaByUser', () => {
//     it('should delete agent media records', async () => {
//       // Arrange
//       const mediaIds = ['media-id-1', 'media-id-2'];
//       const deleteResult = {
//         deletedIds: ['media-id-1', 'media-id-2'],
//         skippedIds: [],
//         failedIds: [],
//       };

//       mediaUserService.deleteAgentMediaByUser.mockResolvedValue(deleteResult);

//       // Act
//       const result = await controller.deleteAgentMediaByUser(mediaIds, mockUser);

//       // Assert
//       expect(mediaUserService.deleteAgentMediaByUser).toHaveBeenCalledWith(mediaIds, mockUser.sub);
//       expect(result).toEqual(deleteResult);
//     });
//   });
// });
