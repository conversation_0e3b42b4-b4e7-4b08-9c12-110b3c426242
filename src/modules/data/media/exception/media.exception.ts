import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Error codes for the Media module
 * Range: 10300-10399
 */
export const MEDIA_ERROR_CODES = {
  // General exceptions
  NOT_FOUND: new ErrorCode(
    10300,
    'Media not found',
    HttpStatus.NOT_FOUND,
  ),

  // Permisson-security exceptions
  FORBIDDEN: new ErrorCode(
    10301,
    'Not found',
    HttpStatus.NOT_FOUND,
  ),

  FILE_TYPE_NOT_FOUND: new ErrorCode(
    10302,
    'Media with this name already exists',
    HttpStatus.CONFLICT,
  ),

  FILE_SIZE_EXCEEDED: new ErrorCode(
    10303,
    'Invalid code definition format',
    HttpStatus.BAD_REQUEST,
  ),

  BAD_REQUEST: new ErrorCode(
    10310,
    'Failed to send request',
    HttpStatus.BAD_REQUEST,
  ),

  DATA_FETCH_ERROR: new ErrorCode(
    10311,
    'Failed to fetch data',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  GENERAL_ERROR: new ErrorCode(
    10312,
    'General media error',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
};
