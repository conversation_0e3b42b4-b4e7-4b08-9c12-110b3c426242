import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MediaAdminService } from '../../../../admin/services/media-admin.service';
import { Media } from '../../../../entities/media.entity';

describe('MediaAdminService', () => {
  let service: MediaAdminService;
  let mediaRepository: jest.Mocked<Repository<Media>>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MediaAdminService,
        {
          provide: getRepositoryToken(Media),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            save: jest.fn(),
            delete: jest.fn(),
            update: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<MediaAdminService>(MediaAdminService);
    mediaRepository = module.get(getRepositoryToken(Media)) as jest.Mocked<Repository<Media>>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  // Thêm các test case cho các phương thức của MediaAdminService khi cần
  // Hiện tại MediaAdminService chưa có phương thức nào được triển khai
});
