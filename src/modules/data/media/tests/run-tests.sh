#!/bin/bash

# Script để chạy tất cả các test cho module media

# Đặt màu sắc cho output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Bắt đầu chạy test cho module media...${NC}"

# Chạy unit test
echo -e "${YELLOW}Chạy unit test...${NC}"
npx jest --config=src/modules/data/media/tests/jest.config.js "src/modules/data/media/tests/unit" --coverage

# Lưu kết quả unit test
UNIT_TEST_RESULT=$?

# Hiển thị kết quả
if [ $UNIT_TEST_RESULT -eq 0 ]; then
  echo -e "${GREEN}Tất cả các unit test đã chạy thành công!${NC}"
else
  echo -e "${RED}Một số unit test đã thất bại!${NC}"
fi

# Chạy integration test
echo -e "${YELLOW}Chạy integration test...${NC}"
npx jest --config=src/modules/data/media/tests/jest.config.js "src/modules/data/media/tests/integration" --coverage

# Lưu kết quả integration test
INTEGRATION_TEST_RESULT=$?

# Hiển thị kết quả
if [ $INTEGRATION_TEST_RESULT -eq 0 ]; then
  echo -e "${GREEN}Tất cả các integration test đã chạy thành công!${NC}"
else
  echo -e "${RED}Một số integration test đã thất bại!${NC}"
fi

# Chạy e2e test
echo -e "${YELLOW}Chạy e2e test...${NC}"
npx jest --config=src/modules/data/media/tests/jest.config.js "src/modules/data/media/tests/e2e" --coverage

# Lưu kết quả e2e test
E2E_TEST_RESULT=$?

# Hiển thị kết quả
if [ $E2E_TEST_RESULT -eq 0 ]; then
  echo -e "${GREEN}Tất cả các e2e test đã chạy thành công!${NC}"
else
  echo -e "${RED}Một số e2e test đã thất bại!${NC}"
fi

echo -e "${GREEN}Hoàn thành chạy test!${NC}"

# Hiển thị đường dẫn đến báo cáo coverage
echo -e "${YELLOW}Báo cáo coverage được lưu tại: coverage/media/lcov-report/index.html${NC}"
