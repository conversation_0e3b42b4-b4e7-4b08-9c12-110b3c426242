import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { QueryFileDto } from '../../dto/query-file.dto';
import { SortDirection } from '@common/dto/query.dto';

describe('QueryFileDto', () => {
  it('nên xác thực DTO hợp lệ với các tham số mặc định', async () => {
    // Arrange
    const dto = plainToInstance(QueryFileDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với tất cả các tham số', async () => {
    // Arrange
    const dto = plainToInstance(QueryFileDto, {
      page: 1,
      limit: 10,
      search: 'test',
      extensions: 'pdf,docx',
      vectorStoreId: 'vs_123e4567-e89b-12d3-a456-426614174000',
      sortBy: 'name',
      sortDirection: SortDirection.ASC
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi page là số âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryFileDto, {
      page: -1
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const errorJson = JSON.stringify(errors);
    expect(errorJson).toContain('page');
  });

  it('nên thất bại khi limit là số âm', async () => {
    // Arrange
    const dto = plainToInstance(QueryFileDto, {
      limit: -1
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const errorJson = JSON.stringify(errors);
    expect(errorJson).toContain('limit');
  });

  it('nên thất bại khi sortDirection không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryFileDto, {
      sortDirection: 'INVALID'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const errorJson = JSON.stringify(errors);
    expect(errorJson).toContain('sortDirection');
    expect(errorJson).toContain('isEnum');
  });

  it('nên xác thực với sortBy hợp lệ', async () => {
    // Arrange
    const validSortByValues = ['name', 'createdAt', 'storage'];
    
    for (const sortBy of validSortByValues) {
      const dto = plainToInstance(QueryFileDto, {
        sortBy
      });

      // Act
      const errors = await validate(dto);

      // Assert
      expect(errors.length).toBe(0);
    }
  });

  it('nên xác thực với vectorStoreId hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryFileDto, {
      vectorStoreId: 'vs_123e4567-e89b-12d3-a456-426614174000'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực với extensions hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryFileDto, {
      extensions: 'pdf,docx,txt'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });
});
