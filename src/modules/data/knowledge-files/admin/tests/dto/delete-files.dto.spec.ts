import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { DeleteFilesDto } from '../../dto/delete-files.dto';

describe('DeleteFilesDto', () => {
  it('nên xác thực DTO hợp lệ với nhiều file ID', async () => {
    // Arrange
    const dto = plainToInstance(DeleteFilesDto, {
      fileIds: ['123e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174001']
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với một file ID', async () => {
    // Arrange
    const dto = plainToInstance(DeleteFilesDto, {
      fileIds: ['123e4567-e89b-12d3-a456-426614174000']
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi fileIds là mảng rỗng', async () => {
    // Arrange
    const dto = plainToInstance(DeleteFilesDto, {
      fileIds: []
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('arrayMinSize');
  });

  it('nên thất bại khi fileIds không được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(DeleteFilesDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isArray');
  });

  it('nên thất bại khi fileIds chứa chuỗi rỗng', async () => {
    // Arrange
    const dto = plainToInstance(DeleteFilesDto, {
      fileIds: ['123e4567-e89b-12d3-a456-426614174000', '']
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isNotEmpty');
  });

  it('nên thất bại khi fileIds chứa giá trị không phải chuỗi', async () => {
    // Arrange
    const dto = plainToInstance(DeleteFilesDto, {
      fileIds: ['123e4567-e89b-12d3-a456-426614174000', 123]
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isString');
  });
});
