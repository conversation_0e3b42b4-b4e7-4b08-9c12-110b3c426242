import { Injectable, Logger } from '@nestjs/common';
import { DataSource, In } from 'typeorm';
import { KnowledgeFile, VectorStore } from '../../entities';
import { KnowledgeFileRepository, VectorStoreFileRepository, VectorStoreRepository } from '../../repositories';
import {
  CreateVectorStoreDto,
  QueryVectorStoreDto,
  VectorStoreResponseDto,
  AssignFilesDto,
} from '../dto';
import { v4 as uuidv4 } from 'uuid';
import { plainToInstance } from 'class-transformer';
import { PaginatedResult } from '@common/response/api-response-dto';
import { OwnerType } from '@shared/enums';
import { AppException } from '@common/exceptions/app.exception';
import { KNOWLEDGE_FILE_ERROR_CODES } from '@modules/data/knowledge-files/exceptions';
import { OpenAiService } from '@shared/services/ai/openai.service';
import { S3Service } from '@shared/services/s3.service';
import { Transactional } from 'typeorm-transactional';
import { Val<PERSON><PERSON><PERSON><PERSON>per } from '../../helpers/validation.helper';
import { FileHelper } from '../../helpers/file.helper';
import { KnowledgeFileStatus } from '../../enums/knowledge-file-status.enum';

/**
 * Interface cho lỗi xử lý file
 */
interface FileError {
  id: string;
  error: string;
}

@Injectable()
export class VectorStoreAdminService {
  private readonly logger = new Logger(VectorStoreAdminService.name);

  constructor(
    private readonly vectorStoreRepository: VectorStoreRepository,
    private readonly vectorStoreFileRepository: VectorStoreFileRepository,
    private readonly knowledgeFileRepository: KnowledgeFileRepository,
    private readonly openAiService: OpenAiService,
    private readonly s3Service: S3Service,
    private readonly dataSource: DataSource,
    private readonly validationHelper: ValidationHelper
  ) {}

  /**
   * Tạo một vector store mới
   * @param dto Thông tin vector store cần tạo
   * @param employeeId ID của nhân viên
   * @returns Thông tin vector store đã tạo
   */
  @Transactional()
  async createVectorStore(dto: CreateVectorStoreDto, employeeId: number): Promise<VectorStoreResponseDto> {
    try {
      this.logger.log(`Bắt đầu tạo vector store cho admin ID ${employeeId} với tên: ${dto.name}`);

      // Tạo vector store trên OpenAI
      this.logger.log(`Đang tạo vector store trên OpenAI với tên: ${dto.name}`);
      const openAiVectorStore = await this.openAiService.createVectorStore({
        name: dto.name
      });

      // Kiểm tra kết quả từ OpenAI
      this.validationHelper.validateOpenAiVectorStoreId(openAiVectorStore);
      this.logger.log(`Đã tạo vector store trên OpenAI với ID: ${openAiVectorStore.vectorStoreId}`);

      // Tạo bản ghi vector store trong database
      const vectorStore = this.vectorStoreRepository.create({
        id: openAiVectorStore.vectorStoreId,
        name: dto.name,
        storage: 0,
        ownerType: OwnerType.ADMIN,
        ownerId: employeeId,
        createdAt: Date.now(),
        updateAt: Date.now()
      });

      // Lưu vào database
      this.logger.log(`Đang lưu vector store vào database với ID: ${vectorStore.id}`);
      await this.vectorStoreRepository.save(vectorStore);
      this.logger.log(`Đã lưu vector store vào database thành công`);

      // Trả về thông tin vector store
      return plainToInstance(
        VectorStoreResponseDto,
        {
          storeId: vectorStore.id,
          storeName: vectorStore.name,
          size: vectorStore.storage,
          agents: 0,
          files: 0,
          createdAt: vectorStore.createdAt,
          updatedAt: vectorStore.updateAt
        },
        { excludeExtraneousValues: true }
      );
    } catch (error) {
      this.logger.error(`Lỗi khi tạo vector store: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_CREATE_ERROR,
        `Lỗi khi tạo vector store: ${error.message}`
      );
    }
  }

  /**
   * Lấy danh sách vector store từ hệ thống
   * @param queryDto Tham số truy vấn
   * @param employeeId ID của nhân viên
   * @returns Danh sách vector store với phân trang
   */
  async getVectorStores(queryDto: QueryVectorStoreDto, employeeId: number): Promise<PaginatedResult<VectorStoreResponseDto>> {
    try {
      this.logger.log(`Lấy danh sách vector store với tham số: ${JSON.stringify(queryDto)}`);

      // Sử dụng repository để lấy danh sách vector store
      const result = await this.vectorStoreRepository.findAllWithPagination(queryDto);
      const vectorStores = result.items;

      // Lấy số lượng file trong mỗi vector store
      const storeIds = vectorStores.map(store => store.id);
      const fileCountMap = new Map<string, number>();
      const agentCountMap = new Map<string, number>();

      // Chỉ truy vấn khi có vector stores
      if (storeIds.length > 0) {
        this.logger.log(`Đang lấy thông tin chi tiết cho ${storeIds.length} vector store`);

        // Lấy số lượng file trong mỗi vector store
        const fileCounts = await this.vectorStoreFileRepository.countFilesByVectorStoreIds(storeIds);
        fileCounts.forEach(item => {
          fileCountMap.set(item.storeId, item.fileCount);
        });
        this.logger.log(`Đã lấy số lượng file cho ${fileCounts.length} vector store`);

        // Lấy số lượng agent sử dụng mỗi vector store
        const agentCountsQuery = this.dataSource.createQueryBuilder()
          .select('a.vector_store_id', 'storeId')
          .addSelect('COUNT(a.id)', 'agentCount')
          .from('agents', 'a')
          .where('a.vector_store_id IN (:...storeIds)', { storeIds })
          .groupBy('a.vector_store_id');

        const agentCounts = await agentCountsQuery.getRawMany();
        agentCounts.forEach(item => {
          agentCountMap.set(item.storeId, parseInt(item.agentCount));
        });
        this.logger.log(`Đã lấy số lượng agent cho ${agentCounts.length} vector store`);
      }

      // Chuyển đổi sang DTO
      const vectorStoreResponses = vectorStores.map(store => {
        return plainToInstance(
          VectorStoreResponseDto,
          {
            storeId: store.id,
            storeName: store.name,
            size: store.storage,
            agents: agentCountMap.get(store.id) || 0,
            files: fileCountMap.get(store.id) || 0,
            createdAt: store.createdAt,
            updatedAt: store.updateAt
          },
          { excludeExtraneousValues: true }
        );
      });

      // Trả về kết quả phân trang
      this.logger.log(`Đã hoàn thành việc lấy danh sách vector store`);
      return {
        items: vectorStoreResponses,
        meta: result.meta
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách vector store: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_LIST_ERROR,
        `Lỗi khi lấy danh sách vector store: ${error.message}`
      );
    }
  }

  /**
   * Gán các file vào vector store
   * @param vectorStoreId ID của vector store
   * @param dto Thông tin các file cần gán
   * @param employeeId ID của nhân viên
   * @returns Thông tin về việc gán file thành công, bao gồm số lượng file đã xử lý, danh sách file đã xử lý với OpenAI fileId và danh sách file bị bỏ qua
   */
  @Transactional()
  async assignFilesToVectorStore(vectorStoreId: string, dto: AssignFilesDto, employeeId: number): Promise<{
    success: boolean;
    message: string;
    processedFiles?: number;
    processedFileDetails?: { id: string; openAiFileId: string }[];
    skippedFiles?: string[]
  }> {
    try {
      this.logger.log(`Bắt đầu gán file vào vector store ${vectorStoreId} cho admin ${employeeId}`);
      this.logger.log(`File IDs cần gán: ${dto.fileIds.join(', ')}`);

      // Kiểm tra tham số đầu vào
      this.validationHelper.validateVectorStoreId(vectorStoreId);
      this.validationHelper.validateFileIds(dto.fileIds);

      // Kiểm tra vector store có tồn tại và thuộc về admin này không
      this.logger.log(`Kiểm tra quyền sở hữu vector store ${vectorStoreId}`);
      const vectorStore = await this.vectorStoreRepository.findOneByIdAndEmployeeId(vectorStoreId, employeeId);
      this.validationHelper.validateVectorStoreExists(vectorStore, employeeId, OwnerType.ADMIN);
      // Sau khi validate, chúng ta chắc chắn vectorStore không phải null
      this.logger.log(`Đã tìm thấy vector store: ${vectorStore!.name}`);

      // Kiểm tra các file có tồn tại và thuộc quyền sở hữu của admin này
      this.logger.log(`Kiểm tra các file: ${dto.fileIds.join(', ')}`);
      const files = await this.knowledgeFileRepository.findByIdsAndEmployeeId(dto.fileIds, employeeId);
      this.validationHelper.validateFilesExist(files, dto.fileIds, employeeId, OwnerType.ADMIN);
      this.logger.log(`Đã tìm thấy ${files.length}/${dto.fileIds.length} file hợp lệ thuộc quyền sở hữu của admin`);

      // Kiểm tra xem file đã được gán vào vector store này trước đó chưa
      this.logger.log(`Kiểm tra các file đã được gán vào vector store ${vectorStoreId} chưa`);
      const existingVectorStoreFiles = await this.vectorStoreFileRepository.findByVectorStoreIdAndFileIds(vectorStoreId, dto.fileIds);
      this.logger.log(`Tìm thấy ${existingVectorStoreFiles.length} bản ghi liên kết file đã tồn tại`);

      // Lọc ra các file chưa được gán vào vector store hiện tại
      // hoặc file đã được gán nhưng chưa có fileId trong OpenAI
      const filesNeedUpload = files.filter(file => {
        // Nếu file chưa có fileId trong OpenAI, cần upload lại
        if (!file.fileId) {
          return true;
        }

        // Hoặc nếu file chưa được gán vào vector store hiện tại
        const existingAssignment = existingVectorStoreFiles.find(vsf => vsf.fileId === file.id);
        return !existingAssignment;
      });

      // Lọc ra các file cần gán vào vector store
      const existingFileIds = existingVectorStoreFiles.map(vsf => vsf.fileId);
      const filesToAssign = dto.fileIds.filter(fileId => !existingFileIds.includes(fileId));

      this.logger.log(`Có ${filesToAssign.length} file cần gán vào vector store ${vectorStoreId}`);
      this.logger.log(`Có ${filesNeedUpload.length} file cần upload lên OpenAI`);

      // Nếu không có file cần gán và không có file cần upload, thì trả về thành công luôn
      if (filesToAssign.length === 0 && filesNeedUpload.length === 0) {
        this.logger.log(`Tất cả các file đã được gán vào vector store ${vectorStoreId} và có OpenAI file ID`);
        return {
          success: true,
          message: 'Tất cả các file đã được gán vào vector store này và có OpenAI file ID.'
        };
      }

      // Tạo các bản ghi liên kết mới cho các file chưa được gán
      if (filesToAssign.length > 0) {
        this.logger.log(`Tạo ${filesToAssign.length} bản ghi liên kết mới`);
        const vectorStoreFiles = filesToAssign.map(fileId => ({
          id: uuidv4(), // Thêm ID duy nhất cho mỗi bản ghi
          vectorStoreId,
          fileId,
          createdAt: Date.now(),
          updatedAt: Date.now()
        }));

        // Lưu vào database
        await this.vectorStoreFileRepository.save(vectorStoreFiles);
        this.logger.log(`Đã lưu các bản ghi liên kết mới vào database thành công`);
      }

      // Khởi tạo mảng kết quả
      const processedFiles: { id: string; openAiFileId: string }[] = [];
      const failedFiles: FileError[] = [];

      // Upload các file cần xử lý lên OpenAI
      if (filesNeedUpload.length > 0) {
        this.logger.log(`Chuẩn bị xử lý ${filesNeedUpload.length} file trên OpenAI vector store`);

        // Gán từng file vào vector store trên OpenAI
        for (const file of filesNeedUpload) {
          try {
            this.logger.log(`======= Xử lý file ${file.id} (${file.name}) =======`);

            // Kiểm tra phần mở rộng của file
            const fileExtension = FileHelper.getFileExtension(file.name);

            // Khai báo biến uploadResult ở phạm vi ngoài
            let uploadResult;

            if (!FileHelper.hasValidExtension(file.name)) {
              this.logger.warn(`File ${file.id} (${file.name}) không có phần mở rộng hợp lệ`);

              // Thêm phần mở rộng .txt mặc định nếu không có
              const newStorageKey = FileHelper.createExtendedKey(file.storageKey, 'txt');

              // Tạo bản sao của file với phần mở rộng mới
              await this.s3Service.copyFile(file.storageKey, newStorageKey);

              this.logger.log(`Đã tạo bản sao của file với phần mở rộng .txt: ${newStorageKey}`);

              // Upload file lên OpenAI để lấy fileId
              this.logger.log(`Chuẩn bị tải file lên OpenAI từ S3 key: ${newStorageKey}`);

              // Upload file lên OpenAI và gán vào vector store
              this.logger.log(`Bắt đầu upload file ${file.name} (${newStorageKey}) lên OpenAI...`);
              uploadResult = await this.openAiService.uploadFileToVectorStore(
                vectorStoreId,
                newStorageKey
              );
            } else {
              // Upload file lên OpenAI để lấy fileId
              this.logger.log(`Chuẩn bị tải file lên OpenAI từ S3 key: ${file.storageKey}`);

              // Upload file lên OpenAI và gán vào vector store
              this.logger.log(`Bắt đầu upload file ${file.name} (${file.storageKey}) lên OpenAI...`);
              uploadResult = await this.openAiService.uploadFileToVectorStore(
                vectorStoreId,
                file.storageKey
              );
            }

            // Kiểm tra kết quả upload
            if (!uploadResult || !uploadResult.fileId) {
              throw new Error('Không nhận được fileId từ OpenAI sau khi upload');
            }

            // Xử lý fileId từ kết quả upload
            const openAiFileId = typeof uploadResult.fileId === 'string'
              ? uploadResult.fileId
              : Array.isArray(uploadResult.fileId) ? uploadResult.fileId[0] : String(uploadResult.fileId);

            this.logger.log(`Tải file lên OpenAI thành công, nhận được file ID: ${openAiFileId}`);

            // Cập nhật fileId và trạng thái vào database với transaction
            await this.dataSource.transaction(async (transactionalEntityManager) => {
              const fileToUpdate = await transactionalEntityManager.findOne(KnowledgeFile, {
                where: { id: file.id }
              });

              if (fileToUpdate) {
                fileToUpdate.fileId = openAiFileId;
                // Cập nhật trạng thái từ DRAFT sang APPROVED sau khi đã tải lên OpenAI thành công
                if (fileToUpdate.status === KnowledgeFileStatus.DRAFT) {
                  fileToUpdate.status = KnowledgeFileStatus.APPROVED;
                  this.logger.log(`Đã cập nhật trạng thái file ${file.id} từ DRAFT sang APPROVED`);
                }
                await transactionalEntityManager.save(KnowledgeFile, fileToUpdate);
                this.logger.log(`Đã cập nhật fileId: ${openAiFileId} vào database cho file ${file.id}`);
              } else {
                throw new Error(`Không tìm thấy file ${file.id} trong database khi cập nhật fileId`);
              }
            });

            processedFiles.push({
              id: file.id,
              openAiFileId: openAiFileId
            });
          } catch (error) {
            this.logger.error(`Lỗi khi xử lý file ${file.id}:`, error);
            this.logger.error(`Chi tiết lỗi: ${error.message}`);
            this.logger.error(`Stack trace: ${error.stack}`);
            failedFiles.push({ id: file.id, error: error.message });
            // Tiếp tục với file tiếp theo ngay cả khi có lỗi
          }
          this.logger.log(`======= Hoàn tất xử lý file ${file.id} =======`);
        }
      }

      // Tạo thông báo kết quả
      let message = '';
      if (processedFiles.length > 0) {
        message += `Đã xử lý ${processedFiles.length} file với OpenAI thành công.`;
      }
      if (filesToAssign.length > 0) {
        message += ` Đã gán ${filesToAssign.length} file vào vector store.`;
      }
      if (message === '') {
        message = 'Tất cả file đã được xử lý trước đó.';
      }
      this.logger.log(`Kết quả: ${message}`);

      // Cập nhật dung lượng sử dụng của vector store cho tất cả các file được gán
      try {
        // Tính tổng dung lượng từ tất cả các file được gán (không chỉ các file đã xử lý)
        const filesToUpdateStorage = files.filter(file => filesToAssign.includes(file.id));
        const totalStorage = filesToUpdateStorage.reduce((sum, file) => sum + Number(file.storage || 0), 0);


        // Cập nhật dung lượng vào vector store
        if (totalStorage > 0) {
          this.logger.log(`Cập nhật dung lượng vector store: +${totalStorage} bytes`);
          // Đảm bảo vectorStore không phải null trước khi sử dụng
          if (vectorStore) {
            const currentStorage = parseInt(`${vectorStore.storage | 0}`) + parseInt(`${totalStorage | 0}`);
            vectorStore.storage = currentStorage;
            vectorStore.updateAt = Date.now(); // Cập nhật timestamp
            await this.vectorStoreRepository.save(vectorStore);
          }
          this.logger.log(`Đã cập nhật dung lượng vector store thành công`);
        }
      } catch (storageError) {
        this.logger.warn(`Không thể cập nhật dung lượng vector store: ${storageError.message}`);
        // Không ném lỗi vì đây không phải là lỗi nghiêm trọng
      }

      // Thêm các file đã có sẵn fileId vào danh sách processedFiles
      const existingFilesWithId = files.filter(file => {
        // Chỉ lấy các file đã có fileId và đã được gán vào vector store
        return file.fileId && filesToAssign.includes(file.id) &&
               !processedFiles.some(pf => pf.id === file.id);
      });

      // Thêm vào danh sách processedFiles
      for (const file of existingFilesWithId) {
        processedFiles.push({
          id: file.id,
          openAiFileId: file.fileId
        });
      }

      const result = {
        success: processedFiles.length > 0 || filesToAssign.length > 0,
        message,
        processedFiles: processedFiles.length,
        processedFileDetails: processedFiles,
        skippedFiles: failedFiles.map(f => f.id)
      };

      this.logger.log(`Kết thúc quá trình xử lý file với vector store: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi gán file vào vector store: ${error.message}`, error.stack);
      // Không cần kiểm tra NotFoundException vì đã sử dụng ValidationHelper
      if (error instanceof AppException) {
        // Re-throw existing AppException
        throw error;
      }
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_ASSIGN_FILES_ERROR,
        `Lỗi khi gán file vào vector store: ${error.message}`
      );
    }
  }



  /**
   * Xóa các file khỏi vector store
   * @param vectorStoreId ID của vector store
   * @param fileIds Danh sách ID của các file cần xóa
   * @param employeeId ID của nhân viên
   * @returns Thông tin về việc xóa file thành công
   */
  @Transactional()
  async removeFilesFromVectorStore(vectorStoreId: string, fileIds: string[], employeeId: number): Promise<{ success: boolean; message: string }> {
    try {
      this.logger.log(`Bắt đầu xóa file khỏi vector store ${vectorStoreId} cho admin ${employeeId}`);
      this.logger.log(`File IDs cần xóa: ${fileIds.join(', ')}`);

      // Kiểm tra tham số đầu vào
      this.validationHelper.validateVectorStoreId(vectorStoreId);
      this.validationHelper.validateFileIds(fileIds);

      // Kiểm tra vector store có tồn tại và thuộc về admin này không
      this.logger.log(`Kiểm tra quyền sở hữu vector store ${vectorStoreId}`);
      const vectorStore = await this.vectorStoreRepository.findOneByIdAndEmployeeId(vectorStoreId, employeeId);
      this.validationHelper.validateVectorStoreExists(vectorStore, employeeId, OwnerType.ADMIN);
      // Sau khi validate, chúng ta chắc chắn vectorStore không phải null
      this.logger.log(`Đã tìm thấy vector store: ${vectorStore!.name}`);

      // Kiểm tra các file có tồn tại trong vector store không
      this.logger.log(`Kiểm tra các file tồn tại trong vector store`);
      const vectorStoreFiles = await this.vectorStoreFileRepository.findByVectorStoreIdAndFileIds(vectorStoreId, fileIds);
      this.logger.log(`Tìm thấy ${vectorStoreFiles.length}/${fileIds.length} file cần xóa`);

      if (vectorStoreFiles.length === 0) {
        this.logger.log(`Không có file nào cần xóa khỏi vector store ${vectorStoreId}`);
        return {
          success: true,
          message: 'Không có file nào cần xóa khỏi vector store.'
        };
      }

      // Lấy thông tin các file để xóa khỏi OpenAI, chỉ lấy file thuộc quyền sở hữu của admin này
      const filesToRemove = await this.knowledgeFileRepository.findByIdsAndEmployeeId(
        vectorStoreFiles.map(vsf => vsf.fileId),
        employeeId
      );
      this.logger.log(`Đã tìm thấy ${filesToRemove.length} file thuộc quyền sở hữu của admin để xóa khỏi OpenAI`);

      // Nếu không có file nào thuộc quyền sở hữu của admin, trả về lỗi
      if (filesToRemove.length === 0) {
        this.logger.warn(`Không có file nào thuộc quyền sở hữu của admin ${employeeId}`);
        return {
          success: false,
          message: 'Không có file nào thuộc quyền sở hữu của bạn để xóa khỏi vector store.'
        };
      }

      // Tính tổng dung lượng của các file sẽ bị xóa
      const totalStorage = filesToRemove.reduce((sum, file) => sum + file.storage, 0);
      this.logger.log(`Tổng dung lượng các file sẽ bị xóa: ${totalStorage} bytes`);

      // Xóa các bản ghi liên kết chỉ cho các file thuộc quyền sở hữu của admin
      const fileIdsToRemove = filesToRemove.map(file => file.id);
      const vectorStoreFilesToRemove = vectorStoreFiles.filter(vsf => fileIdsToRemove.includes(vsf.fileId));

      this.logger.log(`Xóa ${vectorStoreFilesToRemove.length} bản ghi liên kết khỏi database`);
      await this.vectorStoreFileRepository.remove(vectorStoreFilesToRemove);
      this.logger.log(`Đã xóa các bản ghi liên kết thành công`);

      // Xóa các file khỏi vector store trên OpenAI
      const successfullyRemovedFromOpenAI: string[] = [];
      const failedToRemoveFromOpenAI: FileError[] = [];

      for (const file of filesToRemove) {
        if (file.fileId) {
          try {
            this.logger.log(`Đang xóa file ${file.id} (OpenAI file ID: ${file.fileId}) khỏi vector store ${vectorStoreId}`);
            await this.openAiService.deleteVectorStoreFile(vectorStoreId, file.fileId);
            this.logger.log(`Đã xóa file ${file.id} khỏi vector store thành công`);
            successfullyRemovedFromOpenAI.push(file.id);
          } catch (error) {
            this.logger.warn(`Lỗi khi xóa file ${file.id} khỏi vector store: ${error.message}`);
            failedToRemoveFromOpenAI.push({ id: file.id, error: error.message });
            // Tiếp tục với file tiếp theo ngay cả khi có lỗi
          }
        } else {
          this.logger.warn(`File ${file.id} không có OpenAI file ID, bỏ qua xóa từ OpenAI`);
        }
      }

      // Cập nhật dung lượng vector store cho tất cả các file đã xóa
      try {
        // Tính tổng dung lượng từ tất cả các file đã xóa
        if (totalStorage > 0) {
          this.logger.log(`Cập nhật giảm dung lượng vector store: -${totalStorage} bytes`);
          // Đảm bảo vectorStore không phải null trước khi sử dụng
          if (vectorStore) {
            vectorStore.storage = Math.max(0, vectorStore.storage - totalStorage); // Đảm bảo không âm
            vectorStore.updateAt = Date.now(); // Cập nhật timestamp
            await this.vectorStoreRepository.save(vectorStore);
          }
          this.logger.log(`Đã cập nhật dung lượng vector store thành công`);
        }
      } catch (storageError) {
        this.logger.warn(`Không thể cập nhật dung lượng vector store: ${storageError.message}`);
        // Không ném lỗi vì đây không phải là lỗi nghiêm trọng
      }

      // Tạo thông báo kết quả
      let resultMessage = `Đã xóa ${vectorStoreFiles.length} file khỏi vector store thành công.`;
      if (failedToRemoveFromOpenAI.length > 0) {
        resultMessage += ` Có ${failedToRemoveFromOpenAI.length} file không thể xóa khỏi OpenAI nhưng đã xóa liên kết trong database.`;
      }

      this.logger.log(`Kết thúc quá trình xóa file khỏi vector store với kết quả: ${resultMessage}`);
      return {
        success: true,
        message: resultMessage
      };
    } catch (error) {
      this.logger.error(`Lỗi khi xóa file khỏi vector store: ${error.message}`, error.stack);
      // Không cần kiểm tra NotFoundException vì đã sử dụng ValidationHelper
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_REMOVE_FILES_ERROR,
        `Lỗi khi xóa file khỏi vector store: ${error.message}`
      );
    }
  }

  /**
   * Lấy thông tin chi tiết của một vector store
   * @param vectorStoreId ID của vector store
   * @param employeeId ID của nhân viên (không sử dụng trong phương thức này nhưng giữ lại để tương thích với interface)
   * @returns Thông tin chi tiết của vector store
   */
  async getVectorStoreDetail(vectorStoreId: string, employeeId: number): Promise<VectorStoreResponseDto> {
    try {
      this.logger.log(`Lấy thông tin chi tiết vector store ${vectorStoreId}`);

      // Kiểm tra tham số đầu vào
      this.validationHelper.validateVectorStoreId(vectorStoreId);

      // Kiểm tra vector store có tồn tại không (admin có thể xem tất cả vector store)
      this.logger.log(`Kiểm tra vector store ${vectorStoreId}`);
      const vectorStore = await this.vectorStoreRepository.findOne({
        where: { id: vectorStoreId }
      });

      if (!vectorStore) {
        this.logger.warn(`Vector store ${vectorStoreId} không tồn tại`);
        throw new AppException(
          KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
          `Vector store với ID ${vectorStoreId} không tồn tại`
        );
      }
      this.logger.log(`Đã tìm thấy vector store: ${vectorStore.name}`);

      // Lấy danh sách các file trong vector store
      this.logger.log(`Lấy danh sách file trong vector store ${vectorStoreId}`);
      const vectorStoreFiles = await this.vectorStoreFileRepository.findByVectorStoreId(vectorStoreId);

      // Lấy thông tin chi tiết của các file
      const fileIds = vectorStoreFiles.map(vsf => vsf.fileId);
      let fileList: any[] = [];
      let fileCount = 0;

      if (fileIds.length > 0) {
        const files = await this.knowledgeFileRepository.find({
          where: { id: In(fileIds) }
        });
        fileCount = files.length;

        // Chuyển đổi thành DTO
        fileList = files.map(file => {
          // Lấy extension từ tên file
          const extension = FileHelper.getFileExtension(file.name);

          return {
            id: file.id,
            name: file.name,
            extension: extension,
            storage: file.storage,
            createdAt: file.createdAt,
            status: file.status
          };
        });
      }

      this.logger.log(`Số lượng file trong vector store: ${fileCount}`);

      // Đếm số lượng agent sử dụng vector store
      this.logger.log(`Đếm số lượng agent sử dụng vector store ${vectorStoreId}`);
      const agentCount = await this.dataSource.createQueryBuilder()
        .select('COUNT(id)', 'count')
        .from('agents', 'a')
        .where('a.vector_store_id = :vectorStoreId', { vectorStoreId })
        .getRawOne()
        .then(result => parseInt(result?.count || '0'));
      this.logger.log(`Số lượng agent sử dụng vector store: ${agentCount}`);

      // Trả về thông tin vector store
      this.logger.log(`Hoàn thành lấy thông tin chi tiết vector store ${vectorStoreId}`);
      return plainToInstance(
        VectorStoreResponseDto,
        {
          storeId: vectorStore.id,
          storeName: vectorStore.name,
          size: vectorStore.storage,
          agents: agentCount,
          files: fileCount,
          createdAt: vectorStore.createdAt,
          updatedAt: vectorStore.updateAt,
          fileList: fileList // Thêm danh sách file vào response
        },
        { excludeExtraneousValues: true }
      );
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin chi tiết vector store: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DETAIL_ERROR,
        `Lỗi khi lấy thông tin chi tiết vector store: ${error.message}`
      );
    }
  }

  /**
   * Xóa một hoặc nhiều vector store
   * @param vectorStoreIdOrIds ID hoặc mảng ID của các vector store cần xóa
   * @param employeeId ID của nhân viên
   * @returns Thông tin về việc xóa vector store thành công
   */
  @Transactional()
  async deleteVectorStore(vectorStoreIdOrIds: string | string[], employeeId: number): Promise<{
    success: boolean;
    deletedCount: number;
    failedItems?: { id: string; reason: string }[];
  }> {
    try {
      // Chuyển đổi tham số thành mảng để xử lý thống nhất
      const storeIds = Array.isArray(vectorStoreIdOrIds) ? vectorStoreIdOrIds : [vectorStoreIdOrIds];

      if (storeIds.length === 0) {
        return { success: true, deletedCount: 0 };
      }

      this.logger.log(`Bắt đầu xóa ${storeIds.length} vector store cho admin ${employeeId}`);

      // Kết quả xử lý
      const results = {
        success: true,
        deletedCount: 0,
        failedItems: [] as { id: string; reason: string }[]
      };

      // Xử lý từng vector store
      for (const storeId of storeIds) {
        try {
          this.logger.log(`Bắt đầu xóa vector store ${storeId} cho admin ${employeeId}`);

          // Kiểm tra tham số đầu vào
          this.validationHelper.validateVectorStoreId(storeId);

          // Kiểm tra vector store có tồn tại và thuộc về admin này không
          this.logger.log(`Kiểm tra quyền sở hữu vector store ${storeId}`);
          const vectorStore = await this.vectorStoreRepository.findOneByIdAndEmployeeId(storeId, employeeId);

          if (!vectorStore) {
            this.logger.warn(`Vector store ${storeId} không tồn tại hoặc không thuộc về admin ${employeeId}`);
            results.failedItems.push({
              id: storeId,
              reason: 'Vector store không tồn tại hoặc bạn không có quyền truy cập'
            });
            continue;
          }

          // Xóa vector store trên OpenAI
          this.logger.log(`Xóa vector store trên OpenAI: ${storeId}`);
          try {
            await this.openAiService.deleteVectorStore(storeId);
            this.logger.log(`Đã xóa vector store trên OpenAI thành công`);
          } catch (openaiError) {
            // Nếu không xóa được trên OpenAI, vẫn tiếp tục xóa trong database
            this.logger.warn(`Không thể xóa vector store trên OpenAI: ${openaiError.message}`);
          }

          // Xóa các bản ghi liên kết với file
          this.logger.log(`Xóa các liên kết file với vector store ${storeId}`);
          const deleteFilesResult = await this.vectorStoreFileRepository.delete({ vectorStoreId: storeId });
          this.logger.log(`Đã xóa ${deleteFilesResult.affected || 0} liên kết file với vector store`);

          // Xóa vector store
          this.logger.log(`Xóa vector store ${storeId} khỏi database`);
          await this.vectorStoreRepository.delete({ id: storeId });
          this.logger.log(`Đã xóa vector store khỏi database thành công`);

          this.logger.log(`Hoàn thành việc xóa vector store ${storeId}`);

          // Tăng số lượng vector store đã xóa thành công
          results.deletedCount++;
        } catch (error) {
          this.logger.error(`Lỗi khi xóa vector store ${storeId}: ${error.message}`, error.stack);
          results.failedItems.push({
            id: storeId,
            reason: `Lỗi khi xóa: ${error.message}`
          });
        }
      }

      // Nếu không xóa được vector store nào, đánh dấu là không thành công
      if (results.deletedCount === 0 && storeIds.length > 0) {
        results.success = false;
      }

      // Nếu không có lỗi nào, không trả về mảng failedItems
      if (results.failedItems.length === 0) {
        const { failedItems, ...rest } = results;
        return rest;
      }

      return results;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa vector store: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_DELETE_ERROR,
        `Lỗi khi xóa vector store: ${error.message}`
      );
    }
  }


}