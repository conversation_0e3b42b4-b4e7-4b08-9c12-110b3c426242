import { plainToInstance } from 'class-transformer';
import { AssignFilesResponseDto, ProcessedFileDetailDto } from '../../dto';

describe('AssignFilesResponseDto', () => {
  it('nên chuyển đổi đúng từ plain object sang DTO', () => {
    // Arrange
    const plainObject = {
      success: true,
      message: 'Đã xử lý 2 file với OpenAI thành công. Đã gán 2 file vào vector store.',
      processedFiles: 2,
      processedFileDetails: [
        {
          id: 'bfd70b5e-e3bd-403b-95ec-e8b346ffa753',
          openAiFileId: 'file-EV7pF7UTU1exXGhZbSzLqm',
        },
        {
          id: 'cfd70b5e-e3bd-403b-95ec-e8b346ffa754',
          openAiFileId: 'file-FV7pF7UTU1exXGhZbSzLqn',
        },
      ],
      skippedFiles: [],
    };

    // Act
    const dto = plainToInstance(AssignFilesResponseDto, plainObject, { 
      excludeExtraneousValues: true,
      enableImplicitConversion: true
    });

    // Chuyển đổi thủ công các đối tượng con
    if (dto.processedFileDetails && dto.processedFileDetails.length > 0) {
      dto.processedFileDetails = dto.processedFileDetails.map(detail => 
        plainToInstance(ProcessedFileDetailDto, detail, { excludeExtraneousValues: true })
      );
    }

    // Assert
    expect(dto).toBeDefined();
    expect(dto.success).toBe(true);
    expect(dto.message).toBe('Đã xử lý 2 file với OpenAI thành công. Đã gán 2 file vào vector store.');
    expect(dto.processedFiles).toBe(2);
    expect(dto.processedFileDetails).toHaveLength(2);
    expect(dto.processedFileDetails[0]).toBeInstanceOf(ProcessedFileDetailDto);
    expect(dto.processedFileDetails[0].id).toBe('bfd70b5e-e3bd-403b-95ec-e8b346ffa753');
    expect(dto.processedFileDetails[0].openAiFileId).toBe('file-EV7pF7UTU1exXGhZbSzLqm');
    expect(dto.processedFileDetails[1].id).toBe('cfd70b5e-e3bd-403b-95ec-e8b346ffa754');
    expect(dto.processedFileDetails[1].openAiFileId).toBe('file-FV7pF7UTU1exXGhZbSzLqn');
    expect(dto.skippedFiles).toEqual([]);
  });

  it('nên chuyển đổi đúng từ plain object sang DTO với skippedFiles', () => {
    // Arrange
    const plainObject = {
      success: true,
      message: 'Đã xử lý 1 file với OpenAI thành công. Đã gán 1 file vào vector store. Đã bỏ qua 1 file.',
      processedFiles: 1,
      processedFileDetails: [
        {
          id: 'bfd70b5e-e3bd-403b-95ec-e8b346ffa753',
          openAiFileId: 'file-EV7pF7UTU1exXGhZbSzLqm',
        },
      ],
      skippedFiles: ['dfd70b5e-e3bd-403b-95ec-e8b346ffa755'],
    };

    // Act
    const dto = plainToInstance(AssignFilesResponseDto, plainObject, { 
      excludeExtraneousValues: true,
      enableImplicitConversion: true
    });

    // Chuyển đổi thủ công các đối tượng con
    if (dto.processedFileDetails && dto.processedFileDetails.length > 0) {
      dto.processedFileDetails = dto.processedFileDetails.map(detail => 
        plainToInstance(ProcessedFileDetailDto, detail, { excludeExtraneousValues: true })
      );
    }

    // Assert
    expect(dto).toBeDefined();
    expect(dto.success).toBe(true);
    expect(dto.message).toBe('Đã xử lý 1 file với OpenAI thành công. Đã gán 1 file vào vector store. Đã bỏ qua 1 file.');
    expect(dto.processedFiles).toBe(1);
    expect(dto.processedFileDetails).toHaveLength(1);
    expect(dto.processedFileDetails[0]).toBeInstanceOf(ProcessedFileDetailDto);
    expect(dto.processedFileDetails[0].id).toBe('bfd70b5e-e3bd-403b-95ec-e8b346ffa753');
    expect(dto.processedFileDetails[0].openAiFileId).toBe('file-EV7pF7UTU1exXGhZbSzLqm');
    expect(dto.skippedFiles).toEqual(['dfd70b5e-e3bd-403b-95ec-e8b346ffa755']);
  });

  it('nên chuyển đổi đúng từ plain object sang DTO với processedFileDetails rỗng', () => {
    // Arrange
    const plainObject = {
      success: false,
      message: 'Không thể xử lý file với OpenAI.',
      processedFiles: 0,
      processedFileDetails: [],
      skippedFiles: ['dfd70b5e-e3bd-403b-95ec-e8b346ffa755'],
    };

    // Act
    const dto = plainToInstance(AssignFilesResponseDto, plainObject, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeDefined();
    expect(dto.success).toBe(false);
    expect(dto.message).toBe('Không thể xử lý file với OpenAI.');
    expect(dto.processedFiles).toBe(0);
    expect(dto.processedFileDetails).toHaveLength(0);
    expect(dto.skippedFiles).toEqual(['dfd70b5e-e3bd-403b-95ec-e8b346ffa755']);
  });
});

describe('ProcessedFileDetailDto', () => {
  it('nên chuyển đổi đúng từ plain object sang DTO', () => {
    // Arrange
    const plainObject = {
      id: 'bfd70b5e-e3bd-403b-95ec-e8b346ffa753',
      openAiFileId: 'file-EV7pF7UTU1exXGhZbSzLqm',
    };

    // Act
    const dto = plainToInstance(ProcessedFileDetailDto, plainObject, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeDefined();
    expect(dto.id).toBe('bfd70b5e-e3bd-403b-95ec-e8b346ffa753');
    expect(dto.openAiFileId).toBe('file-EV7pF7UTU1exXGhZbSzLqm');
  });
});
