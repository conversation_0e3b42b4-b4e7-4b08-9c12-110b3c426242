import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';

// Mock cho VectorStoreUserController
const mockVectorStoreUserController = {
  createVectorStore: jest.fn(),
  getVectorStores: jest.fn(),
  getVectorStoreDetail: jest.fn(),
  assignFilesToVectorStore: jest.fn(),
  removeFileFromVectorStore: jest.fn(),
  removeFilesFromVectorStore: jest.fn(),
  deleteVectorStores: jest.fn()
};

// Mock cho VectorStoreUserService
const mockVectorStoreUserService = {
  createVectorStore: jest.fn(),
  getVectorStores: jest.fn(),
  getVectorStoreDetail: jest.fn(),
  assignFilesToVectorStore: jest.fn(),
  removeFilesFromVectorStore: jest.fn(),
  deleteVectorStore: jest.fn()
};

// Mock cho JwtUserGuard
const mockJwtUserGuard = {
  canActivate: jest.fn().mockImplementation(() => true)
};

// Mock cho CurrentUser decorator
jest.mock('@modules/auth/decorators', () => ({
  CurrentUser: () => (target: any, key: string, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;
    descriptor.value = function(...args: any[]) {
      return originalMethod.apply(this, [...args, 1]); // Thêm userId = 1
    };
    return descriptor;
  }
}));

describe('VectorStoreUserController (API)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    // Tạo module test với các mock
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [],
      providers: [],
    })
      .overrideProvider('VectorStoreUserController')
      .useValue(mockVectorStoreUserController)
      .overrideProvider('VectorStoreUserService')
      .useValue(mockVectorStoreUserService)
      .overrideGuard('JwtUserGuard')
      .useValue(mockJwtUserGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Mock các phương thức của controller
    mockVectorStoreUserController.createVectorStore.mockImplementation(async (dto, userId) => {
      return {
        code: 201,
        message: 'Tạo vector store thành công.',
        result: {
          storeId: 'vs_123e4567-e89b-12d3-a456-426614174000',
          storeName: dto.name,
          size: 0,
          agents: 0,
          files: 0,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        }
      };
    });

    mockVectorStoreUserController.getVectorStores.mockImplementation(async (queryDto, userId) => {
      return {
        code: 200,
        message: 'Lấy danh sách vector store thành công.',
        result: {
          items: [
            {
              storeId: 'vs_123e4567-e89b-12d3-a456-426614174000',
              storeName: 'VectorStoreAI',
              size: 1024,
              agents: 2,
              files: 3,
              createdAt: Date.now(),
              updatedAt: Date.now(),
            }
          ],
          meta: {
            totalItems: 1,
            itemCount: 1,
            itemsPerPage: 10,
            totalPages: 1,
            currentPage: 1,
          }
        }
      };
    });

    mockVectorStoreUserController.getVectorStoreDetail.mockImplementation(async (id, userId) => {
      return {
        code: 200,
        message: 'Lấy thông tin chi tiết vector store thành công.',
        result: {
          storeId: id,
          storeName: 'VectorStoreAI',
          size: 1024,
          agents: 2,
          files: 3,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        }
      };
    });

    mockVectorStoreUserController.assignFilesToVectorStore.mockImplementation(async (id, dto, userId) => {
      return {
        code: 200,
        message: 'Gán file vào vector store thành công.',
        result: {
          success: true,
          message: `Đã xử lý ${dto.fileIds.length} file với OpenAI thành công. Đã gán ${dto.fileIds.length} file vào vector store.`,
          processedFiles: dto.fileIds.length,
          processedFileDetails: dto.fileIds.map(fileId => ({
            id: fileId,
            openAiFileId: `file-${fileId.substring(0, 8)}`
          })),
          skippedFiles: []
        }
      };
    });

    mockVectorStoreUserController.removeFileFromVectorStore.mockImplementation(async (id, fileId, userId) => {
      return {
        code: 200,
        message: 'Xóa file khỏi vector store thành công.',
        result: {
          success: true,
          message: 'Đã xóa 1 file khỏi vector store thành công.',
        }
      };
    });

    mockVectorStoreUserController.removeFilesFromVectorStore.mockImplementation(async (id, dto, userId) => {
      return {
        code: 200,
        message: 'Xóa file khỏi vector store thành công.',
        result: {
          success: true,
          message: `Đã xóa ${dto.fileIds.length} file khỏi vector store thành công.`,
        }
      };
    });

    mockVectorStoreUserController.deleteVectorStores.mockImplementation(async (dto, userId) => {
      return {
        code: 200,
        message: `Đã xóa ${dto.storeIds.length} vector store thành công.`,
        result: {
          success: true,
          deletedCount: dto.storeIds.length,
        }
      };
    });
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /user/vector-stores', () => {
    it('nên tạo vector store mới', () => {
      const createVectorStoreDto = {
        name: 'VectorStoreAI'
      };

      return request(app.getHttpServer())
        .post('/user/vector-stores')
        .send(createVectorStoreDto)
        .expect(201)
        .expect((res) => {
          expect(res.body.code).toBe(201);
          expect(res.body.message).toContain('Tạo vector store thành công');
          expect(res.body.result).toBeDefined();
          expect(res.body.result.storeName).toBe('VectorStoreAI');
        });
    });
  });

  describe('GET /user/vector-stores', () => {
    it('nên lấy danh sách vector stores', () => {
      return request(app.getHttpServer())
        .get('/user/vector-stores')
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toContain('Lấy danh sách vector store thành công');
          expect(res.body.result).toBeDefined();
          expect(res.body.result.items).toBeInstanceOf(Array);
          expect(res.body.result.meta).toBeDefined();
        });
    });
  });

  describe('GET /user/vector-stores/:id', () => {
    it('nên lấy thông tin chi tiết vector store', () => {
      const storeId = 'vs_123e4567-e89b-12d3-a456-426614174000';

      return request(app.getHttpServer())
        .get(`/user/vector-stores/${storeId}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toContain('Lấy thông tin chi tiết vector store thành công');
          expect(res.body.result).toBeDefined();
          expect(res.body.result.storeId).toBe(storeId);
        });
    });
  });

  describe('POST /user/vector-stores/:id/files', () => {
    it('nên gán file vào vector store', () => {
      const storeId = 'vs_123e4567-e89b-12d3-a456-426614174000';
      const assignFilesDto = {
        fileIds: ['123e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174001']
      };

      return request(app.getHttpServer())
        .post(`/user/vector-stores/${storeId}/files`)
        .send(assignFilesDto)
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toContain('Gán file vào vector store thành công');
          expect(res.body.result).toBeDefined();
          expect(res.body.result.processedFiles).toBe(2);
        });
    });
  });

  describe('DELETE /user/vector-stores/:id/files/:fileId', () => {
    it('nên xóa file khỏi vector store', () => {
      const storeId = 'vs_123e4567-e89b-12d3-a456-426614174000';
      const fileId = '123e4567-e89b-12d3-a456-426614174000';

      return request(app.getHttpServer())
        .delete(`/user/vector-stores/${storeId}/files/${fileId}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toContain('Xóa file khỏi vector store thành công');
          expect(res.body.result).toBeDefined();
          expect(res.body.result.success).toBe(true);
        });
    });
  });

  describe('DELETE /user/vector-stores/:id/files', () => {
    it('nên xóa nhiều file khỏi vector store', () => {
      const storeId = 'vs_123e4567-e89b-12d3-a456-426614174000';
      const assignFilesDto = {
        fileIds: ['123e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174001']
      };

      return request(app.getHttpServer())
        .delete(`/user/vector-stores/${storeId}/files`)
        .send(assignFilesDto)
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toContain('Xóa file khỏi vector store thành công');
          expect(res.body.result).toBeDefined();
          expect(res.body.result.success).toBe(true);
        });
    });
  });

  describe('DELETE /user/vector-stores', () => {
    it('nên xóa nhiều vector store', () => {
      const deleteVectorStoresDto = {
        storeIds: ['vs_123e4567-e89b-12d3-a456-426614174000', 'vs_123e4567-e89b-12d3-a456-426614174001']
      };

      return request(app.getHttpServer())
        .delete('/user/vector-stores')
        .send(deleteVectorStoresDto)
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toContain('Đã xóa 2 vector store thành công');
          expect(res.body.result).toBeDefined();
          expect(res.body.result.deletedCount).toBe(2);
        });
    });
  });
});
