import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

/**
 * DTO cho thông tin chi tiết về file đã xử lý
 */
export class ProcessedFileDetailDto {
  @ApiProperty({
    description: 'ID của file',
    example: 'bfd70b5e-e3bd-403b-95ec-e8b346ffa753',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'ID của file trên OpenAI',
    example: 'file-EV7pF7UTU1exXGhZbSzLqm',
  })
  @Expose()
  openAiFileId: string;
}

/**
 * DTO cho kết quả gán file vào vector store
 */
export class AssignFilesResponseDto {
  @ApiProperty({
    description: 'Trạng thái thành công',
    example: true,
  })
  @Expose()
  success: boolean;

  @ApiProperty({
    description: 'Thông báo kết quả',
    example: 'Đã xử lý 1 file với OpenAI thành công. Đã gán 1 file vào vector store.',
  })
  @Expose()
  message: string;

  @ApiProperty({
    description: 'Số lượng file đã xử lý thành công',
    example: 1,
  })
  @Expose()
  processedFiles: number;

  @ApiProperty({
    description: 'Chi tiết về các file đã xử lý thành công',
    type: [ProcessedFileDetailDto],
  })
  @Expose()
  @Type(() => ProcessedFileDetailDto)
  processedFileDetails: ProcessedFileDetailDto[];

  @ApiProperty({
    description: 'Danh sách ID của các file bị bỏ qua',
    type: [String],
    example: [],
  })
  @Expose()
  skippedFiles: string[];
}
