import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { AffiliateAccountRepository } from '@modules/affiliate/repositories/affiliate-account.repository';
import { AffiliatePointConversionHistoryRepository } from '@modules/affiliate/repositories/affiliate-point-conversion-history.repository';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { AffiliatePointConversionQueryDto, AffiliatePointConversionDto } from '../dto';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class AffiliatePointConversionService {
  private readonly logger = new Logger(AffiliatePointConversionService.name);

  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly affiliatePointConversionHistoryRepository: AffiliatePointConversionHistoryRepository,
    private readonly userRepository: UserRepository,
  ) {}

  /**
   * Lấy danh sách lịch sử chuyển đổi điểm
   * @param queryDto Tham số truy vấn
   * @returns Danh sách lịch sử chuyển đổi điểm với phân trang
   */
  @Transactional()
  async getPointConversions(
    queryDto: AffiliatePointConversionQueryDto,
  ): Promise<PaginatedResult<AffiliatePointConversionDto>> {
    try {
      // Nếu có tìm kiếm theo tên/email người dùng, cần xử lý đặc biệt
      if (queryDto.search && isNaN(Number(queryDto.search))) {
        return await this.getPointConversionsWithUserSearch(queryDto);
      }

      // Lấy danh sách lịch sử chuyển đổi điểm với phân trang
      const { items, meta } =
        await this.affiliatePointConversionHistoryRepository.findWithPaginationForAdmin(
          queryDto,
        );

      // Lấy thông tin tài khoản affiliate và người dùng
      const affiliateAccountIds = items.map((item) => item.affiliateAccountId);
      const affiliateAccounts = await this.affiliateAccountRepository.findByIds(
        affiliateAccountIds,
      );

      const userIds = affiliateAccounts.map((account) => account.userId);
      const users = await this.userRepository.findByIds(userIds);

      // Xử lý dữ liệu trả về
      const conversionDtos = items.map((conversion) => {
        const affiliateAccount = affiliateAccounts.find(
          (account) => account.id === conversion.affiliateAccountId,
        );
        const user = users.find(
          (u) => u.id === affiliateAccount?.userId,
        );

        return {
          id: conversion.id,
          affiliateAccountId: conversion.affiliateAccountId,
          userName: user?.fullName || 'Unknown',
          userEmail: user?.email || 'Unknown',
          pointsConverted: Number(conversion.pointsConverted),
          conversionRate: Number(conversion.conversionRate),
          amount: Number(conversion.amount),
          createdAt: Number(conversion.createdAt),
          status: conversion.status,
        };
      });

      return {
        items: conversionDtos,
        meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting point conversions: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy danh sách lịch sử chuyển đổi điểm',
      );
    }
  }

  /**
   * Lấy chi tiết lịch sử chuyển đổi điểm
   * @param id ID của bản ghi chuyển đổi
   * @returns Thông tin chi tiết lịch sử chuyển đổi điểm
   */
  @Transactional()
  async getPointConversionById(
    id: number,
  ): Promise<AffiliatePointConversionDto> {
    try {
      // Lấy thông tin chi tiết lịch sử chuyển đổi điểm
      const conversion =
        await this.affiliatePointConversionHistoryRepository.findById(id);

      if (!conversion) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.POINT_CONVERSION_NOT_FOUND,
          'Không tìm thấy lịch sử chuyển đổi điểm',
        );
      }

      // Lấy thông tin tài khoản affiliate và người dùng
      const affiliateAccount = await this.affiliateAccountRepository.findById(
        conversion.affiliateAccountId,
      );

      if (!affiliateAccount) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản affiliate',
        );
      }

      const user = await this.userRepository.findById(affiliateAccount.userId);

      // Xử lý dữ liệu trả về
      return {
        id: conversion.id,
        affiliateAccountId: conversion.affiliateAccountId,
        userName: user?.fullName || 'Unknown',
        userEmail: user?.email || 'Unknown',
        pointsConverted: Number(conversion.pointsConverted),
        conversionRate: Number(conversion.conversionRate),
        amount: Number(conversion.amount),
        createdAt: Number(conversion.createdAt),
        status: conversion.status,
      };
    } catch (error) {
      this.logger.error(
        `Error getting point conversion: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy thông tin chi tiết lịch sử chuyển đổi điểm',
      );
    }
  }

  /**
   * Lấy danh sách lịch sử chuyển đổi điểm với tìm kiếm theo thông tin người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách lịch sử chuyển đổi điểm với phân trang
   */
  @Transactional()
  private async getPointConversionsWithUserSearch(
    queryDto: AffiliatePointConversionQueryDto,
  ): Promise<PaginatedResult<AffiliatePointConversionDto>> {
    try {
      const { page = 1, limit = 10, search } = queryDto;

      // Tìm kiếm người dùng theo tên hoặc email
      const users = await this.userRepository.findByNameOrEmail(search || '');
      const userIds = users.map(user => user.id);

      if (userIds.length === 0) {
        // Không tìm thấy người dùng nào, trả về kết quả trống
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: limit,
            totalPages: 0,
            currentPage: page,
          },
        };
      }

      // Tìm tài khoản affiliate của các người dùng
      const affiliateAccounts = await this.affiliateAccountRepository.createQueryBuilder('account')
        .where('account.userId IN (:...userIds)', { userIds })
        .getMany();

      const affiliateAccountIds = affiliateAccounts.map(account => account.id);

      if (affiliateAccountIds.length === 0) {
        // Không tìm thấy tài khoản affiliate nào, trả về kết quả trống
        return {
          items: [],
          meta: {
            totalItems: 0,
            itemCount: 0,
            itemsPerPage: limit,
            totalPages: 0,
            currentPage: page,
          },
        };
      }

      // Tạo queryDto mới với affiliateAccountIds
      const newQueryDto = { ...queryDto };
      delete newQueryDto.search; // Xóa tham số search vì đã xử lý

      // Lấy danh sách lịch sử chuyển đổi điểm với phân trang
      const queryBuilder = this.affiliatePointConversionHistoryRepository.createQueryBuilder('conversion')
        .where('conversion.affiliateAccountId IN (:...affiliateAccountIds)', { affiliateAccountIds });

      // Thêm điều kiện thời gian nếu có
      if (queryDto.begin) {
        queryBuilder.andWhere('conversion.createdAt >= :begin', { begin: queryDto.begin });
      }

      if (queryDto.end) {
        queryBuilder.andWhere('conversion.createdAt <= :end', { end: queryDto.end });
      }

      // Thêm điều kiện trạng thái nếu có
      if (queryDto.status) {
        queryBuilder.andWhere('conversion.status = :status', { status: queryDto.status });
      }

      // Đếm tổng số bản ghi
      const totalItems = await queryBuilder.getCount();

      // Thêm sắp xếp và phân trang
      const sortBy = queryDto.sortBy || 'createdAt';
      const sortDirection = queryDto.sortDirection || 'DESC';

      queryBuilder
        .orderBy(`conversion.${sortBy}`, sortDirection)
        .skip((page - 1) * limit)
        .take(limit);

      // Lấy dữ liệu
      const items = await queryBuilder.getMany();

      // Tính toán metadata
      const totalPages = Math.ceil(totalItems / limit);

      // Xử lý dữ liệu trả về
      const conversionDtos = await Promise.all(items.map(async (conversion) => {
        const affiliateAccount = affiliateAccounts.find(
          (account) => account.id === conversion.affiliateAccountId,
        );

        const user = await this.userRepository.findById(affiliateAccount?.userId || 0);

        return {
          id: conversion.id,
          affiliateAccountId: conversion.affiliateAccountId,
          userName: user?.fullName || 'Unknown',
          userEmail: user?.email || 'Unknown',
          pointsConverted: Number(conversion.pointsConverted),
          conversionRate: Number(conversion.conversionRate),
          amount: Number(conversion.amount),
          createdAt: Number(conversion.createdAt),
          status: conversion.status,
        };
      }));

      return {
        items: conversionDtos,
        meta: {
          totalItems,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error getting point conversions with user search: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy danh sách lịch sử chuyển đổi điểm',
      );
    }
  }
}
