import { Injectable, Logger } from '@nestjs/common';
import { AffiliateRankDto, AffiliateRankQueryDto, AffiliateRankStatisticsDto, AffiliateRankDistributionDto, AffiliateRankRevenueDto, AffiliateRankOverviewDto, CreateAffiliateRankDto, CreateAffiliateRankResponseDto } from '../dto';
import { AffiliateRankRepository } from '@modules/affiliate/repositories/affiliate-rank.repository';
import { AppException, ErrorCode } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { Transactional } from 'typeorm-transactional';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';
import { AffiliateAccountRepository, AffiliateCustomerOrderRepository } from '../../repositories';
import { S3Service } from '@shared/services/s3.service';
import { TimeIntervalEnum } from '@shared/utils/time/time-interval.util';
import { FileSizeEnum } from '@shared/utils/file/file-size.util';
import { CdnService } from '@shared/services/cdn.service';

@Injectable()
export class AffiliateRankService {
  private readonly logger = new Logger(AffiliateRankService.name);

  constructor(
    private readonly affiliateRankRepository: AffiliateRankRepository,
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly affiliateCustomerOrderRepository: AffiliateCustomerOrderRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService
  ) {}

  /**
   * Lấy danh sách rank affiliate với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách rank affiliate với phân trang
   */
  @Transactional()
  async getRanks(
    queryDto: AffiliateRankQueryDto,
  ): Promise<PaginatedResult<AffiliateRankDto>> {
    try {
      // Lấy danh sách rank affiliate với phân trang
      const { items: ranks, meta } =
        await this.affiliateRankRepository.findWithPagination(queryDto);

      // Xử lý dữ liệu trả về
      const rankDtos = await Promise.all(ranks.map(async (rank) => {
        // Tạo URL tạm thời cho rankBadge nếu có
        let rankBadgeUrl: string | null = null;
        if (rank.rankBadge) {
          rankBadgeUrl = this.cdnService.generateUrlView(
            rank.rankBadge,
            TimeIntervalEnum.ONE_HOUR
          );
        }

        return {
          id: rank.id,
          rankName: rank.rankName,
          rankBadge: rankBadgeUrl || rank.rankBadge, // Sử dụng URL tạm thời nếu có, nếu không sử dụng key
          commission: rank.commission,
          minCondition: rank.minCondition,
          maxCondition: rank.maxCondition,
          isActive: rank.isActive,
          displayOrder: rank.displayOrder,
          description: rank.description,
          createdAt: rank.createdAt,
          updatedAt: rank.updatedAt,
        };
      }));

      return {
        items: rankDtos,
        meta,
      };
    } catch (error) {
      this.logger.error(
        `Error getting affiliate ranks: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy danh sách rank affiliate',
      );
    }
  }

  /**
   * Lấy thông tin chi tiết rank affiliate
   * @param id ID của rank affiliate
   * @returns Thông tin chi tiết rank affiliate
   */
  @Transactional()
  async getRankById(id: number): Promise<AffiliateRankDto> {
    try {
      // Kiểm tra id có hợp lệ không
      if (id === null || id === undefined || isNaN(Number(id))) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.RANK_NOT_FOUND,
          'ID rank affiliate không hợp lệ',
        );
      }

      // Lấy thông tin rank affiliate
      const rank = await this.affiliateRankRepository.findById(Number(id));
      if (!rank) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.RANK_NOT_FOUND,
          'Không tìm thấy rank affiliate',
        );
      }

      // Tạo URL tạm thời cho rankBadge nếu có
      let rankBadgeUrl: string | null = null;
      if (rank.rankBadge) {
        rankBadgeUrl = this.cdnService.generateUrlView(
          rank.rankBadge,
          TimeIntervalEnum.ONE_HOUR
        );
      }

      return {
        id: rank.id,
        rankName: rank.rankName,
        rankBadge: rankBadgeUrl || rank.rankBadge, // Sử dụng URL tạm thời nếu có, nếu không sử dụng key
        commission: rank.commission,
        minCondition: rank.minCondition,
        maxCondition: rank.maxCondition,
        isActive: rank.isActive,
        displayOrder: rank.displayOrder,
        description: rank.description,
        createdAt: rank.createdAt,
        updatedAt: rank.updatedAt,
      };
    } catch (error) {
      this.logger.error(
        `Error getting affiliate rank by ID: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.RANK_NOT_FOUND,
        'Lỗi khi lấy thông tin rank affiliate',
      );
    }
  }

  /**
   * Tạo rank affiliate mới
   * @param createRankDto Thông tin rank cần tạo
   * @returns Thông tin rank đã tạo và URL tạm thời để upload ảnh (nếu có)
   */
  @Transactional()
  async createRank(
    createRankDto: CreateAffiliateRankDto,
  ): Promise<CreateAffiliateRankResponseDto> {
    try {
      // Kiểm tra điều kiện min và max
      if (createRankDto.minCondition >= createRankDto.maxCondition) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.INVALID_RANK_CONDITION,
          'Điều kiện tối thiểu phải nhỏ hơn điều kiện tối đa',
        );
      }

      // Kiểm tra xem có rank nào có khoảng điều kiện chồng chéo không
      const overlappingRank =
        await this.affiliateRankRepository.findOverlappingRank(
          createRankDto.minCondition,
          createRankDto.maxCondition,
        );

      if (overlappingRank) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.RANK_CONDITION_OVERLAP,
          `Khoảng điều kiện chồng chéo với rank ${overlappingRank.rankName}`,
        );
      }

      // Tạo key cho rankBadge nếu cần upload ảnh
      let uploadUrl: string | undefined;
      let rankBadgeKey = createRankDto.rankBadge || '';

      // Nếu có mediaType, tạo URL tạm thời để upload ảnh
      if (createRankDto.mediaType) {
        // Tạo key cho file (đường dẫn trên S3/Cloudflare R2)
        rankBadgeKey = `affiliate/rank/${createRankDto.rankName.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}.${this.getFileExtension(createRankDto.mediaType)}`;

        // Xác định kích thước tối đa của file
        let maxFileSize: FileSizeEnum = FileSizeEnum.ONE_MB; // Mặc định là 1MB

        // Nếu có maxSize, chuyển đổi thành FileSizeEnum phù hợp
        if (createRankDto.maxSize) {
          if (createRankDto.maxSize <= FileSizeEnum.ONE_MB) {
            maxFileSize = FileSizeEnum.ONE_MB;
          } else if (createRankDto.maxSize <= FileSizeEnum.TWO_MB) {
            maxFileSize = FileSizeEnum.TWO_MB;
          } else if (createRankDto.maxSize <= FileSizeEnum.FIVE_MB) {
            maxFileSize = FileSizeEnum.FIVE_MB;
          } else {
            maxFileSize = FileSizeEnum.TEN_MB;
          }
        }

        // Tạo URL tạm thời có chữ ký số với thời hạn 15 phút
        uploadUrl = await this.s3Service.createPresignedWithID(
          rankBadgeKey,
          TimeIntervalEnum.FIFTEEN_MINUTES,
          createRankDto.mediaType,
          maxFileSize
        );
      }

      // Lấy tất cả các rank để xác định displayOrder mới
      const existingRanks = await this.affiliateRankRepository.find();

      // Tìm displayOrder lớn nhất hiện tại
      const maxDisplayOrder = existingRanks.reduce(
        (max, rank) => (rank.displayOrder > max ? rank.displayOrder : max),
        0
      );

      // Tạo rank mới với displayOrder tự động tăng
      const newRank = await this.affiliateRankRepository.createRank({
        ...createRankDto,
        rankBadge: rankBadgeKey,
        displayOrder: maxDisplayOrder + 1, // Tự động tăng displayOrder
        createdAt: Math.floor(Date.now() / 1000),
      });

      // Lấy thông tin rank đã tạo
      const rankInfo = await this.getRankById(newRank.id);

      // Trả về thông tin rank và URL tạm thời để upload ảnh (nếu có)
      return {
        ...rankInfo,
        uploadUrl,
      };
    } catch (error) {
      this.logger.error(
        `Error creating affiliate rank: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.RANK_CREATION_FAILED,
        'Lỗi khi tạo rank affiliate',
      );
    }
  }

  /**
   * Lấy phần mở rộng file dựa trên loại media
   * @param mediaType Loại media
   * @returns Phần mở rộng file
   */
  private getFileExtension(mediaType: string): string {
    switch (mediaType) {
      case 'image/png':
        return 'png';
      case 'image/jpeg':
        return 'jpg';
      case 'image/gif':
        return 'gif';
      case 'image/webp':
        return 'webp';
      default:
        return 'png';
    }
  }

  /**
   * Cập nhật thông tin rank affiliate
   * @param id ID của rank affiliate
   * @param updateRankDto Thông tin cần cập nhật
   * @returns Thông tin rank đã cập nhật
   */
  @Transactional()
  async updateRank(
    id: number,
    updateRankDto: Partial<
      Omit<AffiliateRankDto, 'id' | 'createdAt' | 'updatedAt'>
    >,
  ): Promise<AffiliateRankDto> {
    try {
      // Lấy thông tin rank hiện tại
      const currentRank = await this.affiliateRankRepository.findById(id);
      if (!currentRank) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.RANK_NOT_FOUND,
          'Không tìm thấy rank affiliate',
        );
      }

      // Kiểm tra điều kiện min và max nếu có cập nhật
      const minCondition =
        updateRankDto.minCondition ?? currentRank.minCondition;
      const maxCondition =
        updateRankDto.maxCondition ?? currentRank.maxCondition;

      if (minCondition >= maxCondition) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.INVALID_RANK_CONDITION,
          'Điều kiện tối thiểu phải nhỏ hơn điều kiện tối đa',
        );
      }

      // Kiểm tra xem có rank nào có khoảng điều kiện chồng chéo không
      if (
        updateRankDto.minCondition !== undefined ||
        updateRankDto.maxCondition !== undefined
      ) {
        const overlappingRank =
          await this.affiliateRankRepository.findOverlappingRank(
            minCondition,
            maxCondition,
            id,
          );

        if (overlappingRank) {
          throw new AppException(
            AFFILIATE_ERROR_CODES.RANK_CONDITION_OVERLAP,
            `Khoảng điều kiện chồng chéo với rank ${overlappingRank.rankName}`,
          );
        }
      }

      // Cập nhật rank
      await this.affiliateRankRepository.updateRank(id, {
        ...updateRankDto,
        updatedAt: Math.floor(Date.now() / 1000),
      });

      return this.getRankById(id);
    } catch (error) {
      this.logger.error(
        `Error updating affiliate rank: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.RANK_UPDATE_FAILED,
        'Lỗi khi cập nhật rank affiliate',
      );
    }
  }

  /**
   * Cập nhật trạng thái kích hoạt của rank affiliate
   * @param id ID của rank affiliate
   * @param isActive Trạng thái kích hoạt
   * @returns Thông tin rank đã cập nhật
   */
  @Transactional()
  async updateRankStatus(
    id: number,
    isActive: boolean,
  ): Promise<AffiliateRankDto> {
    try {
      // Cập nhật trạng thái kích hoạt
      await this.affiliateRankRepository.updateStatus(id, isActive);

      // Lấy thông tin rank đã cập nhật
      return this.getRankById(id);
    } catch (error) {
      this.logger.error(
        `Error updating affiliate rank status: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.RANK_UPDATE_FAILED,
        'Lỗi khi cập nhật trạng thái rank affiliate',
      );
    }
  }

  /**
   * Lấy thống kê về rank affiliate
   * @returns Thống kê về rank affiliate
   */
  async getStatistics(): Promise<AffiliateRankStatisticsDto> {
    try {
      const now = Math.floor(Date.now() / 1000);

      // Lấy tất cả các rank
      const ranks = await this.affiliateRankRepository.find();

      // Tính tổng số rank và số rank đang hoạt động
      const totalRanks = ranks.length;
      const activeRanks = ranks.filter(rank => rank.isActive).length;

      // Khởi tạo mảng phân bố và doanh thu
      const distribution: AffiliateRankDistributionDto[] = [];
      const revenue: AffiliateRankRevenueDto[] = [];

      // Tính tổng số tài khoản affiliate
      const totalAccounts = await this.affiliateAccountRepository.count();

      // Biến để theo dõi rank phổ biến nhất và rank có doanh thu cao nhất
      let mostPopularRank = '';
      let highestRevenueRank = '';
      let maxAccountCount = 0;
      let maxRevenue = 0;
      let totalCommissionPaid = 0;

      // Xử lý từng rank
      for (const rank of ranks) {
        // Tính doanh thu, hoa hồng và số đơn hàng
        const { totalRevenue, totalCommission, orderCount } =
          await this.affiliateCustomerOrderRepository.calculateStatsByRankId(rank.id);

        // Sử dụng số đơn hàng làm số tài khoản (đơn giản hóa)
        const accountCount = orderCount > 0 ? orderCount : 0;

        // Tính phần trăm
        const percentage = totalAccounts > 0 ? (accountCount / totalAccounts) * 100 : 0;

        // Tạo URL tạm thời cho rankBadge nếu có
        let rankBadgeUrl: string | null = null;
        if (rank.rankBadge) {
          rankBadgeUrl = this.cdnService.generateUrlView(
            rank.rankBadge,
            TimeIntervalEnum.ONE_HOUR
          );
        }

        // Thêm vào phân bố
        distribution.push({
          id: rank.id,
          rankName: rank.rankName,
          rankBadge: rankBadgeUrl || rank.rankBadge, // Sử dụng URL tạm thời nếu có, nếu không sử dụng key
          commission: rank.commission,
          accountCount,
          percentage
        });

        // Cập nhật rank phổ biến nhất
        if (accountCount > maxAccountCount) {
          maxAccountCount = accountCount;
          mostPopularRank = rank.rankName;
        }

        // Thêm vào doanh thu
        revenue.push({
          id: rank.id,
          rankName: rank.rankName,
          totalRevenue,
          totalCommission,
          orderCount
        });

        // Cập nhật rank có doanh thu cao nhất
        if (totalRevenue > maxRevenue) {
          maxRevenue = totalRevenue;
          highestRevenueRank = rank.rankName;
        }

        // Cộng dồn tổng hoa hồng
        totalCommissionPaid += totalCommission;
      }

      // Sắp xếp phân bố theo số lượng tài khoản giảm dần
      distribution.sort((a, b) => b.accountCount - a.accountCount);

      // Sắp xếp doanh thu theo tổng doanh thu giảm dần
      revenue.sort((a, b) => b.totalRevenue - a.totalRevenue);

      // Tạo đối tượng tổng quan
      const overview: AffiliateRankOverviewDto = {
        totalRanks,
        activeRanks,
        mostPopularRank,
        highestRevenueRank,
        totalCommissionPaid
      };

      return {
        overview,
        distribution,
        revenue,
        updatedAt: now
      };
    } catch (error) {
      this.logger.error(`Error getting affiliate rank statistics: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy thống kê về rank affiliate'
      );
    }
  }

  /**
   * Lấy khoảng minCondition và maxCondition đã sử dụng
   * @returns Khoảng minCondition và maxCondition đã sử dụng
   */
  async getUsedConditionRanges(): Promise<{ minCondition: number; maxCondition: number }[]> {
    try {
      // Lấy khoảng điều kiện đã sử dụng trực tiếp từ repository
      const conditionRanges = await this.affiliateRankRepository.getUsedConditionRanges();

      // Đảm bảo không có giá trị NaN hoặc null trong kết quả
      return conditionRanges.map(range => ({
        minCondition: typeof range.minCondition === 'number' && !isNaN(range.minCondition)
          ? range.minCondition
          : 0,
        maxCondition: typeof range.maxCondition === 'number' && !isNaN(range.maxCondition)
          ? range.maxCondition
          : 0
      }));
    } catch (error) {
      this.logger.error(
        `Error getting used condition ranges: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.STATISTICS_RETRIEVAL_FAILED,
        'Lỗi khi lấy khoảng điều kiện đã sử dụng',
      );
    }
  }

  /**
   * Xóa rank affiliate
   * @param id ID của rank cần xóa
   * @returns Kết quả xóa
   */
  @Transactional()
  async deleteRank(id: number): Promise<void> {
    try {
      // Kiểm tra id có hợp lệ không
      if (id === null || id === undefined || isNaN(Number(id))) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.RANK_NOT_FOUND,
          'ID rank affiliate không hợp lệ',
        );
      }

      // Kiểm tra rank có tồn tại không
      const rank = await this.affiliateRankRepository.findById(Number(id));
      if (!rank) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.RANK_NOT_FOUND,
          'Không tìm thấy rank affiliate',
        );
      }

      // Xóa rank
      await this.affiliateRankRepository.deleteRank(Number(id));
    } catch (error) {
      this.logger.error(
        `Error deleting affiliate rank: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.RANK_DELETION_FAILED,
        'Lỗi khi xóa rank affiliate',
      );
    }
  }
}
