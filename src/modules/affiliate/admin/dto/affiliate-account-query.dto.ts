import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto/query.dto';

/**
 * Enum cho trạng thái tài khoản affiliate
 */
export enum AffiliateAccountStatus {
  ACTIVE = 'ACTIVE',
  PENDING = 'PENDING',
  BLOCKED = 'BLOCKED',
  INACTIVE = 'INACTIVE'
}

/**
 * DTO cho tham số truy vấn danh sách tài khoản affiliate
 */
export class AffiliateAccountQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Trạng thái tài khoản',
    enum: AffiliateAccountStatus,
    example: AffiliateAccountStatus.ACTIVE
  })
  @IsOptional()
  @IsEnum(AffiliateAccountStatus)
  status?: AffiliateAccountStatus;

  @ApiPropertyOptional({
    description: 'ID rank affiliate',
    example: 1
  })
  @IsOptional()
  @Type(() => Number)
  rankId?: number;

  @ApiPropertyOptional({
    description: 'Loại tài khoản (PERSONAL, BUSINESS)',
    example: 'PERSONAL'
  })
  @IsOptional()
  @IsString()
  accountType?: string;
}
