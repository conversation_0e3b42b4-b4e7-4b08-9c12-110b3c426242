import { ApiProperty } from '@nestjs/swagger';
import { PointConversionStatus } from '@modules/affiliate/enums';

/**
 * DTO cho thông tin lịch sử chuyển đổi điểm (Admin)
 */
export class AffiliatePointConversionDto {
  /**
   * ID của bản ghi chuyển đổi
   */
  @ApiProperty({
    description: 'ID của bản ghi chuyển đổi',
    example: 1,
  })
  id: number;

  /**
   * ID tài khoản affiliate
   */
  @ApiProperty({
    description: 'ID tài khoản affiliate',
    example: 1,
  })
  affiliateAccountId: number;

  /**
   * Tên người dùng
   */
  @ApiProperty({
    description: 'Tên người dùng',
    example: 'Nguyễn Văn A',
  })
  userName: string;

  /**
   * Email người dùng
   */
  @ApiProperty({
    description: 'Email người dùng',
    example: '<EMAIL>',
  })
  userEmail: string;

  /**
   * <PERSON>ố point đổi được
   */
  @ApiProperty({
    description: 'Số point đổi được',
    example: 10000,
  })
  pointsConverted: number;

  /**
   * Tỷ lệ chuyển đổi
   */
  @ApiProperty({
    description: 'Tỷ lệ chuyển đổi',
    example: 1,
  })
  conversionRate: number;

  /**
   * Số tiền đã chuyển đổi
   */
  @ApiProperty({
    description: 'Số tiền đã chuyển đổi',
    example: 10000,
  })
  amount: number;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @ApiProperty({
    description: 'Thời gian tạo (Unix timestamp)',
    example: **********,
  })
  createdAt: number;

  /**
   * Trạng thái chuyển đổi
   */
  @ApiProperty({
    description: 'Trạng thái chuyển đổi',
    enum: PointConversionStatus,
    example: PointConversionStatus.SUCCESS,
  })
  status: PointConversionStatus;
}
