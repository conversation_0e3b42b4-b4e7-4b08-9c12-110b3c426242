import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { WithdrawalStatus } from './affiliate-withdrawal-query.dto';

/**
 * DTO cho thông tin ngân hàng trong yêu cầu rút tiền
 */
export class AffiliateBankInfoDto {
  @ApiProperty({
    description: 'Mã ngân hàng',
    example: 'TCB',
  })
  @IsString()
  bankCode: string;

  @ApiProperty({
    description: 'Số tài khoản ngân hàng',
    example: '***********',
  })
  @IsString()
  accountNumber: string;

  @ApiProperty({
    description: 'Tên tài khoản ngân hàng',
    example: 'NGUYEN VAN A',
  })
  @IsString()
  accountName: string;
}

/**
 * DTO cho thông tin người dùng trong yêu cầu rút tiền
 */
export class AffiliateWithdrawalUserDto {
  @ApiProperty({
    description: 'ID của người dùng',
    example: 123,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Họ tên người dùng',
    example: 'Nguyễn Văn A',
  })
  @IsString()
  fullName: string;

  @ApiProperty({
    description: 'Email người dùng',
    example: '<EMAIL>',
  })
  @IsString()
  email: string;

  @ApiPropertyOptional({
    description: 'Số điện thoại người dùng',
    example: '**********',
  })
  @IsOptional()
  @IsString()
  phoneNumber?: string;
}

/**
 * DTO cho thông tin yêu cầu rút tiền
 */
export class AffiliateWithdrawalDto {
  @ApiProperty({
    description: 'ID của yêu cầu rút tiền',
    example: 789,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'ID tài khoản affiliate',
    example: 123,
  })
  @IsNumber()
  affiliateAccountId: number;

  @ApiProperty({
    description: 'Thông tin người dùng',
    type: AffiliateWithdrawalUserDto,
  })
  @ValidateNested()
  @Type(() => AffiliateWithdrawalUserDto)
  user: AffiliateWithdrawalUserDto;

  @ApiProperty({
    description: 'Số tiền rút',
    example: 1000000,
  })
  @IsNumber()
  amount: number;

  @ApiProperty({
    description: 'Tiền thuế VAT',
    example: 100000,
  })
  @IsNumber()
  vatAmount: number;

  @ApiProperty({
    description: 'Tiền thực nhận',
    example: 900000,
  })
  @IsNumber()
  netPayment: number;

  @ApiProperty({
    description: 'Thông tin ngân hàng',
    type: AffiliateBankInfoDto,
  })
  @ValidateNested()
  @Type(() => AffiliateBankInfoDto)
  bankInfo: AffiliateBankInfoDto;

  @ApiProperty({
    description: 'Trạng thái yêu cầu',
    enum: WithdrawalStatus,
    example: WithdrawalStatus.APPROVED,
  })
  @IsEnum(WithdrawalStatus)
  status: WithdrawalStatus;

  @ApiProperty({
    description: 'Thời gian tạo yêu cầu (Unix timestamp)',
    example: **********,
  })
  @IsNumber()
  createdAt: number;

  @ApiPropertyOptional({
    description: 'Thời gian hoàn thành yêu cầu (Unix timestamp)',
    example: **********,
  })
  @IsOptional()
  @IsNumber()
  finishAt?: number;

  @ApiPropertyOptional({
    description: 'Lý do từ chối',
    example: 'Thông tin tài khoản không chính xác',
  })
  @IsOptional()
  @IsString()
  rejectReason?: string;

  @ApiPropertyOptional({
    description: 'Đường dẫn hóa đơn đầu vào',
    example: 'invoice_789.pdf',
  })
  @IsOptional()
  @IsString()
  purchaseInvoice?: string;

  @ApiPropertyOptional({
    description: 'ID nhân viên xử lý',
    example: 456,
  })
  @IsOptional()
  @IsNumber()
  processedByEmployeeId?: number;

  @ApiPropertyOptional({
    description: 'Tên nhân viên xử lý',
    example: 'Lê Thị C',
  })
  @IsOptional()
  @IsString()
  processedByEmployeeName?: string;
}
