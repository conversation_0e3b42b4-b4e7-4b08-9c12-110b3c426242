import { Injectable } from '@nestjs/common';
import { DataSource, Repository, Not, IsNull } from 'typeorm';
import { User } from '@modules/user/entities';
import { PaginatedResult } from '@/common/response';
import { AffiliateCustomerQueryDto } from '../user/dto';

/**
 * Repository cho User (Affiliate Customer)
 * Extends Repository<User> theo Repository Standard #2
 */
@Injectable()
export class AffiliateCustomerRepository extends Repository<User> {
  constructor(dataSource: DataSource) {
    super(User, dataSource.createEntityManager());
  }

  /**
   * Tìm khách hàng theo ID
   * @param id ID của khách hàng
   * @returns Thông tin khách hàng hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<User | null> {
    return this.findOne({ where: { id } });
  }

  /**
   * Tìm khách hàng theo ID tài khoản affiliate
   * @param affiliateAccountId ID tài khoản affiliate
   * @returns Danh sách khách hàng
   */
  async findByAffiliateAccountId(affiliateAccountId: number): Promise<User[]> {
    return this.find({
      where: {
        affiliateAccountId,
      },
    });
  }

  /**
   * Đếm tổng số khách hàng có affiliateAccountId
   * @returns Tổng số khách hàng
   */
  async countTotal(): Promise<number> {
    return this.count({
      where: {
        affiliateAccountId: Not(IsNull()),
      },
    });
  }

  /**
   * Đếm số khách hàng theo tài khoản affiliate
   * @param affiliateAccountId ID tài khoản affiliate
   * @returns Số khách hàng
   */
  async countByAffiliateAccount(affiliateAccountId: number): Promise<number> {
    return this.count({
      where: {
        affiliateAccountId,
      },
    });
  }

  /**
   * Tìm danh sách khách hàng với phân trang
   * @param affiliateAccountId ID tài khoản affiliate
   * @param queryDto Tham số truy vấn
   * @returns Danh sách khách hàng với phân trang
   */
  async findWithPagination(
    affiliateAccountId: number,
    queryDto: AffiliateCustomerQueryDto,
  ): Promise<PaginatedResult<User>> {
    const {
      page = 1,
      limit = 10,
      search,
      begin,
      end,
      sortBy = 'createdAt',
      sortDirection = 'DESC',
    } = queryDto;

    const skip = (page - 1) * limit;

    // Xây dựng query
    const queryBuilder = this.createQueryBuilder('user').where(
      'user.affiliateAccountId = :affiliateAccountId',
      {
        affiliateAccountId,
      },
    );

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      queryBuilder.andWhere(
        '(user.fullName LIKE :search OR user.email LIKE :search OR user.phoneNumber LIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Thêm điều kiện thời gian nếu có
    if (begin) {
      queryBuilder.andWhere('user.createdAt >= :begin', { begin });
    }

    if (end) {
      queryBuilder.andWhere('user.createdAt <= :end', { end });
    }

    // Đếm tổng số bản ghi
    const totalItems = await queryBuilder.getCount();

    // Thêm sắp xếp và phân trang
    queryBuilder
      .orderBy(`user.${sortBy}`, sortDirection)
      .skip(skip)
      .take(limit);

    // Lấy dữ liệu
    const items = await queryBuilder.getMany();

    // Tính toán metadata
    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }
}
