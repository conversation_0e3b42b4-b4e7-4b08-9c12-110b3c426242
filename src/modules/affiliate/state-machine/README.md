# Sử dụng XState/FSM trong RedAI Backend

## Giới thiệu

Tài liệu này mô tả cách sử dụng XState và @xstate/fsm để quản lý các quy trình nghiệp vụ nhiều bước trong RedAI Backend. Cá<PERSON> thư viện này cung cấp một cách tiếp cận dựa trên máy trạng thái hữu hạn (Finite State Machine - FSM) để quản lý luồng nghiệp vụ phức tạp một cách rõ ràng và dễ bảo trì.

## Cài đặt

Để sử dụng phiên bản ổn định của XState, hãy cài đặt:

```bash
npm install xstate@4.38.2 @xstate/fsm@1.6.3
```

Phiên bản 4.38.2 là phiên bản ổn định cuối cùng của XState v4 và được khuyến nghị sử dụng cho các dự án sản xuất.

## Khái niệm cơ bản

### Máy trạng thái hữu hạn (FSM)

Máy trạng thái hữu hạn là một mô hình toán học mô tả một hệ thống có thể ở một trong số hữu hạn các trạng thái tại một thời điểm. FSM có thể chuyển từ trạng thái này sang trạng thái khác dựa trên các sự kiện đầu vào.

### Các thành phần chính của XState

1. **State (Trạng thái)**: Đại diện cho trạng thái hiện tại của hệ thống
2. **Event (Sự kiện)**: Kích hoạt chuyển đổi giữa các trạng thái
3. **Transition (Chuyển đổi)**: Định nghĩa cách hệ thống di chuyển từ trạng thái này sang trạng thái khác
4. **Action (Hành động)**: Thực hiện các tác vụ khi xảy ra chuyển đổi
5. **Context (Ngữ cảnh)**: Dữ liệu được lưu trữ và có thể được cập nhật trong quá trình chuyển đổi

## Ví dụ: Quy trình đăng ký Affiliate

### Định nghĩa máy trạng thái

```typescript
import { Machine, interpret, assign } from 'xstate';

const personalAffiliateMachine = Machine({
  id: 'personalAffiliate',
  initial: 'termsAcceptance',
  context: {
    userData: null,
    bankInfo: null,
    signature: null,
    otpVerified: false,
  },
  states: {
    termsAcceptance: {
      on: {
        ACCEPT_TERMS: {
          target: 'personalInfo',
          actions: assign({
            userData: (_, event) => event.userData,
          }),
        },
      },
    },
    personalInfo: {
      on: {
        SUBMIT_INFO: {
          target: 'bankInfo',
          actions: assign({
            userData: (_, event) => event.userData,
          }),
        },
      },
    },
    bankInfo: {
      on: {
        SUBMIT_BANK_INFO: {
          target: 'handwrittenSignature',
          actions: assign({
            bankInfo: (_, event) => event.bankInfo,
          }),
        },
      },
    },
    handwrittenSignature: {
      on: {
        SUBMIT_SIGNATURE: {
          target: 'otpVerification',
          actions: assign({
            signature: (_, event) => event.signature,
          }),
        },
      },
    },
    otpVerification: {
      on: {
        VERIFY_OTP: {
          target: 'pendingApproval',
          actions: assign({
            otpVerified: (_, event) => event.verified,
          }),
        },
      },
    },
    pendingApproval: {
      on: {
        ADMIN_APPROVE: 'approved',
        ADMIN_REJECT: 'rejected',
      },
    },
    approved: {
      type: 'final',
    },
    rejected: {
      type: 'final',
    },
  },
});
```

### Tạo service để quản lý máy trạng thái

```typescript
import { Injectable } from '@nestjs/common';
import { Machine, interpret, assign } from 'xstate';

@Injectable()
export class AffiliateStateMachineService {
  private machines = new Map<string, any>(); // Lưu trữ theo userId

  private personalAffiliateMachine = Machine({
    // Định nghĩa máy trạng thái như ở trên
  });

  startPersonalAffiliate(userId: string, initialData: any) {
    const machine = interpret(this.personalAffiliateMachine)
      .onTransition((state) => {
        console.log(`State changed for user ${userId}:`, state.value);
        this.handleStateChange(userId, state);
      })
      .start();

    this.machines.set(userId, machine);
    return machine;
  }

  sendEvent(userId: string, event: string, payload?: any) {
    const machine = this.machines.get(userId);
    if (machine) {
      machine.send({ type: event, ...payload });
    }
  }

  getCurrentState(userId: string) {
    const machine = this.machines.get(userId);
    return machine ? machine.state : null;
  }

  private handleStateChange(userId: string, state: any) {
    // Xử lý khi state thay đổi
    // Ví dụ: lưu vào database, gửi thông báo, etc.
  }
}
```

## Hướng dẫn sử dụng

### 1. Định nghĩa máy trạng thái

Đầu tiên, định nghĩa máy trạng thái với các trạng thái, sự kiện và chuyển đổi:

```typescript
const myMachine = Machine({
  id: 'myProcess',
  initial: 'step1',
  context: {
    // Dữ liệu ban đầu
    data: null,
  },
  states: {
    step1: {
      on: {
        NEXT: {
          target: 'step2',
          actions: assign({
            data: (context, event) => ({ ...context.data, ...event.data }),
          }),
        },
      },
    },
    step2: {
      on: {
        NEXT: 'step3',
        BACK: 'step1',
      },
    },
    step3: {
      type: 'final',
    },
  },
});
```

### 2. Tạo và khởi động máy trạng thái

```typescript
// Trong service
const service = interpret(myMachine)
  .onTransition((state) => {
    console.log('Current state:', state.value);
  })
  .start();
```

### 3. Gửi sự kiện để chuyển đổi trạng thái

```typescript
// Chuyển từ step1 sang step2
service.send({ type: 'NEXT', data: { name: 'John Doe' } });

// Chuyển từ step2 sang step1
service.send('BACK');
```

### 4. Truy cập trạng thái và ngữ cảnh hiện tại

```typescript
const currentState = service.state.value; // Ví dụ: 'step2'
const currentContext = service.state.context; // Ví dụ: { data: { name: 'John Doe' } }
```

## Tích hợp với NestJS

### 1. Tạo service quản lý máy trạng thái

```typescript
import { Injectable } from '@nestjs/common';
import { Machine, interpret, assign } from 'xstate';

@Injectable()
export class ProcessStateMachineService {
  private machines = new Map<string, any>();

  private processMachine = Machine({
    // Định nghĩa máy trạng thái
  });

  startProcess(processId: string, initialData: any) {
    const machine = interpret(this.processMachine)
      .onTransition((state) => {
        this.handleStateChange(processId, state);
      })
      .start();

    this.machines.set(processId, machine);
    return machine;
  }

  sendEvent(processId: string, event: string, payload?: any) {
    const machine = this.machines.get(processId);
    if (machine) {
      machine.send({ type: event, ...payload });
    }
  }

  getCurrentState(processId: string) {
    const machine = this.machines.get(processId);
    return machine ? machine.state : null;
  }

  private handleStateChange(processId: string, state: any) {
    // Xử lý khi state thay đổi
    // Ví dụ: lưu vào database, gửi thông báo, etc.
  }
}
```

### 2. Đăng ký service trong module

```typescript
import { Module } from '@nestjs/common';
import { ProcessStateMachineService } from './process-state-machine.service';

@Module({
  providers: [ProcessStateMachineService],
  exports: [ProcessStateMachineService],
})
export class ProcessModule {}
```

### 3. Sử dụng trong controller

```typescript
import { Controller, Post, Body, Param } from '@nestjs/common';
import { ProcessStateMachineService } from './process-state-machine.service';

@Controller('processes')
export class ProcessController {
  constructor(private readonly stateMachineService: ProcessStateMachineService) {}

  @Post(':id/start')
  startProcess(@Param('id') id: string, @Body() initialData: any) {
    this.stateMachineService.startProcess(id, initialData);
    return { success: true, message: 'Process started' };
  }

  @Post(':id/events')
  sendEvent(
    @Param('id') id: string,
    @Body() eventData: { type: string; [key: string]: any },
  ) {
    const { type, ...payload } = eventData;
    this.stateMachineService.sendEvent(id, type, payload);
    return { success: true, message: 'Event sent' };
  }

  @Get(':id/state')
  getState(@Param('id') id: string) {
    const state = this.stateMachineService.getCurrentState(id);
    return {
      success: true,
      data: {
        state: state ? state.value : null,
        context: state ? state.context : null,
      },
    };
  }
}
```

## Lưu trữ trạng thái

Để duy trì trạng thái giữa các lần khởi động server, bạn cần lưu trạng thái vào cơ sở dữ liệu:

### 1. Tạo entity để lưu trạng thái

```typescript
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity('process_states')
export class ProcessState {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  processId: string;

  @Column()
  currentState: string;

  @Column('json')
  context: any;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt: Date;
}
```

### 2. Cập nhật service để lưu và khôi phục trạng thái

```typescript
@Injectable()
export class ProcessStateMachineService {
  constructor(
    @InjectRepository(ProcessState)
    private processStateRepository: Repository<ProcessState>,
  ) {}

  async startProcess(processId: string, initialData: any) {
    // Kiểm tra xem có trạng thái đã lưu không
    const savedState = await this.processStateRepository.findOne({
      where: { processId },
    });

    let machine;
    if (savedState) {
      // Khôi phục trạng thái
      machine = interpret(this.processMachine)
        .onTransition((state) => {
          this.handleStateChange(processId, state);
        })
        .start(savedState.currentState);

      // Khôi phục context
      machine.state.context = savedState.context;
    } else {
      // Tạo mới
      machine = interpret(this.processMachine)
        .onTransition((state) => {
          this.handleStateChange(processId, state);
        })
        .start();

      // Lưu trạng thái ban đầu
      await this.processStateRepository.save({
        processId,
        currentState: machine.state.value,
        context: initialData,
      });
    }

    this.machines.set(processId, machine);
    return machine;
  }

  private async handleStateChange(processId: string, state: any) {
    // Lưu trạng thái mới vào database
    await this.processStateRepository.update(
      { processId },
      {
        currentState: state.value,
        context: state.context,
        updatedAt: new Date(),
      },
    );

    // Xử lý logic khác khi trạng thái thay đổi
  }
}
```

## Xử lý trạng thái song song

XState hỗ trợ các trạng thái song song (parallel states) cho phép nhiều trạng thái con hoạt động đồng thời:

```typescript
const parallelMachine = Machine({
  id: 'parallel',
  type: 'parallel',
  states: {
    upload: {
      initial: 'idle',
      states: {
        idle: {
          on: { START_UPLOAD: 'uploading' }
        },
        uploading: {
          on: {
            UPLOAD_COMPLETE: 'success',
            UPLOAD_ERROR: 'failure'
          }
        },
        success: { type: 'final' },
        failure: {}
      }
    },
    validation: {
      initial: 'idle',
      states: {
        idle: {
          on: { START_VALIDATION: 'validating' }
        },
        validating: {
          on: {
            VALIDATION_COMPLETE: 'success',
            VALIDATION_ERROR: 'failure'
          }
        },
        success: { type: 'final' },
        failure: {}
      }
    }
  }
});
```

## Xử lý điều kiện chuyển đổi

Bạn có thể thêm điều kiện (guards) để quyết định có thực hiện chuyển đổi hay không:

```typescript
const conditionalMachine = Machine({
  // ...
  states: {
    review: {
      on: {
        SUBMIT: [
          {
            target: 'approved',
            cond: (context) => context.score >= 70,
          },
          {
            target: 'rejected',
            cond: (context) => context.score < 70,
          },
        ],
      },
    },
    // ...
  },
});
```

## Xử lý hoạt động bất đồng bộ

Để xử lý các hoạt động bất đồng bộ như gọi API, bạn có thể sử dụng các trạng thái đang chờ (pending states):

```typescript
const asyncMachine = Machine({
  // ...
  states: {
    idle: {
      on: { FETCH: 'loading' },
    },
    loading: {
      invoke: {
        src: (context) => fetchData(context.id),
        onDone: {
          target: 'success',
          actions: assign({
            data: (_, event) => event.data,
          }),
        },
        onError: {
          target: 'failure',
          actions: assign({
            error: (_, event) => event.data,
          }),
        },
      },
    },
    success: {},
    failure: {
      on: { RETRY: 'loading' },
    },
  },
});
```

## Kết luận

XState và @xstate/fsm cung cấp một cách mạnh mẽ để quản lý các quy trình nghiệp vụ phức tạp trong ứng dụng NestJS. Bằng cách sử dụng máy trạng thái, bạn có thể:

1. Mô hình hóa rõ ràng các trạng thái và chuyển đổi
2. Quản lý dữ liệu ngữ cảnh một cách nhất quán
3. Xử lý các trường hợp đặc biệt và lỗi một cách rõ ràng
4. Dễ dàng mở rộng và bảo trì quy trình

Việc tích hợp với NestJS cho phép bạn tận dụng các tính năng của cả hai framework để xây dựng các quy trình nghiệp vụ mạnh mẽ và dễ bảo trì.

## Tài liệu tham khảo

- [XState Documentation](https://xstate.js.org/docs/)
- [XState FSM Documentation](https://xstate.js.org/docs/packages/xstate-fsm/)
- [NestJS Documentation](https://docs.nestjs.com/)
