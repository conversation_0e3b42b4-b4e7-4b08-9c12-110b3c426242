import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { AffiliateAccountRepository } from '@modules/affiliate/repositories/affiliate-account.repository';
import { AffiliateClickRepository } from '@modules/affiliate/repositories/affiliate-click.repository';
import { AffiliateReferralLinkDto } from '../dto';
import { AFFILIATE_ERROR_CODES } from '@modules/affiliate/errors';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class AffiliateReferralLinkService {
  private readonly logger = new Logger(AffiliateReferralLinkService.name);
  private readonly baseUrl = 'http://app.redai.vn';

  constructor(
    private readonly affiliateAccountRepository: AffiliateAccountRepository,
    private readonly affiliateClickRepository: AffiliateClickRepository,
  ) {}

  /**
   * Lấy thông tin link giới thiệu
   * @param userId ID của người dùng
   * @returns Thông tin link giới thiệu
   */
  @Transactional()
  async getReferralLink(userId: number): Promise<AffiliateReferralLinkDto> {
    try {
      // Lấy tài khoản affiliate của người dùng
      const affiliateAccount =
        await this.affiliateAccountRepository.findByUserId(userId);

      if (!affiliateAccount) {
        throw new AppException(
          AFFILIATE_ERROR_CODES.ACCOUNT_NOT_FOUND,
          'Không tìm thấy tài khoản affiliate',
        );
      }

      // Tạo link giới thiệu
      const referralLink = `${this.baseUrl}?ref=${affiliateAccount.id}`;

      // Lấy thống kê lượt click
      const totalClicks = await this.affiliateClickRepository.countByAffiliateAccountId(
        affiliateAccount.id,
      );

      // Lấy thống kê lượt click trong 30 ngày gần đây
      const thirtyDaysAgo = Math.floor(Date.now() / 1000) - 30 * 24 * 60 * 60;
      const recentClicks = await this.affiliateClickRepository.countByAffiliateAccountIdAndTimeRange(
        affiliateAccount.id,
        thirtyDaysAgo,
        Math.floor(Date.now() / 1000),
      );

      // Xử lý dữ liệu trả về
      return {
        referralLink,
        affiliateAccountId: affiliateAccount.id,
        totalClicks,
        recentClicks,
      };
    } catch (error) {
      this.logger.error(
        `Error getting referral link: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AFFILIATE_ERROR_CODES.REFERRAL_LINK_CREATION_FAILED,
        'Lỗi khi lấy thông tin link giới thiệu',
      );
    }
  }
}
