import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { JwtService } from '@nestjs/jwt';
import { AffiliateUserModule } from '../affiliate-user.module';
import { 
  AffiliateAccountRepository,
  AffiliateClickRepository,
  AffiliateCustomerOrderRepository,
  AffiliateWithdrawHistoryRepository,
  AffiliateRankRepository
} from '@modules/affiliate/repositories';
import { UserRepository } from '@modules/user/repositories';
import { PointPurchaseTransactionRepository } from '@modules/r-point/repositories';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';

describe('Affiliate Integration Tests', () => {
  let app: INestApplication;
  let jwtService: JwtService;

  // Mock repositories
  const mockAffiliateAccountRepository = {
    findByUserId: jest.fn()
  };

  const mockAffiliateClickRepository = {
    countByAffiliateAccountIdAndTimeRange: jest.fn()
  };

  const mockAffiliateCustomerOrderRepository = {
    countByAffiliateAccountIdAndTimeRange: jest.fn(),
    calculateRevenueByAffiliateAccountIdAndTimeRange: jest.fn(),
    findWithPagination: jest.fn()
  };

  const mockAffiliateWithdrawHistoryRepository = {
    findWithPagination: jest.fn()
  };

  const mockAffiliateRankRepository = {
    findById: jest.fn()
  };

  const mockUserRepository = {
    findById: jest.fn(),
    countByAffiliateAccountIdAndTimeRange: jest.fn(),
    findAffiliateCustomersWithPagination: jest.fn()
  };

  const mockPointPurchaseTransactionRepository = {
    findById: jest.fn()
  };

  // Mock JWT guard
  const mockJwtUserGuard = {
    canActivate: jest.fn().mockImplementation((context) => {
      const req = context.switchToHttp().getRequest();
      req.user = { id: 1, email: '<EMAIL>', role: 'user' };
      return true;
    })
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AffiliateUserModule],
      providers: [
        JwtService
      ]
    })
      .overrideProvider(AffiliateAccountRepository)
      .useValue(mockAffiliateAccountRepository)
      .overrideProvider(AffiliateClickRepository)
      .useValue(mockAffiliateClickRepository)
      .overrideProvider(AffiliateCustomerOrderRepository)
      .useValue(mockAffiliateCustomerOrderRepository)
      .overrideProvider(AffiliateWithdrawHistoryRepository)
      .useValue(mockAffiliateWithdrawHistoryRepository)
      .overrideProvider(AffiliateRankRepository)
      .useValue(mockAffiliateRankRepository)
      .overrideProvider(UserRepository)
      .useValue(mockUserRepository)
      .overrideProvider(PointPurchaseTransactionRepository)
      .useValue(mockPointPurchaseTransactionRepository)
      .overrideGuard(JwtUserGuard)
      .useValue(mockJwtUserGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    jwtService = moduleFixture.get<JwtService>(JwtService);
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/user/affiliate/statistics (GET)', () => {
    it('should return statistics', async () => {
      // Arrange
      const mockAffiliateAccount = {
        id: 123,
        availableBalance: 1500000
      };

      mockAffiliateAccountRepository.findByUserId.mockResolvedValue(mockAffiliateAccount);
      mockAffiliateClickRepository.countByAffiliateAccountIdAndTimeRange.mockResolvedValue(1250);
      mockUserRepository.countByAffiliateAccountIdAndTimeRange.mockResolvedValue(45);
      mockAffiliateCustomerOrderRepository.countByAffiliateAccountIdAndTimeRange.mockResolvedValue(78);
      mockAffiliateCustomerOrderRepository.calculateRevenueByAffiliateAccountIdAndTimeRange.mockResolvedValue(7800000);

      // Create a valid JWT token
      const token = jwtService.sign({ id: 1, email: '<EMAIL>', role: 'user' });

      // Act & Assert
      return request(app.getHttpServer())
        .get('/user/affiliate/statistics')
        .set('Authorization', `Bearer ${token}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data).toBeDefined();
          expect(res.body.data.walletBalance).toBe(1500000);
          expect(res.body.data.clickCount).toBe(1250);
          expect(res.body.data.customerCount).toBe(45);
          expect(res.body.data.orderCount).toBe(78);
          expect(res.body.data.revenue).toBe(7800000);
          expect(res.body.message).toBe('Lấy thông tin thống kê thành công');
        });
    });

    it('should return 404 when affiliate account does not exist', async () => {
      // Arrange
      mockAffiliateAccountRepository.findByUserId.mockResolvedValue(null);

      // Create a valid JWT token
      const token = jwtService.sign({ id: 1, email: '<EMAIL>', role: 'user' });

      // Act & Assert
      return request(app.getHttpServer())
        .get('/user/affiliate/statistics')
        .set('Authorization', `Bearer ${token}`)
        .expect(404)
        .expect((res) => {
          expect(res.body.success).toBe(false);
          expect(res.body.message).toBe('Không tìm thấy tài khoản affiliate');
        });
    });
  });

  describe('/user/affiliate/account (GET)', () => {
    it('should return account information', async () => {
      // Arrange
      const mockAffiliateAccount = {
        id: 123,
        status: 'ACTIVE',
        createdAt: **********,
        rankId: 2
      };

      const mockUser = {
        id: 1,
        fullName: 'Nguyễn Văn A'
      };

      const mockRank = {
        id: 2,
        rankName: 'Silver',
        rankBadge: 'silver_badge.png',
        commission: 5.5,
        minCondition: 10,
        maxCondition: 49
      };

      mockAffiliateAccountRepository.findByUserId.mockResolvedValue(mockAffiliateAccount);
      mockUserRepository.findById.mockResolvedValue(mockUser);
      mockAffiliateRankRepository.findById.mockResolvedValue(mockRank);

      // Create a valid JWT token
      const token = jwtService.sign({ id: 1, email: '<EMAIL>', role: 'user' });

      // Act & Assert
      return request(app.getHttpServer())
        .get('/user/affiliate/account')
        .set('Authorization', `Bearer ${token}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data).toBeDefined();
          expect(res.body.data.accountInfo.id).toBe(123);
          expect(res.body.data.accountInfo.partnerName).toBe('Nguyễn Văn A');
          expect(res.body.data.rankInfo.rankName).toBe('Silver');
          expect(res.body.data.referralCode).toBeDefined();
          expect(res.body.data.referralLink).toBeDefined();
          expect(res.body.message).toBe('Lấy thông tin tài khoản affiliate thành công');
        });
    });
  });

  describe('/user/affiliate/orders (GET)', () => {
    it('should return paginated orders', async () => {
      // Arrange
      const mockAffiliateAccount = {
        id: 123
      };

      const mockOrder = {
        orderId: 456,
        commission: 5
      };

      const mockTransaction = {
        userId: 789,
        amount: 1500000,
        createdAt: **********,
        status: 'COMPLETED'
      };

      const mockCustomer = {
        id: 789,
        fullName: 'Trần Thị B',
        email: '<EMAIL>',
        phoneNumber: '**********'
      };

      const mockPaginatedOrders = {
        items: [mockOrder],
        meta: {
          totalItems: 78,
          itemCount: 1,
          itemsPerPage: 10,
          totalPages: 8,
          currentPage: 1
        }
      };

      mockAffiliateAccountRepository.findByUserId.mockResolvedValue(mockAffiliateAccount);
      mockAffiliateCustomerOrderRepository.findWithPagination.mockResolvedValue(mockPaginatedOrders);
      mockPointPurchaseTransactionRepository.findById.mockResolvedValue(mockTransaction);
      mockUserRepository.findById.mockResolvedValue(mockCustomer);

      // Create a valid JWT token
      const token = jwtService.sign({ id: 1, email: '<EMAIL>', role: 'user' });

      // Act & Assert
      return request(app.getHttpServer())
        .get('/user/affiliate/orders')
        .set('Authorization', `Bearer ${token}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data).toBeDefined();
          expect(res.body.data.items).toHaveLength(1);
          expect(res.body.data.items[0].orderId).toBe('456');
          expect(res.body.data.items[0].customer.fullName).toBe('Trần Thị B');
          expect(res.body.data.meta.totalItems).toBe(78);
          expect(res.body.message).toBe('Lấy danh sách đơn hàng affiliate thành công');
        });
    });
  });
});
