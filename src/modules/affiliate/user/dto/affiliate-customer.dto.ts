import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin khách hàng affiliate
 */
export class AffiliateCustomerDto {
  @ApiProperty({
    description: 'ID của khách hàng',
    example: 456
  })
  id: number;

  @ApiProperty({
    description: 'Họ tên khách hàng',
    example: 'Trần Thị B'
  })
  fullName: string;

  @ApiProperty({
    description: 'Email khách hàng',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({
    description: 'Số điện thoại khách hàng',
    example: '0987654321'
  })
  phoneNumber: string;

  @ApiProperty({
    description: 'Thời gian tạo tà<PERSON> kho<PERSON>n (Unix timestamp)',
    example: 1672531200
  })
  createdAt: number;

  @ApiProperty({
    description: 'Số đơn hàng đã đặt',
    example: 3
  })
  orderCount: number;

  @ApiProperty({
    description: 'Tổng số tiền đã chi tiêu',
    example: 4500000
  })
  totalSpent: number;
}
