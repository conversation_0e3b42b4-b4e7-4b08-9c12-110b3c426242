/**
 * Interface chuyển đổi từ các class DTO trong package com.redon_agency.chatbot.common.feign.sms.dto
 */

/**
 * Chuyển đổi từ AccessTokenRequest.java
 */
export interface AccessTokenRequest {
  grant_type: string;
  client_id: string;
  client_secret: string;
  scope: string;
  session_id: string;
}

/**
 * Chuyển đổi từ AccessTokenResponse.java
 */
export interface AccessTokenResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
  scope: string;
  error?: number;
  error_description?: string;
}

/**
 * Chuyển đổi từ AdsRequest.java
 */
export interface AdsRequest {
  access_token?: string;
  session_id?: string;
  CampaignCode: string;
  PhoneList: string; // Danh sách số điện thoại, phân cách bằng dấu phẩy
}

/**
 * Chuyển đổi từ AdsResponse.java
 */
export interface AdsResponse {
  errorid: number;
  errordes: string;
  requestid: string;
}

/**
 * Chuyển đổi từ CampaignRequest.java
 */
export interface CampaignRequest {
  access_token?: string;
  session_id?: string;
  CampaignName: string; // Tên campaign không được trùng nhau
  BrandName: string;
  Message: string; // Nội dung tin nhắn
  ScheduleTime: string; // Định dạng "yyyy-MM-dd HH:mm"
  Quota: number; // Hạn mức gửi tin của campaign
}

/**
 * Chuyển đổi từ CampaignResponse.java
 */
export interface CampaignResponse {
  errorid: number;
  errordes: string;
  campaigncode: string;
}

/**
 * Chuyển đổi từ CancelAdsRequest.java
 */
export interface CancelAdsRequest {
  access_token?: string;
  session_id?: string;
  RequestCode: string;
}

/**
 * Chuyển đổi từ CancelAdsResponse.java
 */
export interface CancelAdsResponse {
  errorid: number;
  errordes: string;
}

/**
 * Chuyển đổi từ StatusDetails.java
 */
export interface StatusDetails {
  TelcoCode: string;
  Count: number;
  Price: number;
  Amount: number;
}

/**
 * Chuyển đổi từ DetailsCampaignReq.java
 */
export interface DetailsCampaignReq {
  access_token?: string;
  session_id?: string;
  CampaignCode: string;
}

/**
 * Chuyển đổi từ DetailsCampaignRes.java
 */
export interface DetailsCampaignRes {
  errorid: number;
  errordes: string;
  campaigncode: string;
  brandname: string;
  message: string;
  scheduletime: string;
  quota: number;
  status: string;
}

/**
 * Chuyển đổi từ DetailsStatusCampaignReq.java
 */
export interface DetailsStatusCampaignReq {
  access_token?: string;
  session_id?: string;
  CampaignCode: string;
}

/**
 * Chuyển đổi từ DlrAdsRecheckReq.java
 */
export interface DlrAdsRecheckReq {
  access_token: string;
  session_id: string;
  RequestCode: string;
}

/**
 * Chuyển đổi từ CampaignDlr.java
 */
export interface CampaignDlr {
  errorid: number;
  errordes: string;
}

/**
 * Chuyển đổi từ DlrAdsRecheckRes.java
 */
export interface DlrAdsRecheckRes {
  errorid: number;
  errordes: string;
}

/**
 * Chuyển đổi từ OtpRequest.java
 */
export interface OtpRequest {
  access_token?: string;
  session_id?: string;
  BrandName: string;
  Phone: string;
  Message: string;
  Quota: number; // Hạn mức gửi tin của campaign
}

/**
 * Chuyển đổi từ OtpResponse.java
 */
export interface OtpResponse {
  errorid: number;
  errordes: string;
  requestid: string;
  price: number;
  amount: number;
} 