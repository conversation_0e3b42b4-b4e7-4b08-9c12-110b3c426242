import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho yêu cầu ký hợp đồng
 */
export class SignContractDto {
  /**
   * Dữ liệu chữ ký dạng Base64
   */
  @ApiProperty({
    description: 'Dữ liệu chữ ký dạng Base64',
    example: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAZAAAA...',
    required: true,
  })
  @IsNotEmpty({ message: 'Dữ liệu chữ ký không được để trống' })
  @IsString({ message: 'Dữ liệu chữ ký phải là chuỗi' })
  signatureData: string;
}
