import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { RuleContract } from './entities/rule-contract.entity';
import { RuleContractRepository, UserRepository } from './repositories';
import { RuleContractAdminService } from './admin/services';
import { RuleContractUserService } from './user/services';
import { RuleContractAdminController } from './admin/controllers';
import { RuleContractUserController } from './user/controllers';
import { UserModule } from '@modules/user/user.module';
import { ServicesModule } from '@shared/services/services.module';
import { ContractHelperService } from './user/services/contract-helper.service';
import { SystemConfigurationModule } from '@modules/system-configuration/system-configuration.module';
import { EmailModule } from '@modules/email/email.module';
import { RuleContractStateService } from './state-machine/rule-contract-state.service';
import { RuleContractActionsService } from './state-machine/rule-contract-actions.service';

/**
 * <PERSON>dule quản lý hợp đồng nguyên tắc
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([RuleContract]),
    UserModule,
    ServicesModule,
    SystemConfigurationModule,
    EmailModule,
  ],
  controllers: [
    RuleContractAdminController,
    RuleContractUserController,
  ],
  providers: [
    RuleContractRepository,
    UserRepository, // Thêm UserRepository từ module rule-contract
    RuleContractAdminService,
    RuleContractUserService,
    ContractHelperService,
    RuleContractStateService,
    RuleContractActionsService,
  ],
  exports: [
    RuleContractRepository,
    UserRepository, // Thêm UserRepository vào exports
    RuleContractAdminService,
    RuleContractUserService,
    ContractHelperService,
    RuleContractStateService,
    RuleContractActionsService,
  ],
})
export class RuleContractModule {}
