import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber } from 'class-validator';

/**
 * DTO cho phê duyệt hợp đồng nguyên tắc
 */
export class ApproveContractDto {
  /**
   * ID của admin phê duyệt
   */
  @ApiProperty({
    description: 'ID của admin phê duyệt',
    example: 1,
    type: Number,
  })
  @IsNotEmpty({ message: 'ID admin không được để trống' })
  @IsNumber({}, { message: 'ID admin phải là số' })
  adminId: number;
}
