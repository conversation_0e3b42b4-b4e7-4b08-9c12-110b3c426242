import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';

/**
 * DTO cho từ chối hợp đồng nguyên tắc
 */
export class RejectContractDto {
  /**
   * ID của admin từ chối
   */
  @ApiProperty({
    description: 'ID của admin từ chối',
    example: 1,
    type: Number,
  })
  @IsNotEmpty({ message: 'ID admin không được để trống' })
  @IsNumber({}, { message: 'ID admin phải là số' })
  adminId: number;

  /**
   * Lý do từ chối
   */
  @ApiProperty({
    description: 'Lý do từ chối',
    example: 'Thông tin không chính xác',
    type: String,
  })
  @IsNotEmpty({ message: 'Lý do từ chối không được để trống' })
  @IsString({ message: 'Lý do từ chối phải là chuỗi' })
  rejectionReason: string;
}
