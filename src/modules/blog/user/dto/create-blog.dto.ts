import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, Min } from 'class-validator';
import { BlogStatusEnum } from '../../enums';

export class CreateBlogDto {
  @ApiProperty({
    description: 'Tiêu đề bài viết',
    example: 'Tiêu đề bài viết',
  })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Mô tả ngắn về bài viết',
    example: 'Mô tả ngắn về bài viết',
  })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Loại nội dung',
    example: 'text/html',
  })
  @IsNotEmpty()
  @IsString()
  content_media_type: string;

  @ApiProperty({
    description: 'Loại thumbnail',
    example: 'image/jpeg',
  })
  @IsNotEmpty()
  @IsString()
  thumbnail_media_type: string;

  @ApiProperty({
    description: 'Số point',
    example: 100,
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  point: number;

  @ApiProperty({
    description: 'Tags',
    example: ['tag1', 'tag2'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  tags: string[];

  @ApiProperty({
    description: 'Trạng thái',
    example: BlogStatusEnum.DRAFT,
    enum: BlogStatusEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(BlogStatusEnum)
  status?: BlogStatusEnum;
}

export class UpdateBlogMediaDto {
  @ApiProperty({
    description: 'Loại media (content hoặc thumbnail)',
    example: 'content',
    enum: ['content', 'thumbnail'],
  })
  @IsNotEmpty()
  @IsString()
  media_type: 'content' | 'thumbnail';

  @ApiProperty({
    description: 'Loại nội dung media',
    example: 'text/html',
  })
  @IsNotEmpty()
  @IsString()
  media_content_type: string;
}
