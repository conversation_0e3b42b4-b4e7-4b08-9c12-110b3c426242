import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BlogUserService } from '@modules/blog/user/services';
import { Blog } from '../../entities';
import { BlogRepository } from '../../repositories';
import { BlogStatusEnum, AuthorTypeEnum } from '../../enums';
import { GetBlogsDto, CreateBlogDto, UpdateBlogMediaDto } from '../../dto';
import { NotFoundException } from '@nestjs/common';
import { S3Service } from '@/shared/services/s3.service';
import { MediaTypeEnum } from '@modules/blog/dto';
import { User } from '@modules/user/entities';
import { SqlHelper } from '@/common/helpers/sql.helper';
import { BlogUserException, BlogUserErrorCode } from '@modules/blog/exceptions/blog-user.exception';

// Mock all external dependencies
jest.mock('@/config', () => ({
  ConfigService: jest.fn().mockImplementation(() => ({
    get: jest.fn().mockImplementation((key) => {
      // Return mock values for any config keys that might be used
      const mockConfig = {
        DB_HOST: 'localhost',
        DB_USERNAME: 'test',
        DB_PASSWORD: 'test',
        DB_DATABASE: 'test',
        CF_R2_ACCESS_KEY: 'test',
        CF_R2_SECRET_KEY: 'test',
        CF_R2_ENDPOINT: 'test',
        CF_BUCKET_NAME: 'test',
        OPENAI_API_KEY: 'test',
        CDN_URL: 'test',
        CDN_SECRET_KEY: 'test',
        JWT_SECRET: 'test'
      };
      return mockConfig[key] || null;
    })
  }))
}), { virtual: true });

// Mock S3Service
jest.mock('@/shared/services/s3.service', () => ({
  S3Service: jest.fn().mockImplementation(() => ({
    createPresignedWithID: jest.fn().mockResolvedValue('mock-presigned-url')
  }))
}), { virtual: true });

// We'll mock the SqlHelper directly in the test setup instead of using jest.mock

// Mock data
const mockBlog = {
  id: 1,
  title: 'Test Blog Title',
  content: 'https://cdn.example.com/blogs/content-123.html',
  point: 100,
  viewCount: 1000,
  thumbnailUrl: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
  tags: ['nestjs', 'typescript', 'backend'],
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
  userId: 1,
  employeeId: null,
  employeeModerator: null,
  authorType: AuthorTypeEnum.USER,
  status: BlogStatusEnum.APPROVED,
  enable: true,
  like: 500
};

const mockUser = {
  id: 1,
  fullName: 'Nguyễn Văn A',
  email: '<EMAIL>',
  phoneNumber: '**********',
  isActive: true,
  avatar: 'https://cdn.example.com/avatars/user1.jpg',
};

describe('BlogUserService', () => {
  let service: BlogUserService;
  let blogRepository: Repository<Blog>;
  let userRepository: Repository<User>;
  let customBlogRepository: BlogRepository;
  let s3Service: S3Service;
  let sqlHelper: any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BlogUserService,
        {
          provide: getRepositoryToken(Blog),
          useValue: {
            createQueryBuilder: jest.fn(() => ({
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              orderBy: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              take: jest.fn().mockReturnThis(),
              getManyAndCount: jest.fn().mockResolvedValue([[mockBlog], 1]),
            })),
            findOne: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: BlogRepository,
          useValue: {
            incrementViewCount: jest.fn(),
          },
        },
        {
          provide: S3Service,
          useValue: {
            createPresignedWithID: jest.fn(),
          },
        },
        {
          provide: SqlHelper,
          useValue: {
            exists: jest.fn().mockResolvedValue(true),
            insert: jest.fn().mockResolvedValue({ id: 1 }),
            update: jest.fn().mockResolvedValue(true),
            select: jest.fn().mockResolvedValue([mockBlog]),
            getPaginatedData: jest.fn().mockResolvedValue({
              items: [mockBlog],
              meta: {
                totalItems: 1,
                itemCount: 1,
                itemsPerPage: 10,
                totalPages: 1,
                currentPage: 1
              }
            })
          },
        },
      ],
    }).compile();

    service = module.get<BlogUserService>(BlogUserService);
    blogRepository = module.get<Repository<Blog>>(getRepositoryToken(Blog));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    customBlogRepository = module.get<BlogRepository>(BlogRepository);
    s3Service = module.get<S3Service>(S3Service);
    sqlHelper = module.get(SqlHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return paginated blogs', async () => {
      // Arrange
      const dto: GetBlogsDto = { page: 1, limit: 10 };

      // Mock the queryBuilder
      const queryBuilderMock = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockBlog], 1]),
      };

      jest.spyOn(blogRepository, 'createQueryBuilder').mockReturnValue(queryBuilderMock as any);

      // Mock the user repository to return user data
      jest.spyOn(userRepository, 'find').mockResolvedValue([mockUser as User]);

      // Act
      const result = await service.findAll(dto);

      // Assert
      expect(sqlHelper.getPaginatedData).toHaveBeenCalled();
      expect(result.content.length).toBe(1);
      expect(result.totalItems).toBe(1);
    });

    it('should filter by author type', async () => {
      // Arrange
      const dto: GetBlogsDto = {
        page: 1,
        limit: 10,
        author_type: AuthorTypeEnum.USER
      };

      // Mock the queryBuilder
      const queryBuilderMock = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockBlog], 1]),
      };

      jest.spyOn(blogRepository, 'createQueryBuilder').mockReturnValue(queryBuilderMock as any);

      // Mock the user repository to return user data
      jest.spyOn(userRepository, 'find').mockResolvedValue([mockUser as User]);

      // Act
      const result = await service.findAll(dto);

      // Assert
      expect(sqlHelper.getPaginatedData).toHaveBeenCalled();
      expect(result.content.length).toBe(1);
      expect(result.totalItems).toBe(1);
    });
  });

  describe('findMyBlogs', () => {
    it('should return user\'s blogs', async () => {
      // Arrange
      const userId = 1;
      const dto: GetBlogsDto = { page: 1, limit: 10 };

      // Mock the queryBuilder
      const queryBuilderMock = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockBlog], 1]),
      };

      jest.spyOn(blogRepository, 'createQueryBuilder').mockReturnValue(queryBuilderMock as any);

      // Mock the user repository to return user data
      jest.spyOn(userRepository, 'find').mockResolvedValue([mockUser as User]);

      // Act
      const result = await service.findMyBlogs(userId, dto);

      // Assert
      expect(sqlHelper.getPaginatedData).toHaveBeenCalled();
      expect(result.content.length).toBe(1);
      expect(result.totalItems).toBe(1);
    });

    it('should filter by status for user blogs', async () => {
      // Arrange
      const userId = 1;
      const dto: GetBlogsDto = {
        page: 1,
        limit: 10,
        status: BlogStatusEnum.DRAFT
      };

      // Mock the queryBuilder
      const queryBuilderMock = {
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[{...mockBlog, status: BlogStatusEnum.DRAFT}], 1]),
      };

      jest.spyOn(blogRepository, 'createQueryBuilder').mockReturnValue(queryBuilderMock as any);

      // Mock the user repository to return user data
      jest.spyOn(userRepository, 'find').mockResolvedValue([mockUser as User]);

      // Act
      const result = await service.findMyBlogs(userId, dto);

      // Assert
      expect(sqlHelper.getPaginatedData).toHaveBeenCalled();
      expect(result.content.length).toBe(1);
      expect(result.totalItems).toBe(1);
    });
  });

  describe('findOne', () => {
    it('should return a blog by id with content for the author', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1; // Same as blog author

      jest.spyOn(blogRepository, 'findOne').mockResolvedValue(mockBlog as unknown as Blog);
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(mockUser as User);
      jest.spyOn(customBlogRepository, 'incrementViewCount').mockResolvedValue(undefined);
      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(true); // User has purchased the blog

      // Act
      const result = await service.findOne(blogId, userId);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(customBlogRepository.incrementViewCount).toHaveBeenCalledWith(blogId);
      expect(result).toHaveProperty('id', mockBlog.id);
      expect(result).toHaveProperty('title', mockBlog.title);
      expect(result).toHaveProperty('content', expect.any(String)); // Content should be present
      expect(result.isPurchased).toBe(true);
    });

    it('should return a blog by id with content for a user who purchased it', async () => {
      // Arrange
      const blogId = 1;
      const userId = 2; // Different from blog author

      jest.spyOn(blogRepository, 'findOne').mockResolvedValue(mockBlog as unknown as Blog);
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(mockUser as User);
      jest.spyOn(customBlogRepository, 'incrementViewCount').mockResolvedValue(undefined);
      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(true); // User has purchased the blog

      // Act
      const result = await service.findOne(blogId, userId);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(customBlogRepository.incrementViewCount).toHaveBeenCalledWith(blogId);
      expect(result).toHaveProperty('id', mockBlog.id);
      expect(result).toHaveProperty('title', mockBlog.title);
      expect(result).toHaveProperty('content', expect.any(String)); // Content should be present
      expect(result.isPurchased).toBe(true);
    });

    it('should return a blog by id without content for a user who has not purchased it', async () => {
      // Arrange
      const blogId = 1;
      const userId = 2; // Different from blog author

      jest.spyOn(blogRepository, 'findOne').mockResolvedValue(mockBlog as unknown as Blog);
      jest.spyOn(userRepository, 'findOne').mockResolvedValue(mockUser as User);
      jest.spyOn(customBlogRepository, 'incrementViewCount').mockResolvedValue(undefined);
      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(false); // User has NOT purchased the blog

      // Act
      const result = await service.findOne(blogId, userId);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(customBlogRepository.incrementViewCount).toHaveBeenCalledWith(blogId);
      expect(result).toHaveProperty('id', mockBlog.id);
      expect(result).toHaveProperty('title', mockBlog.title);
      expect(result.content).toBeNull(); // Content should be null
      expect(result.isPurchased).toBe(false);
    });

    it('should throw NotFoundException when blog not found', async () => {
      // Arrange
      const blogId = 999;

      jest.spyOn(sqlHelper, 'select').mockResolvedValue([]);

      // Act & Assert
      await expect(service.findOne(blogId)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_NOT_FOUND)
      );
    });
  });

  describe('create', () => {
    it('should create a new blog and return upload URLs', async () => {
      // Arrange
      const userId = 1;
      const dto: CreateBlogDto = {
        title: 'New Blog',
        description: 'Blog description',
        contentMediaType: 'text/html',
        thumbnailMediaType: 'image/jpeg',
        point: 100,
        tags: ['nestjs', 'typescript'],
        authorType: AuthorTypeEnum.USER,
      };

      // Không cần tạo newBlog vì chúng ta sử dụng SqlHelper.insert

      // Mock SqlHelper
      jest.spyOn(sqlHelper, 'insert').mockResolvedValue({ id: 2 });
      jest.spyOn(sqlHelper, 'update').mockResolvedValue(true);

      // Mock S3 service
      jest.spyOn(s3Service, 'createPresignedWithID')
        .mockResolvedValueOnce('content-upload-url')
        .mockResolvedValueOnce('thumbnail-upload-url');

      // Act
      const result = await service.create(userId, dto);

      // Assert
      expect(sqlHelper.insert).toHaveBeenCalled();
      expect(s3Service.createPresignedWithID).toHaveBeenCalledTimes(2);
      expect(sqlHelper.update).toHaveBeenCalled();

      // Verify that update was called with the correct parameters
      expect(sqlHelper.update.mock.calls[0][0]).toBe('blogs'); // Table name

      // Check that content and thumbnail_url columns are being updated
      const updateColumns = sqlHelper.update.mock.calls[0][1];
      expect(updateColumns.some(col => col.column === 'content')).toBe(true);
      expect(updateColumns.some(col => col.column === 'thumbnail_url')).toBe(true);

      // Check that the condition is for the correct blog ID
      const updateConditions = sqlHelper.update.mock.calls[0][2];
      expect(updateConditions[0].condition).toBe('id = :id');
      expect(updateConditions[0].params.id).toBe(2);

      expect(result).toEqual({
        blogId: 2,
        contentUploadUrl: 'content-upload-url',
        thumbnailUploadUrl: 'thumbnail-upload-url',
      });
    });
  });

  describe('updateMedia', () => {
    it('should update blog media and return upload URL for content', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      const dto = new UpdateBlogMediaDto();
      dto.media_type = MediaTypeEnum.CONTENT;
      dto.media_content_type = 'text/html';

      // Mock SqlHelper exists
      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(true);

      // Mock S3 service
      jest.spyOn(s3Service, 'createPresignedWithID').mockResolvedValue('content-upload-url');

      // Act
      const result = await service.updateMedia(blogId, userId, dto);

      // Assert
      expect(sqlHelper.exists).toHaveBeenCalled();
      expect(s3Service.createPresignedWithID).toHaveBeenCalledTimes(1);
      expect(result).toEqual({
        uploadUrl: 'content-upload-url',
      });
    });

    it('should update blog media and return upload URL for thumbnail', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      const dto = new UpdateBlogMediaDto();
      dto.media_type = MediaTypeEnum.THUMBNAIL;
      dto.media_content_type = 'image/jpeg';

      // Mock SqlHelper exists
      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(true);

      // Mock S3 service
      jest.spyOn(s3Service, 'createPresignedWithID').mockResolvedValue('thumbnail-upload-url');

      // Act
      const result = await service.updateMedia(blogId, userId, dto);

      // Assert
      expect(sqlHelper.exists).toHaveBeenCalled();
      expect(s3Service.createPresignedWithID).toHaveBeenCalledTimes(1);
      expect(result).toEqual({
        uploadUrl: 'thumbnail-upload-url',
      });
    });

    it('should throw NotFoundException when blog not found', async () => {
      // Arrange
      const blogId = 999;
      const userId = 1;
      const dto = new UpdateBlogMediaDto();
      dto.media_type = MediaTypeEnum.CONTENT;
      dto.media_content_type = 'text/html';

      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(false);

      // Act & Assert
      await expect(service.updateMedia(blogId, userId, dto)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_ACCESS_DENIED)
      );
    });
  });

  describe('submitForReview', () => {
    it('should submit blog for review', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      // Mock SqlHelper
      const mockDraftBlog = { ...mockBlog, status: BlogStatusEnum.DRAFT };
      jest.spyOn(sqlHelper, 'select').mockResolvedValue([mockDraftBlog]);
      jest.spyOn(sqlHelper, 'update').mockResolvedValue(true);

      // Act
      await service.submitForReview(blogId, userId);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(sqlHelper.update).toHaveBeenCalled();
    });

    it('should throw NotFoundException when blog not found', async () => {
      // Arrange
      const blogId = 999;
      const userId = 1;

      jest.spyOn(sqlHelper, 'select').mockResolvedValue([]);

      // Act & Assert
      await expect(service.submitForReview(blogId, userId)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_ACCESS_DENIED)
      );
    });

    it('should throw BadRequestException when blog is not in draft state', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      const mockPendingBlog = { ...mockBlog, status: BlogStatusEnum.PENDING };
      jest.spyOn(sqlHelper, 'select').mockResolvedValue([mockPendingBlog]);

      // Act & Assert
      await expect(service.submitForReview(blogId, userId)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_NOT_DRAFT)
      );
    });
  });

  describe('cancelSubmit', () => {
    it('should cancel blog review submission', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      const mockPendingBlog = { ...mockBlog, status: BlogStatusEnum.PENDING };
      jest.spyOn(sqlHelper, 'select').mockResolvedValue([mockPendingBlog]);
      jest.spyOn(sqlHelper, 'update').mockResolvedValue(true);

      // Act
      await service.cancelSubmit(blogId, userId);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(sqlHelper.update).toHaveBeenCalled();
    });

    it('should throw NotFoundException when blog not found', async () => {
      // Arrange
      const blogId = 999;
      const userId = 1;

      jest.spyOn(sqlHelper, 'select').mockResolvedValue([]);

      // Act & Assert
      await expect(service.cancelSubmit(blogId, userId)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_ACCESS_DENIED)
      );
    });

    it('should throw BadRequestException when blog is not in pending state', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      const mockDraftBlog = { ...mockBlog, status: BlogStatusEnum.DRAFT };
      jest.spyOn(sqlHelper, 'select').mockResolvedValue([mockDraftBlog]);

      // Act & Assert
      await expect(service.cancelSubmit(blogId, userId)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_NOT_PENDING)
      );
    });
  });

  describe('delete', () => {
    it('should delete a blog', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      const mockDraftBlog = { ...mockBlog, status: BlogStatusEnum.DRAFT };
      jest.spyOn(sqlHelper, 'select').mockResolvedValue([mockDraftBlog]);
      jest.spyOn(sqlHelper, 'update').mockResolvedValue(true);

      // Act
      await service.delete(blogId, userId);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(sqlHelper.update).toHaveBeenCalled();
    });

    it('should throw NotFoundException when blog not found', async () => {
      // Arrange
      const blogId = 999;
      const userId = 1;

      jest.spyOn(sqlHelper, 'select').mockResolvedValue([]);

      // Act & Assert
      await expect(service.delete(blogId, userId)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_ACCESS_DENIED)
      );
    });

    it('should throw ForbiddenException when trying to delete an approved blog', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      const mockApprovedBlog = { ...mockBlog, status: BlogStatusEnum.APPROVED };
      jest.spyOn(sqlHelper, 'select').mockResolvedValue([mockApprovedBlog]);

      // Act & Assert
      await expect(service.delete(blogId, userId)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_CANNOT_DELETE_APPROVED)
      );
    });
  });

  describe('getBlogs', () => {
    it('should return blogs with content for the author', async () => {
      // Arrange
      const userId = 1; // Same as blog author
      const dto = { page: 1, limit: 10 };

      // Mock the queryBuilder
      const queryBuilderMock = {
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockBlog], 1]),
      };

      jest.spyOn(blogRepository, 'createQueryBuilder').mockReturnValue(queryBuilderMock as any);
      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(true); // User has purchased the blog

      // Act
      const result = await service.getBlogs(userId, dto);

      // Assert
      expect(result.content.length).toBe(1);
      expect(result.content[0].content).not.toBeNull(); // Content should be present
      expect(result.content[0].isPurchased).toBe(true);
    });

    it('should return blogs without content for users who have not purchased them', async () => {
      // Arrange
      const userId = 2; // Different from blog author
      const dto = { page: 1, limit: 10 };

      // Mock the queryBuilder
      const queryBuilderMock = {
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoin: jest.fn().mockReturnThis(),
        addSelect: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[mockBlog], 1]),
      };

      jest.spyOn(blogRepository, 'createQueryBuilder').mockReturnValue(queryBuilderMock as any);
      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(false); // User has NOT purchased the blog

      // Act
      const result = await service.getBlogs(userId, dto);

      // Assert
      expect(result.content.length).toBe(1);
      expect(result.content[0].content).toBeNull(); // Content should be null
      expect(result.content[0].isPurchased).toBe(false);
    });
  });
});
