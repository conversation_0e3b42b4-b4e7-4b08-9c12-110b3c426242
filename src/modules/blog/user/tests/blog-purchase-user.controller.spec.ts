import { Test, TestingModule } from '@nestjs/testing';
import { BlogPurchaseUserController } from '../controllers/blog-purchase-user.controller';
import { BlogPurchaseUserService } from '../services/blog-purchase-user.service';
import { GetPurchaseListDto } from '../../dto';
import { NotFoundException, BadRequestException } from '@nestjs/common';

// Mock the SWAGGER_API_TAGS import
jest.mock('@/common/swagger/swagger.tags', () => ({
  SWAGGER_API_TAGS: {
    BLOG_PURCHASES: 'blog-purchases'
  }
}));

// Mock ConfigModule
jest.mock('@nestjs/config', () => {
  const originalModule = jest.requireActual('@nestjs/config');
  return {
    ...originalModule,
    ConfigModule: {
      forRoot: jest.fn().mockReturnValue({
        module: class ConfigModule {},
        providers: [],
      }),
    },
  };
});

// Mock S3Service
jest.mock('@/shared/services/s3.service', () => ({
  S3Service: jest.fn().mockImplementation(() => ({
    createPresignedWithID: jest.fn(),
  })),
}));

// Mock JwtAuthGuard
jest.mock('@/modules/auth/guards', () => ({
  JwtAuthGuard: jest.fn().mockImplementation(() => ({
    canActivate: jest.fn().mockReturnValue(true),
  })),
}));

// Mock CurrentUser decorator
jest.mock('@/common/decorators/current-user.decorator', () => ({
  CurrentUser: () => () => 1, // Always return user ID 1
}));

describe('BlogPurchaseUserController', () => {
  let controller: BlogPurchaseUserController;
  let service: BlogPurchaseUserService;

  const mockBlogPurchaseUserService = {
    getPurchasedBlogs: jest.fn(),
    checkPurchaseStatus: jest.fn(),
    purchaseBlog: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BlogPurchaseUserController],
      providers: [
        {
          provide: BlogPurchaseUserService,
          useValue: mockBlogPurchaseUserService,
        },
      ],
    }).compile();

    controller = module.get<BlogPurchaseUserController>(BlogPurchaseUserController);
    service = module.get<BlogPurchaseUserService>(BlogPurchaseUserService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getPurchasedBlogs', () => {
    it('should return paginated purchase list', async () => {
      // Arrange
      const userId = 1;
      const query: GetPurchaseListDto = { page: 1, limit: 10 };
      const mockRequest = { user: { id: userId } };
      const mockPurchases = {
        content: [
          {
            id: 1,
            blogId: 1,
            userId: 1,
            purchasedAt: 1625097600000,
            point: 100,
            blog: {
              id: 1,
              title: 'Test Blog',
              thumbnailUrl: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
            },
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogPurchaseUserService.getPurchasedBlogs.mockResolvedValue(mockPurchases);

      // Act
      const result = await controller.getPurchasedBlogs(mockRequest, query);

      // Assert
      expect(service.getPurchasedBlogs).toHaveBeenCalledWith(userId, query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockPurchases,
      });
    });

    it('should handle empty purchase list', async () => {
      // Arrange
      const userId = 1;
      const query: GetPurchaseListDto = { page: 1, limit: 10 };
      const mockRequest = { user: { id: userId } };
      const mockEmptyPurchases = {
        content: [],
        totalItems: 0,
        itemCount: 0,
        itemsPerPage: 10,
        totalPages: 0,
        currentPage: 1,
      };
      mockBlogPurchaseUserService.getPurchasedBlogs.mockResolvedValue(mockEmptyPurchases);

      // Act
      const result = await controller.getPurchasedBlogs(mockRequest, query);

      // Assert
      expect(service.getPurchasedBlogs).toHaveBeenCalledWith(userId, query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockEmptyPurchases,
      });
    });

    it('should handle filtering by blog_id', async () => {
      // Arrange
      const userId = 1;
      const query: GetPurchaseListDto = {
        page: 1,
        limit: 10,
        blog_id: 1
      };
      const mockRequest = { user: { id: userId } };
      const mockPurchases = {
        content: [
          {
            id: 1,
            blogId: 1,
            userId: 1,
            purchasedAt: 1625097600000,
            point: 100,
            blog: {
              id: 1,
              title: 'Test Blog',
              thumbnailUrl: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
            },
          },
        ],
        totalItems: 1,
        itemCount: 1,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1,
      };
      mockBlogPurchaseUserService.getPurchasedBlogs.mockResolvedValue(mockPurchases);

      // Act
      const result = await controller.getPurchasedBlogs(mockRequest, query);

      // Assert
      expect(service.getPurchasedBlogs).toHaveBeenCalledWith(userId, query);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockPurchases,
      });
    });

    it('should handle user not found error', async () => {
      // Arrange
      const userId = 999;
      const query: GetPurchaseListDto = { page: 1, limit: 10 };
      const mockRequest = { user: { id: userId } };
      mockBlogPurchaseUserService.getPurchasedBlogs.mockRejectedValue(new NotFoundException('User not found'));

      // Act & Assert
      await expect(controller.getPurchasedBlogs(mockRequest, query)).rejects.toThrow(NotFoundException);
    });
  });

  describe('checkPurchaseStatus', () => {
    it('should return purchase status when blog exists', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      const mockRequest = { user: { id: userId } };
      const mockPurchaseStatus = { purchased: true, purchased_at: 1625097600000 };
      mockBlogPurchaseUserService.checkPurchaseStatus.mockResolvedValue(mockPurchaseStatus);

      // Act
      const result = await controller.checkPurchaseStatus(blogId, mockRequest);

      // Assert
      expect(service.checkPurchaseStatus).toHaveBeenCalledWith(userId, blogId);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockPurchaseStatus,
      });
    });

    it('should return not purchased status when blog is not purchased', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      const mockRequest = { user: { id: userId } };
      const mockPurchaseStatus = { purchased: false };
      mockBlogPurchaseUserService.checkPurchaseStatus.mockResolvedValue(mockPurchaseStatus);

      // Act
      const result = await controller.checkPurchaseStatus(blogId, mockRequest);

      // Assert
      expect(service.checkPurchaseStatus).toHaveBeenCalledWith(userId, blogId);
      expect(result).toEqual({
        code: 200,
        message: 'Success',
        result: mockPurchaseStatus,
      });
    });

    it('should handle blog not found error', async () => {
      // Arrange
      const blogId = 999;
      const userId = 1;
      const mockRequest = { user: { id: userId } };
      mockBlogPurchaseUserService.checkPurchaseStatus.mockRejectedValue(new NotFoundException('Blog not found'));

      // Act & Assert
      await expect(controller.checkPurchaseStatus(blogId, mockRequest)).rejects.toThrow(NotFoundException);
    });
  });

  describe('purchaseBlog', () => {
    it('should purchase a blog and return success response', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      mockBlogPurchaseUserService.purchaseBlog.mockResolvedValue({});

      // Act
      const result = await controller.purchaseBlog(blogId, userId);

      // Assert
      expect(service.purchaseBlog).toHaveBeenCalledWith(userId, blogId);
      expect(result).toEqual({
        code: 201,
        message: 'Blog purchased successfully',
      });
    });

    it('should handle blog not found error', async () => {
      // Arrange
      const blogId = 999;
      const userId = 1;
      mockBlogPurchaseUserService.purchaseBlog.mockRejectedValue(new NotFoundException('Blog not found'));

      // Act & Assert
      await expect(controller.purchaseBlog(blogId, userId)).rejects.toThrow(NotFoundException);
    });

    it('should handle insufficient points error', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      mockBlogPurchaseUserService.purchaseBlog.mockRejectedValue(new BadRequestException('Insufficient points'));

      // Act & Assert
      await expect(controller.purchaseBlog(blogId, userId)).rejects.toThrow(BadRequestException);
    });

    it('should handle already purchased error', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      mockBlogPurchaseUserService.purchaseBlog.mockRejectedValue(new BadRequestException('Blog already purchased'));

      // Act & Assert
      await expect(controller.purchaseBlog(blogId, userId)).rejects.toThrow(BadRequestException);
    });

    it('should handle user not found error', async () => {
      // Arrange
      const blogId = 1;
      const userId = 999;
      mockBlogPurchaseUserService.purchaseBlog.mockRejectedValue(new NotFoundException('User not found'));

      // Act & Assert
      await expect(controller.purchaseBlog(blogId, userId)).rejects.toThrow(NotFoundException);
    });
  });
});
