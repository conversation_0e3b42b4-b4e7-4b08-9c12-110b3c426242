import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse as ApiResponseDoc, ApiParam, ApiQuery, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { BlogCommentUserService } from '@modules/blog/user/services';
import { CreateBlogCommentDto } from '@modules/blog/dto';
import { GetBlogCommentsDto } from '@modules/blog/dto';
import { JwtUserGuard } from '@/modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiResponseDto } from '@/common/response';

@ApiTags(SWAGGER_API_TAGS.BLOG_COMMENTS)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('user/blogs')
export class BlogCommentUserController {
  constructor(private readonly blogCommentUserService: BlogCommentUserService) {}
  /**
   * Tạo bình luận mới cho bài viết
   */
  @Post(':blogId/comments')
  @ApiOperation({
    summary: 'Tạo bình luận mới',
    description: 'Tạo bình luận mới cho bài viết',
  })
  @ApiParam({
    name: 'blogId',
    description: 'ID của bài viết',
    type: Number,
    example: 1,
  })

  @ApiBody({
    type: CreateBlogCommentDto,
    description: 'Thông tin bình luận',
  })
  @ApiResponseDoc({
    status: 201,
    description: 'Bình luận đã được tạo thành công.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 201 },
        message: { type: 'string', example: 'Comment created successfully' },
      },
    },
  })
  async createComment(
    @Param('blogId', ParseIntPipe) blogId: number,
    @CurrentUser('id') userId: number,
    @Body() createCommentDto: CreateBlogCommentDto,
  ): Promise<ApiResponseDto<undefined>> {
    await this.blogCommentUserService.createComment(blogId, userId, createCommentDto);
    return ApiResponseDto.success(undefined)
  }

  /**
   * Xóa bình luận
   */
  @Delete('comments/:id')
  @ApiOperation({
    summary: 'Xóa bình luận',
    description: 'Xóa bình luận theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của bình luận',
    type: Number,
    example: 1,
  })

  @ApiResponseDoc({
    status: 200,
    description: 'Bình luận đã được xóa thành công.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Comment deleted successfully' },
      },
    },
  })
  async deleteComment(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.blogCommentUserService.deleteComment(id, userId);
    return {
      code: 200,
      message: 'Comment deleted successfully',
      result: null,
    };
  }

  /**
   * Lấy danh sách bình luận của bài viết
   */
  @Get(':blogId/comments')
  @ApiOperation({
    summary: 'Lấy danh sách bình luận',
    description: 'Lấy danh sách bình luận của bài viết',
  })
  @ApiParam({
    name: 'blogId',
    description: 'ID của bài viết',
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Trang hiện tại',
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng bản ghi trên mỗi trang',
    type: Number,
    example: 10,
  })
  @ApiResponseDoc({
    status: 200,
    description: 'Danh sách bình luận.',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Success' },
        result: {
          type: 'object',
          properties: {
            content: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'number', example: 1 },
                  blog_id: { type: 'number', example: 1 },
                  user_id: { type: 'number', example: 10 },
                  created_at: { type: 'number', example: 1632474086123 },
                  content: { type: 'string', example: 'Nội dung bình luận' },
                  author_type: { type: 'string', example: 'USER' },
                  employee_id: { type: 'number', example: null, nullable: true },
                  parent_comment_id: { type: 'number', example: null, nullable: true },
                  replies: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        id: { type: 'number', example: 2 },
                        blog_id: { type: 'number', example: 1 },
                        user_id: { type: 'number', example: 15 },
                        created_at: { type: 'number', example: 1632475086123 },
                        content: { type: 'string', example: 'Nội dung trả lời' },
                        author_type: { type: 'string', example: 'USER' },
                        employee_id: { type: 'number', example: null, nullable: true },
                        parent_comment_id: { type: 'number', example: 1 },
                      },
                    },
                  },
                },
              },
            },
            totalItems: { type: 'number', example: 50 },
            itemCount: { type: 'number', example: 10 },
            itemsPerPage: { type: 'number', example: 10 },
            totalPages: { type: 'number', example: 5 },
            currentPage: { type: 'number', example: 1 },
          },
        },
      },
    },
  })
  async getBlogComments(
    @Param('blogId', ParseIntPipe) blogId: number,
    @Query() query: GetBlogCommentsDto,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.blogCommentUserService.getBlogComments(blogId, query);
    return {
      code: 200,
      message: 'Success',
      result,
    };
  }
}
