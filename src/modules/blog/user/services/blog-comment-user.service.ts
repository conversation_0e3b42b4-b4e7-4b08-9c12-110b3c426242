import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Blog, BlogComment } from '../../entities';
import { CreateBlogCommentDto } from '@modules/blog/dto';
import { GetBlogCommentsDto } from '@modules/blog/dto';
import { AuthorTypeEnum, BlogStatusEnum } from '../../enums';
import { SqlHelper } from '@common/helpers/sql.helper';
import { BLOG_ERROR_CODE } from '@modules/blog/exceptions';
import { AppException } from '@/common';

@Injectable()
export class BlogCommentUserService {
  constructor(
    @InjectRepository(BlogComment)
    private readonly blogCommentRepository: Repository<BlogComment>,
    @InjectRepository(Blog)
    private readonly blogRepository: Repository<Blog>,
    private readonly sqlHelper: SqlHelper,
  ) { }

  /**
   * Tạo bình luận mới cho bài viết
   * @param blogId ID của bài viết
   * @param userId ID của người dùng
   * @param dto Dữ liệu bình luận
   * @returns Bình luận đã tạo
   */
  async createComment(blogId: number, userId: number, dto: CreateBlogCommentDto): Promise<BlogComment> {
    try {
      // Kiểm tra bài viết có tồn tại không
      const blogExists = await this.sqlHelper.exists(
        'blogs',
        [
          { condition: 'id = :id', params: { id: blogId } },
          { condition: 'enable = :enable', params: { enable: true } },
          { condition: 'status = :status', params: { status: BlogStatusEnum.APPROVED } }
        ]
      );

      if (!blogExists) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_NOT_FOUND);
      }

      // Nếu là bình luận phản hồi, kiểm tra bình luận cha có tồn tại không
      if (dto.parent_comment_id) {
        const parentComment = await this.sqlHelper.select(
          'blog_comments',
          ['id', 'parent_comment_id as parentCommentId'],
          [
            { condition: 'id = :id', params: { id: dto.parent_comment_id } },
            { condition: 'blog_id = :blogId', params: { blogId } }
          ]
        );

        if (!parentComment || parentComment.length === 0) {
          throw new AppException(BLOG_ERROR_CODE.BLOG_COMMENT_PARENT_NOT_FOUND);
        }

        // Kiểm tra xem bình luận cha có phải là bình luận gốc không
        if (parentComment[0].parentCommentId) {
          throw new AppException(BLOG_ERROR_CODE.BLOG_COMMENT_CANNOT_REPLY_TO_REPLY);
        }
      }

      // Tạo bình luận mới
      const now = Date.now();
      const commentData = {
        blog_id: blogId,
        user_id: userId,
        content: dto.content,
        author_type: AuthorTypeEnum.USER,
        created_at: now,
        parent_comment_id: dto.parent_comment_id || null
      };

      // Lưu bình luận vào database
      const result = await this.sqlHelper.insert(
        'blog_comments',
        commentData,
        ['id', 'blog_id as blogId', 'user_id as userId', 'content', 'author_type as authorType', 'created_at as createdAt', 'parent_comment_id as parentCommentId']
      );

      return result as unknown as BlogComment;
    } catch (error: unknown) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(BLOG_ERROR_CODE.BLOG_COMMENT_CREATE_FAILED);
    }
  }

  /**
   * Xóa bình luận
   * @param commentId ID của bình luận
   * @param userId ID của người dùng
   */
  async deleteComment(commentId: number, userId: number): Promise<void> {
    try {
      // Kiểm tra bình luận có tồn tại không
      const comment = await this.sqlHelper.select(
        'blog_comments',
        ['id', 'user_id as userId'],
        [
          { condition: 'id = :id', params: { id: commentId } }
        ]
      );

      if (!comment || comment.length === 0) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_COMMENT_NOT_FOUND);
      }

      // Kiểm tra người dùng có quyền xóa bình luận không
      if (comment[0].userId !== userId) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_COMMENT_ACCESS_DENIED);
      }

      // Xóa các bình luận phản hồi (nếu có)
      await this.sqlHelper.delete(
        'blog_comments',
        [
          { condition: 'parent_comment_id = :parentCommentId', params: { parentCommentId: commentId } }
        ]
      );

      // Xóa bình luận
      await this.sqlHelper.delete(
        'blog_comments',
        [
          { condition: 'id = :id', params: { id: commentId } }
        ]
      );
    } catch (error: unknown) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(BLOG_ERROR_CODE.BLOG_COMMENT_DELETE_FAILED);
    }
  }

  /**
   * Lấy danh sách bình luận của bài viết
   * @param blogId ID của bài viết
   * @param dto Tham số phân trang
   * @returns Danh sách bình luận có phân trang
   */
  async getBlogComments(blogId: number, dto: GetBlogCommentsDto) {
    try {
      // Kiểm tra bài viết có tồn tại không
      const blogExists = await this.sqlHelper.exists(
        'blogs',
        [
          { condition: 'id = :id', params: { id: blogId } },
          { condition: 'enable = :enable', params: { enable: true } },
          { condition: 'status = :status', params: { status: BlogStatusEnum.APPROVED } }
        ]
      );

      if (!blogExists) {
        throw new AppException(BLOG_ERROR_CODE.BLOG_NOT_FOUND);
      }

      // Lấy danh sách bình luận gốc (không có parent_comment_id) với phân trang
      const offset = (dto.page - 1) * dto.limit;
      const rootComments = await this.sqlHelper.select(
        'blog_comments',
        [
          'id', 'blog_id as blogId', 'user_id as userId', 'created_at as createdAt',
          'content', 'author_type as authorType', 'employee_id as employeeId',
          'parent_comment_id as parentCommentId'
        ],
        [
          { condition: 'blog_id = :blogId', params: { blogId } },
          { condition: 'parent_comment_id IS NULL' }
        ],
        [],
        [{ column: 'created_at', direction: 'DESC' }],
        [],
        undefined,
        { limit: dto.limit, offset: offset }
      );

      // Lấy tổng số bình luận gốc
      const totalItems = await this.sqlHelper.count(
        'blog_comments',
        [
          { condition: 'blog_id = :blogId', params: { blogId } },
          { condition: 'parent_comment_id IS NULL' }
        ]
      );

      // Lấy danh sách bình luận phản hồi cho mỗi bình luận gốc
      const commentsWithReplies = await Promise.all(
        rootComments.map(async (comment) => {
          const replies = await this.sqlHelper.select(
            'blog_comments',
            [
              'id', 'blog_id as blogId', 'user_id as userId', 'created_at as createdAt',
              'content', 'author_type as authorType', 'employee_id as employeeId',
              'parent_comment_id as parentCommentId'
            ],
            [
              { condition: 'parent_comment_id = :parentCommentId', params: { parentCommentId: Number(comment.id) } }
            ],
            [],
            [{ column: 'created_at', direction: 'ASC' }],
            [],
            undefined
          );

          // Lấy thông tin tác giả cho mỗi bình luận và phản hồi
          const commentWithAuthor = await this.getCommentWithAuthor(comment);
          const repliesWithAuthor = await Promise.all(
            replies.map(reply => this.getCommentWithAuthor(reply))
          );

          return {
            ...commentWithAuthor,
            replies: repliesWithAuthor,
          };
        })
      );

      return {
        content: commentsWithReplies,
        totalItems,
        itemCount: commentsWithReplies.length,
        itemsPerPage: dto.limit,
        totalPages: Math.ceil(totalItems / dto.limit),
        currentPage: dto.page,
      };
    } catch (error: unknown) {
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(BLOG_ERROR_CODE.BLOG_COMMENTS_FETCH_FAILED);
    }
  }

  /**
   * Helper method to get comment with author information
   */
  private async getCommentWithAuthor(comment: any) {
    if (comment.authorType === AuthorTypeEnum.USER && comment.userId) {
      const user = await this.sqlHelper.select(
        'users',
        ['id', 'full_name as name', 'avatar'],
        [{ condition: 'id = :id', params: { id: comment.userId } }]
      );

      if (user && user.length > 0) {
        return {
          ...comment,
          author: {
            id: user[0].id,
            name: user[0].name,
            avatar: user[0].avatar,
            type: AuthorTypeEnum.USER
          }
        };
      }
    }

    return comment;
  }
}
