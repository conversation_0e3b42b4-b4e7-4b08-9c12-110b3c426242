import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BlogComment } from '../entities/blog-comment.entity';
import { AuthorTypeEnum } from '../enums';

describe('BlogComment Entity', () => {
  let blogCommentRepository: Repository<BlogComment>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: getRepositoryToken(BlogComment),
          useClass: Repository,
        },
      ],
    }).compile();

    blogCommentRepository = module.get<Repository<BlogComment>>(getRepositoryToken(BlogComment));
  });

  it('should be defined', () => {
    expect(blogCommentRepository).toBeDefined();
  });

  describe('BlogComment Entity Creation', () => {
    it('should create a blog comment entity with all required fields', () => {
      const comment = new BlogComment();
      comment.id = 1;
      comment.blogId = 1;
      comment.userId = 1;
      comment.content = 'This is a test comment';
      comment.createdAt = Date.now();
      comment.authorType = AuthorTypeEnum.USER;
      comment.employeeId = null as number | null;
      comment.parentCommentId = null as number | null;

      expect(comment).toBeDefined();
      expect(comment.id).toBe(1);
      expect(comment.blogId).toBe(1);
      expect(comment.userId).toBe(1);
      expect(comment.content).toBe('This is a test comment');
      expect(comment.createdAt).toBeDefined();
      expect(comment.authorType).toBe(AuthorTypeEnum.USER);
      expect(comment.employeeId).toBeNull();
      expect(comment.parentCommentId).toBeNull();
    });

    it('should create a reply comment entity', () => {
      const comment = new BlogComment();
      comment.id = 2;
      comment.blogId = 1;
      comment.userId = 1;
      comment.content = 'This is a reply comment';
      comment.createdAt = Date.now();
      comment.authorType = AuthorTypeEnum.USER;
      comment.employeeId = null as number | null;
      comment.parentCommentId = 1;

      expect(comment).toBeDefined();
      expect(comment.id).toBe(2);
      expect(comment.blogId).toBe(1);
      expect(comment.parentCommentId).toBe(1);
    });

    it('should create a comment entity by employee', () => {
      const comment = new BlogComment();
      comment.id = 3;
      comment.blogId = 1;
      comment.userId = null as number | null;
      comment.content = 'This is an employee comment';
      comment.createdAt = Date.now();
      comment.authorType = AuthorTypeEnum.SYSTEM;
      comment.employeeId = 1;
      comment.parentCommentId = null as number | null;

      expect(comment).toBeDefined();
      expect(comment.id).toBe(3);
      expect(comment.userId).toBeNull();
      expect(comment.employeeId).toBe(1);
      expect(comment.authorType).toBe(AuthorTypeEnum.SYSTEM);
    });

    it('should create a reply comment entity by employee', () => {
      const comment = new BlogComment();
      comment.id = 4;
      comment.blogId = 1;
      comment.userId = null as number | null;
      comment.content = 'This is an employee reply comment';
      comment.createdAt = Date.now();
      comment.authorType = AuthorTypeEnum.SYSTEM;
      comment.employeeId = 1;
      comment.parentCommentId = 1;

      expect(comment).toBeDefined();
      expect(comment.id).toBe(4);
      expect(comment.userId).toBeNull();
      expect(comment.employeeId).toBe(1);
      expect(comment.authorType).toBe(AuthorTypeEnum.SYSTEM);
      expect(comment.parentCommentId).toBe(1);
    });
  });

  describe('BlogComment Entity Type Validation', () => {
    it('should validate all field types correctly', () => {
      const comment = new BlogComment();

      // Number fields
      comment.id = 1;
      comment.blogId = 1;
      comment.userId = 1;
      comment.createdAt = 1625097600000;

      // String fields
      comment.content = 'Test Comment Content';

      // Enum fields
      comment.authorType = AuthorTypeEnum.USER;

      // Nullable fields
      comment.employeeId = null as number | null;
      comment.parentCommentId = null as number | null;

      // Type assertions
      expect(typeof comment.id).toBe('number');
      expect(typeof comment.blogId).toBe('number');
      expect(typeof comment.userId).toBe('number');
      expect(typeof comment.createdAt).toBe('number');

      expect(typeof comment.content).toBe('string');

      expect(comment.authorType).toBe(AuthorTypeEnum.USER);

      expect(comment.employeeId).toBeNull();
      expect(comment.parentCommentId).toBeNull();
    });

    it('should handle all enum values correctly', () => {
      const comment = new BlogComment();

      // Test all AuthorTypeEnum values
      comment.authorType = AuthorTypeEnum.USER;
      expect(comment.authorType).toBe(AuthorTypeEnum.USER);

      comment.authorType = AuthorTypeEnum.SYSTEM;
      expect(comment.authorType).toBe(AuthorTypeEnum.SYSTEM);
    });
  });

  describe('BlogComment Entity Edge Cases', () => {
    it('should handle large numbers correctly', () => {
      const comment = new BlogComment();

      // Large ID
      comment.id = 1000000000; // 1 billion
      expect(comment.id).toBe(1000000000);

      // Large timestamp
      comment.createdAt = 9999999999999; // Far future timestamp
      expect(comment.createdAt).toBe(9999999999999);
    });

    it('should handle empty or minimal data correctly', () => {
      const comment = new BlogComment();

      // Only set ID
      comment.id = 100;

      expect(comment.id).toBe(100);

      // Other fields should be undefined or their default values
      expect(comment.content).toBeUndefined();
      expect(comment.blogId).toBeUndefined();
      expect(comment.userId).toBeUndefined();
      expect(comment.employeeId).toBeUndefined();
    });

    it('should handle long comment content', () => {
      const comment = new BlogComment();
      comment.id = 101;

      // Create a long comment (1000 characters)
      const longContent = 'A'.repeat(1000);
      comment.content = longContent;

      expect(comment.content.length).toBe(1000);
      expect(comment.content).toBe(longContent);
    });

    it('should handle special characters in comment content', () => {
      const comment = new BlogComment();
      comment.id = 102;

      // Comment with special characters
      const specialContent = 'This comment has special characters: !@#$%^&*()_+{}|:"<>?~`-=[]\\;\',./';
      comment.content = specialContent;

      expect(comment.content).toBe(specialContent);
    });

    it('should handle both user and employee being null', () => {
      const comment = new BlogComment();
      comment.id = 103;
      comment.blogId = 1;
      comment.userId = null as number | null;
      comment.employeeId = null as number | null;
      comment.content = 'Anonymous comment';
      comment.authorType = AuthorTypeEnum.SYSTEM;

      expect(comment.id).toBe(103);
      expect(comment.userId).toBeNull();
      expect(comment.employeeId).toBeNull();
      expect(comment.content).toBe('Anonymous comment');
    });
  });
});
