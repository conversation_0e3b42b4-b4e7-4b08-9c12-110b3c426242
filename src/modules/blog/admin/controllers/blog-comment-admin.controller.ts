import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { Controller, Get, Post, Delete, Param, ParseIntPipe, Query, Body, UseGuards, Req } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiQuery, ApiResponse as ApiResponseDoc, ApiTags, ApiBody } from '@nestjs/swagger';
import { BlogCommentAdminService } from '@modules/blog/admin/services';
import { GetBlogCommentsDto, CreateBlogCommentDto } from '../../dto';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { Roles } from '@/modules/auth/decorators';
import { AppException } from '@/common';
import { ApiResponseDto } from '@/common/response';
import { CurrentEmployee } from '@/modules/auth/decorators';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';
import { PermissionsGuard } from '@modules/auth/guards/permissions.guard';

@ApiTags(SWAGGER_API_TAGS.ADMIN_BLOGS)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@Controller('admin/blog/comments')
export class BlogCommentAdminController {
  constructor(private readonly blogCommentAdminService: BlogCommentAdminService) {}

  /**
   * Tạo bình luận từ hệ thống
   */
  @Post(':blogId')
  @ApiOperation({
    summary: 'Tạo bình luận từ hệ thống',
    description: 'Tạo bình luận từ hệ thống cho bài viết',
  })
  @ApiParam({
    name: 'blogId',
    description: 'ID của bài viết',
    type: Number,
    example: 1,
  })
  @ApiBody({
    type: CreateBlogCommentDto,
    description: 'Dữ liệu bình luận',
    examples: {
      example1: {
        value: {
          content: 'Nội dung bình luận từ hệ thống',
          parent_comment_id: null
        },
        summary: 'Bình luận gốc',
      },
      example2: {
        value: {
          content: 'Nội dung phản hồi từ hệ thống',
          parent_comment_id: 1
        },
        summary: 'Bình luận phản hồi',
      },
    },
  })
  @ApiResponseDoc({
    status: 201,
    description: 'Bình luận đã được tạo thành công.',
    schema: {
      properties: {
        code: { type: 'number', example: 201 },
        message: { type: 'string', example: 'Comment created successfully' },
        result: {
          type: 'object',
          properties: {
            id: { type: 'number', example: 1 },
            blog_id: { type: 'number', example: 1 },
            user_id: { type: 'null', example: null },
            employee_id: { type: 'number', example: 5 },
            created_at: { type: 'number', example: 1632474086123 },
            content: { type: 'string', example: 'Nội dung bình luận từ hệ thống' },
            author_type: { type: 'string', example: 'SYSTEM' },
            parent_comment_id: { type: 'null', example: null }
          },
        },
      },
    },
  })
  @ApiResponseDoc({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ.',
    type: AppException,
  })
  @ApiResponseDoc({
    status: 404,
    description: 'Không tìm thấy bài viết hoặc bình luận cha.',
    type: AppException,
  })
  async createSystemComment(
    @Param('blogId', ParseIntPipe) blogId: number,
    @Body() dto: CreateBlogCommentDto,
    @CurrentEmployee() employee: JwtPayload,
  ): Promise<ApiResponseDto<any>> {
    const employeeId = employee.id; // Lấy ID của nhân viên từ JWT token
    const result = await this.blogCommentAdminService.createSystemComment(blogId, employeeId, dto);
    return ApiResponseDto.created(result, 'Đăng bình luận thành công');
  }

  /**
   * Xóa bình luận
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa bình luận',
    description: 'Xóa bình luận và các phản hồi của nó',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của bình luận',
    type: Number,
    example: 1,
  })
  @ApiResponseDoc({
    status: 200,
    description: 'Bình luận đã được xóa thành công.',
    schema: {
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Comment deleted successfully' },
        result: { type: 'null', example: null },
      },
    },
  })
  @ApiResponseDoc({
    status: 404,
    description: 'Không tìm thấy bình luận.',
    type: AppException,
  })
  async deleteComment(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<any>> {
    await this.blogCommentAdminService.deleteComment(id);
    return ApiResponseDto.deleted(null, 'Xóa bình luận thành công');
  }

  /**
   * Lấy danh sách bình luận của bài viết
   */
  @Get(':blogId')
  @ApiOperation({
    summary: 'Lấy danh sách bình luận của bài viết',
    description: 'Lấy danh sách bình luận của bài viết với phân trang',
  })
  @ApiParam({
    name: 'blogId',
    description: 'ID của bài viết',
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Trang hiện tại',
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Số lượng bản ghi trên mỗi trang',
    type: Number,
    example: 10,
  })
  @ApiResponseDoc({
    status: 200,
    description: 'Danh sách bình luận đã được lấy thành công.',
    schema: {
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Success' },
        result: {
          type: 'object',
          properties: {
            content: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'number', example: 1 },
                  blog_id: { type: 'number', example: 1 },
                  user_id: { type: 'number', example: 10 },
                  created_at: { type: 'number', example: 1632474086123 },
                  content: { type: 'string', example: 'Nội dung bình luận' },
                  author_type: { type: 'string', example: 'USER' },
                  employee_id: { type: 'number', example: null },
                  parent_comment_id: { type: 'number', example: null },
                  replies: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        id: { type: 'number', example: 2 },
                        blog_id: { type: 'number', example: 1 },
                        user_id: { type: 'number', example: 15 },
                        created_at: { type: 'number', example: 1632475086123 },
                        content: { type: 'string', example: 'Nội dung trả lời' },
                        author_type: { type: 'string', example: 'USER' },
                        employee_id: { type: 'number', example: null },
                        parent_comment_id: { type: 'number', example: 1 },
                      },
                    },
                  },
                },
              },
            },
            totalItems: { type: 'number', example: 50 },
            itemCount: { type: 'number', example: 10 },
            itemsPerPage: { type: 'number', example: 10 },
            totalPages: { type: 'number', example: 5 },
            currentPage: { type: 'number', example: 1 },
          },
        },
      },
    },
  })
  @ApiResponseDoc({
    status: 400,
    description: 'Dữ liệu đầu vào không hợp lệ.',
    type: AppException,
  })
  @ApiResponseDoc({
    status: 404,
    description: 'Không tìm thấy bài viết.',
    type: AppException,
  })
  async getComments(
    @Param('blogId', ParseIntPipe) blogId: number,
    @Query() dto: GetBlogCommentsDto,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.blogCommentAdminService.getComments(blogId, dto);
    return ApiResponseDto.success(result,'Danh sách bình luận đã được lấy thành công.');
  }
}
