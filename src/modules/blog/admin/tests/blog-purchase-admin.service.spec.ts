import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BlogPurchaseAdminService } from '../services/blog-purchase-admin.service';
import { BlogPurchase } from '../../entities/blog-purchase.entity';
import { Blog } from '../../entities/blog.entity';
import { User } from '@/modules/user/entities/user.entity';
import { BlogPurchaseQueryDto, BlogPurchaseStatisticsDto } from '../../dto';
import { SqlHelper } from '@common/helpers/sql.helper';

// Mock data
const mockPurchase = {
  id: 1,
  userId: 1,
  blogId: 1,
  point: 100,
  purchasedAt: 1625097600000,
  platformFeePercent: 10,
  sellerReceivePrice: 90,
};

const mockEnrichedPurchase = {
  ...mockPurchase,
  blog: mockPurchase,
  user: mockPurchase
};

const mockPurchaseStatistics = {
  total_purchases: 10,
  total_points: 1000,
  total_platform_fee: 100,
  total_to_sellers: 900,
};

describe('BlogPurchaseAdminService', () => {
  let service: BlogPurchaseAdminService;
  let blogPurchaseRepository: Repository<BlogPurchase>;
  let blogRepository: Repository<Blog>;
  let userRepository: Repository<User>;
  let sqlHelper: SqlHelper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BlogPurchaseAdminService,
        {
          provide: getRepositoryToken(BlogPurchase),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            count: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Blog),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: SqlHelper,
          useValue: {
            select: jest.fn(),
            count: jest.fn(),
            selectOne: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<BlogPurchaseAdminService>(BlogPurchaseAdminService);
    blogPurchaseRepository = module.get<Repository<BlogPurchase>>(getRepositoryToken(BlogPurchase));
    blogRepository = module.get<Repository<Blog>>(getRepositoryToken(Blog));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    sqlHelper = module.get<SqlHelper>(SqlHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getPurchases', () => {
    it('should return paginated purchase list', async () => {
      // Arrange
      const dto: BlogPurchaseQueryDto = { page: 1, limit: 10 };
      const mockPurchases = [mockPurchase];
      const mockEnrichedPurchases = [mockEnrichedPurchase];
      const totalItems = 1;

      jest.spyOn(sqlHelper, 'select')
        .mockResolvedValueOnce(mockPurchases)
        .mockResolvedValueOnce([mockPurchase]) // blog info
        .mockResolvedValueOnce([mockPurchase]); // user info
      jest.spyOn(sqlHelper, 'count').mockResolvedValue(totalItems);

      // Act
      const result = await service.getPurchases(dto);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(sqlHelper.count).toHaveBeenCalled();
      expect(result).toEqual({
        content: mockEnrichedPurchases,
        totalItems,
        itemCount: mockPurchases.length,
        itemsPerPage: dto.limit,
        totalPages: 1,
        currentPage: dto.page,
      });
    });

    it('should filter by blog_id', async () => {
      // Arrange
      const dto: BlogPurchaseQueryDto = { page: 1, limit: 10, blog_id: 1 };
      const mockPurchases = [mockPurchase];
      const mockEnrichedPurchases = [mockEnrichedPurchase];
      const totalItems = 1;

      jest.spyOn(sqlHelper, 'select')
        .mockResolvedValueOnce(mockPurchases)
        .mockResolvedValueOnce([mockPurchase]) // blog info
        .mockResolvedValueOnce([mockPurchase]); // user info
      jest.spyOn(sqlHelper, 'count').mockResolvedValue(totalItems);

      // Act
      const result = await service.getPurchases(dto);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(sqlHelper.count).toHaveBeenCalled();
      expect(result).toEqual({
        content: mockEnrichedPurchases,
        totalItems,
        itemCount: mockPurchases.length,
        itemsPerPage: dto.limit,
        totalPages: 1,
        currentPage: dto.page,
      });
    });

    it('should filter by user_id', async () => {
      // Arrange
      const dto: BlogPurchaseQueryDto = { page: 1, limit: 10, user_id: 1 };
      const mockPurchases = [mockPurchase];
      const mockEnrichedPurchases = [mockEnrichedPurchase];
      const totalItems = 1;

      jest.spyOn(sqlHelper, 'select')
        .mockResolvedValueOnce(mockPurchases)
        .mockResolvedValueOnce([mockPurchase]) // blog info
        .mockResolvedValueOnce([mockPurchase]); // user info
      jest.spyOn(sqlHelper, 'count').mockResolvedValue(totalItems);

      // Act
      const result = await service.getPurchases(dto);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(sqlHelper.count).toHaveBeenCalled();
      expect(result).toEqual({
        content: mockEnrichedPurchases,
        totalItems,
        itemCount: mockPurchases.length,
        itemsPerPage: dto.limit,
        totalPages: 1,
        currentPage: dto.page,
      });
    });

    it('should filter by date range', async () => {
      // Arrange
      const dto: BlogPurchaseQueryDto = {
        page: 1,
        limit: 10,
        start_date: 1625000000000,
        end_date: 1626000000000
      };
      const mockPurchases = [mockPurchase];
      const mockEnrichedPurchases = [mockEnrichedPurchase];
      const totalItems = 1;

      jest.spyOn(sqlHelper, 'select')
        .mockResolvedValueOnce(mockPurchases)
        .mockResolvedValueOnce([mockPurchase]) // blog info
        .mockResolvedValueOnce([mockPurchase]); // user info
      jest.spyOn(sqlHelper, 'count').mockResolvedValue(totalItems);

      // Act
      const result = await service.getPurchases(dto);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(sqlHelper.count).toHaveBeenCalled();
      expect(result).toEqual({
        content: mockEnrichedPurchases,
        totalItems,
        itemCount: mockPurchases.length,
        itemsPerPage: dto.limit,
        totalPages: 1,
        currentPage: dto.page,
      });
    });

    it('should return empty list when no purchases found', async () => {
      // Arrange
      const dto: BlogPurchaseQueryDto = { page: 1, limit: 10 };
      const mockPurchases = [];
      const totalItems = 0;

      jest.spyOn(sqlHelper, 'select').mockResolvedValueOnce(mockPurchases);
      jest.spyOn(sqlHelper, 'count').mockResolvedValue(totalItems);

      // Act
      const result = await service.getPurchases(dto);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(sqlHelper.count).toHaveBeenCalled();
      expect(result).toEqual({
        content: mockPurchases,
        totalItems,
        itemCount: mockPurchases.length,
        itemsPerPage: dto.limit,
        totalPages: 0,
        currentPage: dto.page,
      });
    });
  });

  describe('getStatistics', () => {
    it('should return purchase statistics', async () => {
      // Arrange
      const dto: BlogPurchaseStatisticsDto = {};

      jest.spyOn(sqlHelper, 'count').mockResolvedValue(mockPurchaseStatistics.total_purchases);
      jest.spyOn(sqlHelper, 'select')
        .mockResolvedValueOnce([{ total: mockPurchaseStatistics.total_points }])
        .mockResolvedValueOnce([{
          totalPlatformFee: mockPurchaseStatistics.total_platform_fee,
          totalToSellers: mockPurchaseStatistics.total_to_sellers
        }]);

      // Act
      const result = await service.getStatistics(dto);

      // Assert
      expect(sqlHelper.count).toHaveBeenCalled();
      expect(sqlHelper.select).toHaveBeenCalledTimes(2);
      expect(result).toEqual(mockPurchaseStatistics);
    });

    it('should filter statistics by date range', async () => {
      // Arrange
      const dto: BlogPurchaseStatisticsDto = {
        start_date: 1625000000000,
        end_date: 1626000000000
      };

      jest.spyOn(sqlHelper, 'count').mockResolvedValue(mockPurchaseStatistics.total_purchases);
      jest.spyOn(sqlHelper, 'select')
        .mockResolvedValueOnce([{ total: mockPurchaseStatistics.total_points }])
        .mockResolvedValueOnce([{
          totalPlatformFee: mockPurchaseStatistics.total_platform_fee,
          totalToSellers: mockPurchaseStatistics.total_to_sellers
        }]);

      // Act
      const result = await service.getStatistics(dto);

      // Assert
      expect(sqlHelper.count).toHaveBeenCalled();
      expect(sqlHelper.select).toHaveBeenCalledTimes(2);
      expect(result).toEqual(mockPurchaseStatistics);
    });

    it('should return zero values when no purchases found', async () => {
      // Arrange
      const dto: BlogPurchaseStatisticsDto = {};
      const emptyStats = {
        total_purchases: 0,
        total_points: 0,
        total_platform_fee: 0,
        total_to_sellers: 0,
      };

      jest.spyOn(sqlHelper, 'count').mockResolvedValue(0);
      jest.spyOn(sqlHelper, 'select')
        .mockResolvedValueOnce([{ total: 0 }])
        .mockResolvedValueOnce([{ totalPlatformFee: 0, totalToSellers: 0 }]);

      // Act
      const result = await service.getStatistics(dto);

      // Assert
      expect(sqlHelper.count).toHaveBeenCalled();
      expect(sqlHelper.select).toHaveBeenCalledTimes(2);
      expect(result).toEqual(emptyStats);
    });
  });
});
