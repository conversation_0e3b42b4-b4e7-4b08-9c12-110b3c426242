import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class BlogPurchaseQueryDto {
  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({
    description: 'Số lượng bản ghi trên mỗi trang',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number = 10;

  @ApiProperty({
    description: 'Lọc theo ID của bài viết',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  blog_id?: number;

  @ApiProperty({
    description: 'Lọc theo ID của người dùng',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  user_id?: number;

  @ApiProperty({
    description: '<PERSON>tamp bắt đầu',
    example: 1632474086123,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  start_date?: number;

  @ApiProperty({
    description: 'Timestamp kết thúc',
    example: 1632574086123,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  end_date?: number;
}