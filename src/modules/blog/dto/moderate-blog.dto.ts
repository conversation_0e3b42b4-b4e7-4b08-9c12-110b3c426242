import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { BlogStatusEnum } from '../enums';

export class ModerateBlogDto {
  @ApiProperty({
    description: 'Tr<PERSON>ng thái mới của bài viết',
    example: BlogStatusEnum.APPROVED,
    enum: BlogStatusEnum,
    required: true,
  })
  @IsNotEmpty()
  @IsEnum(BlogStatusEnum)
  status: BlogStatusEnum;

  @ApiProperty({
    description: '<PERSON><PERSON>n hồi cho người dùng',
    example: '<PERSON>à<PERSON> viết đã được phê duyệt',
    required: false,
  })
  @IsString()
  feedback?: string;
}