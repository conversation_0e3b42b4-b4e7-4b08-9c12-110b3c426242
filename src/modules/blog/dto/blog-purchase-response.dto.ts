import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { BlogResponseDto, AuthorDto } from './blog-response.dto';
import { BlogPurchase } from '../entities';

/**
 * DTO cho thông tin người mua
 */
export class BuyerDto {
  @Expose()
  @ApiProperty({
    description: 'ID của người mua',
    example: 10,
    nullable: true,
  })
  id: number | null;

  @Expose()
  @ApiProperty({
    description: 'Tên người mua',
    example: 'Nguyễn Văn B',
    nullable: true,
  })
  name: string;

  @Expose()
  @ApiProperty({
    description: 'Avatar của người mua',
    example: 'https://cdn.example.com/avatars/user20.jpg',
    nullable: true,
  })
  avatar: string;
}

/**
 * DTO cho thông tin chi tiết giao dịch mua bài viết
 */
export class BlogPurchaseDetailResponseDto {
  @Expose()
  @ApiProperty({
    description: 'ID của giao dịch mua bài viết',
    example: 1,
  })
  id: number;

  @Expose()
  @ApiProperty({
    description: 'ID của người mua',
    example: 10,
  })
  userId: number;

  @Expose()
  @ApiProperty({
    description: 'ID của bài viết',
    example: 1,
  })
  blogId: number;

  @Expose()
  @ApiProperty({
    description: 'Số điểm đã sử dụng để mua',
    example: 100,
  })
  point: number;

  @Expose()
  @ApiProperty({
    description: 'Thời gian mua (Unix timestamp)',
    example: 1632474086123,
  })
  purchasedAt: number;

  @Expose()
  @ApiProperty({
    description: 'Phần trăm phí nền tảng',
    example: 5,
  })
  platformFeePercent: number;

  @Expose()
  @ApiProperty({
    description: 'Số điểm người bán nhận được',
    example: 95,
  })
  sellerReceivePrice: number;

  @Expose()
  @Type(() => BlogResponseDto)
  @ApiProperty({
    description: 'Thông tin bài viết',
    type: BlogResponseDto,
  })
  blog: BlogResponseDto;

  @Expose()
  @Type(() => BuyerDto)
  @ApiProperty({
    description: 'Thông tin người mua',
    type: BuyerDto,
  })
  buyer: BuyerDto;
}

/**
 * DTO cho danh sách bài viết đã mua có phân trang
 */
export class PaginatedBlogPurchaseResponseDto {
  @Expose()
  @Type(() => BlogResponseDto)
  @ApiProperty({
    description: 'Danh sách bài viết đã mua',
    type: [BlogResponseDto],
  })
  content: BlogResponseDto[];

  @Expose()
  @ApiProperty({
    description: 'Tổng số bản ghi',
    example: 100,
  })
  totalItems: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng bản ghi trên trang hiện tại',
    example: 10,
  })
  itemCount: number;

  @Expose()
  @ApiProperty({
    description: 'Số lượng bản ghi trên mỗi trang',
    example: 10,
  })
  itemsPerPage: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng số trang',
    example: 10,
  })
  totalPages: number;

  @Expose()
  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
  })
  currentPage: number;
}

/**
 * DTO cho trạng thái mua bài viết
 */
export class BlogPurchaseStatusResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Đã mua hay chưa',
    example: true,
  })
  purchased: boolean;

  @Expose()
  @ApiProperty({
    description: 'Thời gian mua (Unix timestamp)',
    example: 1632474086123,
    nullable: true,
  })
  purchased_at?: number;
}
