import { Controller, Get, Logger } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags, ApiExtraModels } from '@nestjs/swagger';
import { CommonService } from '../services/common.service';
import { CurrencyResponseDto } from '../dto/currency-response.dto';
import { SwaggerApiTag } from '@common/swagger';
import { ApiResponseDto } from '@/common/response/api-response-dto';

/**
 * Controller xử lý các API chung
 */
@ApiTags(SwaggerApiTag.COMMON)
@ApiExtraModels(ApiResponseDto, CurrencyResponseDto)
@Controller('common')
export class CommonController {
  private readonly logger = new Logger(CommonController.name);

  constructor(private readonly commonService: CommonService) {}

  /**
   * Lấy danh sách tiền tệ
   * @returns Danh sách tiền tệ
   */
  @Get('currencies')
  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách tiền tệ' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tiền tệ',
    schema: ApiResponseDto.getArraySchema(CurrencyResponseDto),
  })
  async getCurrencies(): Promise<ApiResponseDto<CurrencyResponseDto[]>> {
    try {
      const currencies = await this.commonService.getCurrencies();
      return ApiResponseDto.success(currencies);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách tiền tệ: ${error.message}`, error.stack);
      throw error;
    }
  }
}
