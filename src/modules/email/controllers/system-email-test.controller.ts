import { Body, Controller, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { SystemEmailService } from '../services/system-email.service';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JWTPayload } from '@/modules/auth/interfaces';
import { ApiErrorResponse } from '@/common/error/api-error-response.decorator';
import { ApiErrorResponseDto } from '@/common/error/api-error-response.dto';
import { EMAIL_ERROR_CODES } from '../common/email-error-codes';
import { SendSystemEmailDto, SendSystemTemplateEmailDto, SystemEmailResponseDto } from '../dto/system-email.dto';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';

/**
 * Controller cung cấp API để test tính năng gửi email hệ thống
 */
@ApiTags(SWAGGER_API_TAGS.EMAIL)
@ApiExtraModels(ApiResponseDto, SystemEmailResponseDto, PaginatedResult, ApiErrorResponseDto)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/system-email-test')
export class SystemEmailTestController {
  constructor(private readonly systemEmailService: SystemEmailService) {}

  /**
   * Gửi email hệ thống đơn giản để test
   */
  @Post('send-email')
  @ApiOperation({ summary: 'Gửi email hệ thống đơn giản để test' })
  @ApiResponse({
    status: 200,
    description: 'Email đã được thêm vào queue',
    schema: ApiResponseDto.getSchema(SystemEmailResponseDto)
  })
  @ApiErrorResponse(
    EMAIL_ERROR_CODES.EMAIL_SEND_FAILED,
    EMAIL_ERROR_CODES.INVALID_EMAIL_DATA
  )
  async sendSystemEmail(
    @Body() sendSystemEmailDto: SendSystemEmailDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<SystemEmailResponseDto>> {
    // Thêm thông tin employee để tracking
    const jobId = await this.systemEmailService.sendEmail({
      to: sendSystemEmailDto.to,
      subject: sendSystemEmailDto.subject,
      content: sendSystemEmailDto.content,
      cc: sendSystemEmailDto.cc,
      bcc: sendSystemEmailDto.bcc,
      attachments: sendSystemEmailDto.attachments,
      timestamp: Date.now(),
      from: sendSystemEmailDto.from
    });

    return ApiResponseDto.success({
      jobId: String(jobId),
      status: 'queued',
      timestamp: Date.now(),
      sentBy: employee.email
    });
  }

  /**
   * Gửi email hệ thống theo mẫu để test
   */
  @Post('send-template-email')
  @ApiOperation({ summary: 'Gửi email hệ thống theo mẫu để test' })
  @ApiResponse({
    status: 200,
    description: 'Email mẫu đã được thêm vào queue',
    schema: ApiResponseDto.getSchema(SystemEmailResponseDto)
  })
  @ApiErrorResponse(
    EMAIL_ERROR_CODES.EMAIL_SEND_FAILED,
    EMAIL_ERROR_CODES.INVALID_EMAIL_DATA,
    EMAIL_ERROR_CODES.TEMPLATE_NOT_FOUND
  )
  async sendSystemTemplateEmail(
    @Body() sendSystemTemplateEmailDto: SendSystemTemplateEmailDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<SystemEmailResponseDto>> {
    // Thêm thông tin employee để tracking
    const jobId = await this.systemEmailService.sendTemplateEmail({
      to: sendSystemTemplateEmailDto.to,
      templateId: sendSystemTemplateEmailDto.templateId,
      data: sendSystemTemplateEmailDto.data,
      cc: sendSystemTemplateEmailDto.cc,
      bcc: sendSystemTemplateEmailDto.bcc,
      timestamp: Date.now()
    });

    return ApiResponseDto.success({
      jobId: String(jobId),
      status: 'queued',
      timestamp: Date.now(),
      sentBy: employee.email
    });
  }
} 