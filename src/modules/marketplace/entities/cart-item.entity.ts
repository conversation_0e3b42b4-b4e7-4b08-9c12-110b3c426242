import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng cart_items trong cơ sở dữ liệu
 * Danh sách các sản phẩm trong giỏ hàng
 */
@Entity('cart_items')
export class CartItem {
  /**
   * Mã định danh cart item
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Mã giỏ hàng
   */
  @Column({ name: 'cart_id' })
  cartId: number;

  /**
   * Mã sản phẩm
   */
  @Column({ name: 'product_id' })
  productId: number;

  /**
   * Số lượng
   */
  @Column({ name: 'quantity' })
  quantity: number;

  /**
   * Thông tin sản phẩm
   * Thuộc tính này không được lưu trong cơ sở dữ liệu
   * Được sử dụng để lưu trữ kết quả join từ Product
   */
  product?: any;

  /**
   * Thông tin giỏ hàng
   * Thuộc tính này không được lưu trong cơ sở dữ liệu
   * Đư<PERSON>c sử dụng để lưu trữ kết quả join từ Cart
   */
  cart?: any;
}
