import { ApiProperty } from '@nestjs/swagger';
import { ProductDetailResponseDto } from './product-detail-response.dto';

/**
 * DTO cho response trả về danh sách sản phẩm có phân trang
 */
export class PaginatedProductResponseDto {
  @ApiProperty({
    description: 'Danh sách sản phẩm',
    type: [ProductDetailResponseDto],
  })
  items: ProductDetailResponseDto[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    type: 'object',
    properties: {
      totalItems: { 
        type: 'number', 
        example: 100,
        description: 'Tổng số sản phẩm'
      },
      itemCount: { 
        type: 'number', 
        example: 10,
        description: 'Số sản phẩm trên trang hiện tại'
      },
      itemsPerPage: { 
        type: 'number', 
        example: 10,
        description: 'Số sản phẩm trên mỗi trang'
      },
      totalPages: { 
        type: 'number', 
        example: 10,
        description: 'Tổng số trang'
      },
      currentPage: { 
        type: 'number', 
        example: 1,
        description: 'Trang hiện tại'
      },
    },
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
