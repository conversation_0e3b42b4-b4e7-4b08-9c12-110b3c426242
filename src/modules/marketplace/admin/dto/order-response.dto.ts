import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin người dùng trong response đơn hàng
 */
export class OrderUserInfoDto {
  @ApiProperty({
    description: 'ID của người dùng',
    example: 123,
  })
  id: number;

  @ApiProperty({
    description: 'Tên người dùng',
    example: 'Nguyễn Văn A',
  })
  name: string;

  @ApiProperty({
    description: 'Email người dùng',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Avatar của người dùng',
    example: 'https://example.com/avatar.jpg',
    nullable: true,
  })
  avatar: string | null;
}

/**
 * DTO cho thông tin chi tiết đơn hàng
 */
export class OrderLineDto {
  @ApiProperty({
    description: 'ID của chi tiết đơn hàng',
    example: 789,
  })
  id: number;

  @ApiProperty({
    description: 'ID của sản phẩm',
    example: 123,
  })
  productId: number;

  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Laptop XYZ',
  })
  productName: string;

  @ApiProperty({
    description: 'Số point của sản phẩm',
    example: 1000,
  })
  point: number;

  @ApiProperty({
    description: 'Số lượng',
    example: 2,
  })
  quantity: number;

  @ApiProperty({
    description: 'Phần trăm phí sàn',
    example: 5.0,
  })
  platformFeePercent: number;
}

/**
 * DTO cho response trả về thông tin đơn hàng
 */
export class OrderResponseDto {
  @ApiProperty({
    description: 'ID của đơn hàng',
    example: 456,
  })
  id: number;

  @ApiProperty({
    description: 'Thông tin người dùng',
    type: OrderUserInfoDto,
  })
  user: OrderUserInfoDto;

  @ApiProperty({
    description: 'Danh sách chi tiết đơn hàng',
    type: [OrderLineDto],
  })
  orderLines: OrderLineDto[];

  @ApiProperty({
    description: 'Tổng số point',
    example: 2000,
  })
  totalAmount: number;

  @ApiProperty({
    description: 'Thời gian tạo đơn hàng',
    example: 1625097600000,
  })
  createdAt: number;
}
