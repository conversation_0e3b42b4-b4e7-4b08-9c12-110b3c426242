import { plainToClass, plainToInstance } from 'class-transformer';
import { CreateProductResponseDto, ImageUploadUrlDto, ProductUploadUrlsDto } from '../../dto/create-product-response.dto';
import { ProductDetailResponseDto } from '../../dto/product-detail-response.dto';
import { ProductStatus } from '@modules/marketplace/enums';

describe('CreateProductResponseDto', () => {
  describe('ImageUploadUrlDto', () => {
    it('phải chuyển đổi dữ liệu URL upload ảnh thành DTO hợp lệ', () => {
      // Arrange
      const imageUrlData = {
        url: 'https://example.com/upload/image1.jpg',
        key: 'marketplace/IMAGE/2023/01/01/product-image-0-123456789-1',
        index: 0,
      };

      // Act
      const imageUrlDto = plainToInstance(ImageUploadUrlDto, imageUrlData);

      // Assert
      expect(imageUrlDto).toBeInstanceOf(ImageUploadUrlDto);
      expect(imageUrlDto.url).toBe('https://example.com/upload/image1.jpg');
      expect(imageUrlDto.key).toBe('marketplace/IMAGE/2023/01/01/product-image-0-123456789-1');
      expect(imageUrlDto.index).toBe(0);
    });
  });

  describe('ProductUploadUrlsDto', () => {
    it('phải chuyển đổi dữ liệu URL upload sản phẩm thành DTO hợp lệ với đầy đủ thông tin', () => {
      // Arrange
      const uploadUrlsData = {
        productId: '123',
        imagesUploadUrls: [
          {
            url: 'https://example.com/upload/image1.jpg',
            key: 'marketplace/IMAGE/2023/01/01/product-image-0-123456789-1',
            index: 0,
          },
          {
            url: 'https://example.com/upload/image2.jpg',
            key: 'marketplace/IMAGE/2023/01/01/product-image-1-123456789-2',
            index: 1,
          },
        ],
        userManualUploadUrl: 'https://example.com/upload/manual.pdf',
        detailUploadUrl: 'https://example.com/upload/detail.pdf',
      };

      // Act
      const uploadUrlsDto = plainToInstance(ProductUploadUrlsDto, uploadUrlsData);

      // Assert
      expect(uploadUrlsDto).toBeInstanceOf(ProductUploadUrlsDto);
      expect(uploadUrlsDto.productId).toBe('123');
      expect(uploadUrlsDto.imagesUploadUrls).toHaveLength(2);
      expect(uploadUrlsDto.imagesUploadUrls[0].url).toBe('https://example.com/upload/image1.jpg');
      expect(uploadUrlsDto.imagesUploadUrls[1].index).toBe(1);
      expect(uploadUrlsDto.userManualUploadUrl).toBe('https://example.com/upload/manual.pdf');
      expect(uploadUrlsDto.detailUploadUrl).toBe('https://example.com/upload/detail.pdf');
    });

    it('phải chuyển đổi dữ liệu URL upload sản phẩm thành DTO hợp lệ khi không có URL upload tài liệu', () => {
      // Arrange
      const uploadUrlsData = {
        productId: '123',
        imagesUploadUrls: [
          {
            url: 'https://example.com/upload/image1.jpg',
            key: 'marketplace/IMAGE/2023/01/01/product-image-0-123456789-1',
            index: 0,
          },
        ],
      };

      // Act
      const uploadUrlsDto = plainToInstance(ProductUploadUrlsDto, uploadUrlsData);

      // Assert
      expect(uploadUrlsDto).toBeInstanceOf(ProductUploadUrlsDto);
      expect(uploadUrlsDto.productId).toBe('123');
      expect(uploadUrlsDto.imagesUploadUrls).toHaveLength(1);
      expect(uploadUrlsDto.userManualUploadUrl).toBeUndefined();
      expect(uploadUrlsDto.detailUploadUrl).toBeUndefined();
    });
  });

  describe('CreateProductResponseDto', () => {
    it('phải chuyển đổi dữ liệu response tạo sản phẩm thành DTO hợp lệ', () => {
      // Arrange
      const mockProductDetail = {
        id: 123,
        name: 'AI Chatbot Template',
        description: 'Mẫu chatbot AI hỗ trợ khách hàng tự động',
        listedPrice: 1200,
        discountedPrice: 1000,
        category: 'KNOWLEDGE_FILE',
        status: ProductStatus.DRAFT,
        images: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
        userManual: 'https://example.com/manual.pdf',
        detail: 'https://example.com/detail.pdf',
      };

      const responseData = {
        product: mockProductDetail,
        uploadUrls: {
          productId: '123',
          imagesUploadUrls: [
            {
              url: 'https://example.com/upload/image1.jpg',
              key: 'marketplace/IMAGE/2023/01/01/product-image-0-123456789-1',
              index: 0,
            },
            {
              url: 'https://example.com/upload/image2.jpg',
              key: 'marketplace/IMAGE/2023/01/01/product-image-1-123456789-2',
              index: 1,
            },
          ],
          userManualUploadUrl: 'https://example.com/upload/manual.pdf',
          detailUploadUrl: 'https://example.com/upload/detail.pdf',
        },
      };

      // Act
      const responseDto = plainToInstance(CreateProductResponseDto, responseData);

      // Assert
      expect(responseDto).toBeInstanceOf(CreateProductResponseDto);
      expect(responseDto.product).toBeDefined();
      expect(responseDto.product.id).toBe(123);
      expect(responseDto.product.name).toBe('AI Chatbot Template');
      expect(responseDto.uploadUrls.productId).toBe('123');
      expect(responseDto.uploadUrls.imagesUploadUrls).toHaveLength(2);
    });
  });
});
