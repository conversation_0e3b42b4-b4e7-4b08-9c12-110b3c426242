# Module Marketplace

## Tổng quan

Module Marketplace cung cấp các chức năng quản lý chợ sản phẩm trong hệ thống. Module này cho phép người dùng đăng bán, mua và quản lý các s<PERSON>h<PERSON>, dịch vụ và tài nguyên liên quan đến AI.

## Cấu trúc module

```
marketplace/
├── admin/                  # Chức năng quản lý dành cho admin
│   ├── controllers/        # Controllers xử lý request từ admin
│   └── services/           # Services xử lý logic nghiệp vụ cho admin
├── user/                   # Chức năng dành cho người dùng
│   ├── controllers/        # Controllers xử lý request từ người dùng
│   └── services/           # Services xử lý logic nghiệp vụ cho người dùng
├── entities/               # Entities mapping với database
├── repositories/           # Repositories tương tác với database
└── marketplace.module.ts   # Module definition
```

## Các entity chính

1. **Product**: Sản phẩm trong marketplace
2. **Category**: <PERSON><PERSON> mục sản phẩm
3. **Review**: Đ<PERSON><PERSON> gi<PERSON> sản phẩm
4. **Order**: Đơn hàng
5. **Transaction**: Giao dịch
6. **Seller**: Thông tin người bán
7. **ProductMedia**: Tài nguyên media của sản phẩm

## Chức năng chính

### Quản lý sản phẩm
- Đăng bán sản phẩm mới
- Cập nhật thông tin sản phẩm
- Quản lý danh mục sản phẩm
- Tìm kiếm và lọc sản phẩm

### Quản lý đơn hàng
- Đặt hàng sản phẩm
- Theo dõi trạng thái đơn hàng
- Xử lý hoàn tiền và hủy đơn hàng

### Quản lý đánh giá
- Đánh giá sản phẩm
- Phản hồi đánh giá
- Quản lý xếp hạng sản phẩm

### Quản lý giao dịch
- Xử lý thanh toán
- Quản lý doanh thu
- Rút tiền và thanh toán cho người bán

## API Endpoints

### User Endpoints

- `GET /marketplace/products` - Lấy danh sách sản phẩm
- `GET /marketplace/products/:id` - Lấy thông tin chi tiết sản phẩm
- `POST /marketplace/products` - Đăng bán sản phẩm mới
- `PUT /marketplace/products/:id` - Cập nhật thông tin sản phẩm
- `DELETE /marketplace/products/:id` - Xóa sản phẩm
- `GET /marketplace/categories` - Lấy danh sách danh mục
- `POST /marketplace/orders` - Đặt hàng sản phẩm
- `GET /marketplace/orders` - Lấy danh sách đơn hàng
- `GET /marketplace/orders/:id` - Lấy thông tin chi tiết đơn hàng
- `POST /marketplace/reviews` - Đăng đánh giá sản phẩm
- `GET /marketplace/seller/dashboard` - Lấy thông tin dashboard người bán

### Admin Endpoints

- `GET /admin/marketplace/products` - Lấy danh sách sản phẩm
- `PUT /admin/marketplace/products/:id/approve` - Phê duyệt sản phẩm
- `PUT /admin/marketplace/products/:id/reject` - Từ chối sản phẩm
- `GET /admin/marketplace/orders` - Lấy danh sách đơn hàng
- `GET /admin/marketplace/transactions` - Lấy danh sách giao dịch
- `GET /admin/marketplace/sellers` - Lấy danh sách người bán
- `GET /admin/marketplace/reviews` - Lấy danh sách đánh giá
- `DELETE /admin/marketplace/reviews/:id` - Xóa đánh giá

## Cách sử dụng

### Đăng bán sản phẩm

```typescript
// Đăng bán sản phẩm mới
const product = await marketplaceService.createProduct({
  name: 'AI Chatbot Template',
  description: 'A ready-to-use chatbot template for customer service',
  price: 29.99,
  categoryId: 1,
  mediaIds: [1, 2, 3],
  tags: ['chatbot', 'customer service', 'template']
});
```

### Đặt hàng sản phẩm

```typescript
// Đặt hàng sản phẩm
const order = await marketplaceService.createOrder({
  productId: 1,
  quantity: 1,
  paymentMethod: 'credit_card',
  paymentDetails: {
    cardNumber: '****************',
    expiryMonth: 12,
    expiryYear: 2025,
    cvv: '123'
  }
});
```

### Đánh giá sản phẩm

```typescript
// Đánh giá sản phẩm
const review = await marketplaceService.createReview({
  productId: 1,
  rating: 5,
  comment: 'Great product, easy to use and customize!'
});
```

## Liên kết với các module khác

- **User Module**: Quản lý thông tin người dùng
- **Auth Module**: Xác thực và phân quyền
- **Payment Module**: Xử lý thanh toán
- **Agent Module**: Tích hợp với agent AI
