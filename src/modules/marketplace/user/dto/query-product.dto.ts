import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsNumber, IsOptional, Min } from 'class-validator';
import { ProductCategory } from '@modules/marketplace/enums';
import { QueryDto } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp sản phẩm
 */
export enum ProductSortField {
  NAME = 'name',
  LISTED_PRICE = 'listedPrice',
  DISCOUNTED_PRICE = 'discountedPrice',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * DTO cho các tham số truy vấn danh sách sản phẩm
 */
export class QueryProductDto extends QueryDto {
  @ApiProperty({
    description: 'Lọc theo loại sản phẩm',
    enum: ProductCategory,
    example: ProductCategory.AGENT,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductCategory)
  category?: ProductCategory;

  @ApiProperty({
    description: 'Gi<PERSON> tối thiểu',
    example: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(0)
  minPrice?: number;

  @ApiProperty({
    description: 'Giá tối đa',
    example: 1000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(0)
  maxPrice?: number;

  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: ProductSortField,
    example: ProductSortField.CREATED_AT,
    default: ProductSortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProductSortField)
  sortBy: ProductSortField = ProductSortField.CREATED_AT;
}
