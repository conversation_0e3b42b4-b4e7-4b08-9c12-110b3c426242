import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thông tin người bán trong lịch sử mua hàng
 */
export class PurchaseHistorySellerDto {
  @ApiProperty({
    description: 'ID của người bán',
    example: 456,
  })
  id: number;

  @ApiProperty({
    description: 'Tên người bán',
    example: 'Nguyễn Văn A',
  })
  name: string;

  @ApiProperty({
    description: 'Avatar của người bán',
    example: 'https://example.com/avatar.jpg',
    nullable: true,
  })
  avatar: string | null;

  @ApiProperty({
    description: 'Email của người bán',
    example: '<EMAIL>',
    nullable: true,
  })
  email: string | null;

  @ApiProperty({
    description: 'Số điện thoại của người bán',
    example: '0987654321',
    nullable: true,
  })
  phoneNumber: string | null;
}

/**
 * DTO cho thông tin đơn hàng trong lịch sử mua hàng
 */
export class PurchaseHistoryItemDto {
  @ApiProperty({
    description: 'ID của đơn hàng',
    example: 101,
  })
  orderId: number;

  @ApiProperty({
    description: 'ID của sản phẩm',
    example: 123,
  })
  productId: number;

  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Laptop XYZ',
  })
  productName: string;

  @ApiProperty({
    description: 'Giá đã thanh toán',
    example: 1000,
  })
  discountedPrice: number;

  @ApiProperty({
    description: 'Số lượng',
    example: 2,
  })
  quantity: number;

  @ApiProperty({
    description: 'Phần trăm phí sàn',
    example: 5.0,
  })
  platformFeePercent: number;

  @ApiProperty({
    description: 'Giá người bán nhận được sau khi trừ phí sàn',
    example: 950,
  })
  sellerReceivePrice: number;

  @ApiProperty({
    description: 'Thông tin người bán',
    type: PurchaseHistorySellerDto,
  })
  seller: PurchaseHistorySellerDto;

  @ApiProperty({
    description: 'Thời gian mua',
    example: 1625097600000,
  })
  createdAt: number;
}
