import { Injectable, Logger } from '@nestjs/common';
import { MarketOrderRepository } from '@modules/marketplace/repositories';
import { QueryPurchaseHistoryDto, CombinedPurchaseHistoryResponseDto } from '../dto';
import { PurchaseHistoryHelper } from '@modules/marketplace/helpers/purchase-history.helper';
import { AppException } from '@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';
import { Transactional } from 'typeorm-transactional';

/**
 * Service xử lý các thao tác liên quan đến đơn hàng cho người dùng
 */
@Injectable()
export class OrderUserService {
  private readonly logger = new Logger(OrderUserService.name);

  constructor(
    private readonly marketOrderRepository: MarketOrderRepository,
    private readonly purchaseHistoryHelper: PurchaseHistoryHelper
  ) {}

  /**
   * L<PERSON><PERSON> lịch sử mua hàng của người dùng
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách lịch sử mua hàng với phân trang
   */
  @Transactional()
  async getPurchaseHistory(
    userId: number,
    queryDto: QueryPurchaseHistoryDto
  ): Promise<CombinedPurchaseHistoryResponseDto> {
    try {
      this.logger.log(`Getting purchase history for user ${userId}`);

      // Lấy danh sách đơn hàng từ repository
      const paginatedOrders = await this.marketOrderRepository.findPurchaseHistory(
        userId,
        queryDto
      );

      // Chuyển đổi sang DTO
      return this.purchaseHistoryHelper.mapToPaginatedPurchaseHistory(paginatedOrders);
    } catch (error) {
      this.logger.error(`Error getting purchase history for user ${userId}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        MARKETPLACE_ERROR_CODES.PURCHASE_HISTORY_ERROR,
        `Không thể lấy lịch sử mua hàng: ${error.message}`
      );
    }
  }
}
