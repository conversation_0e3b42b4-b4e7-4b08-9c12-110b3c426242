import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { MarketOrderLine } from '@modules/marketplace/entities';
import { Product } from '../entities/product.entity';
import { User } from '@modules/user/entities';
import { Employee } from '@modules/employee/entities';

/**
 * <PERSON>ho lưu trữ tùy chỉnh cho chi tiết đơn hàng
 */
@Injectable()
export class MarketOrderLineRepository extends Repository<MarketOrderLine> {
  private readonly logger = new Logger(MarketOrderLineRepository.name);

  constructor(private dataSource: DataSource) {
    super(MarketOrderLine, dataSource.createEntityManager());
  }

  /**
   * Tạo truy vấn cơ bản cho chi tiết đơn hàng
   * @returns QueryBuilder cho chi tiết đơn hàng
   */
  private createBaseQuery(): SelectQueryBuilder<MarketOrderLine> {
    return this.createQueryBuilder('orderLine');
  }

  /**
   * Tìm chi tiết đơn hàng theo ID
   * @param id ID chi tiết đơn hàng
   * @returns Chi tiết đơn hàng hoặc null
   */
  async findById(id: number): Promise<MarketOrderLine | null> {
    return this.createBaseQuery()
      .where('orderLine.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm chi tiết đơn hàng theo ID đơn hàng
   * @param orderId ID đơn hàng
   * @returns Danh sách chi tiết đơn hàng
   */
  async findByOrderId(orderId: number): Promise<MarketOrderLine[]> {
    this.logger.debug(`Finding order lines for order ${orderId}`);

    try {
      // Sử dụng raw query để lấy đầy đủ thông tin
      const query = this.dataSource
        .createQueryBuilder()
        .select('ol.id', 'id')
        .addSelect('ol.order_id', 'orderId')
        .addSelect('ol.product_id', 'productId')
        .addSelect('ol.quantity', 'quantity')
        .addSelect('ol.point', 'point')
        .addSelect('ol.product_name', 'productName')
        .addSelect('ol.platform_fee_percent', 'platformFeePercent')
        .addSelect('ol.seller_receive_price', 'sellerReceivePrice')
        .addSelect('ol.created_at', 'createdAt')
        .addSelect('ol.updated_at', 'updatedAt')
        .addSelect('p.id', 'product_id')
        .addSelect('p.name', 'product_name')
        .addSelect('p.description', 'product_description')
        .addSelect('p.listed_price', 'product_listedPrice')
        .addSelect('p.discounted_price', 'product_discountedPrice')
        .addSelect('p.images', 'product_images')
        .addSelect('p.status', 'product_status')
        .addSelect('p.category', 'product_category')
        .addSelect('u.id', 'user_id')
        .addSelect('u.full_name', 'user_fullName')
        .addSelect('u.email', 'user_email')
        .addSelect('u.avatar', 'user_avatar')
        .addSelect('e.id', 'employee_id')
        .addSelect('e.full_name', 'employee_fullName')
        .addSelect('e.email', 'employee_email')
        .from('market_order_line', 'ol')
        .leftJoin('products', 'p', 'p.id = ol.product_id')
        .leftJoin('users', 'u', 'u.id = p.user_id')
        .leftJoin('employees', 'e', 'e.id = p.employee_id')
        .where('ol.order_id = :orderId', { orderId });

      const orderLinesRaw = await query.getRawMany();
      this.logger.debug(`Found ${orderLinesRaw.length} order lines for order ID: ${orderId}`);

      if (orderLinesRaw.length === 0) {
        return [];
      }

      // Chuyển đổi raw data thành MarketOrderLine objects
      const orderLines: MarketOrderLine[] = orderLinesRaw.map(raw => {
        const orderLine = new MarketOrderLine();
        orderLine.id = raw.id;
        orderLine.orderId = raw.orderId;
        orderLine.productId = raw.productId;
        orderLine.quantity = raw.quantity;
        orderLine.point = raw.point;
        orderLine.productName = raw.productName;
        orderLine.platformFeePercent = raw.platformFeePercent;
        orderLine.sellerReceivePrice = raw.sellerReceivePrice;
        orderLine.createdAt = raw.createdAt;
        orderLine.updatedAt = raw.updatedAt;

        // Gán thông tin sản phẩm
        orderLine.product = {
          id: raw.product_id,
          name: raw.product_name,
          description: raw.product_description,
          listedPrice: raw.product_listedPrice,
          discountedPrice: raw.product_discountedPrice,
          images: raw.product_images,
          status: raw.product_status,
          category: raw.product_category,
          user: {
            id: raw.user_id,
            fullName: raw.user_fullName,
            email: raw.user_email,
            avatar: raw.user_avatar
          },
          employee: {
            id: raw.employee_id,
            fullName: raw.employee_fullName,
            email: raw.employee_email
          }
        };

        return orderLine;
      });

      // Log thông tin chi tiết về order line đầu tiên để debug
      if (orderLines.length > 0) {
        const firstItem = orderLines[0];
        this.logger.debug(`Order line đầu tiên: ID=${firstItem.id}, ProductID=${firstItem.productId}, ProductName=${firstItem.product?.name || 'N/A'}`);
      }

      return orderLines;
    } catch (error) {
      this.logger.error(`Error finding order lines for order ${orderId}: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Tạo chi tiết đơn hàng từ giỏ hàng
   * @param orderLine Thông tin chi tiết đơn hàng
   * @returns Chi tiết đơn hàng đã tạo
   */
  async createOrderLine(orderLine: Partial<MarketOrderLine>): Promise<MarketOrderLine> {
    const newOrderLine = this.create({
      ...orderLine,
      createdAt: Math.floor(Date.now()),
      updatedAt: Math.floor(Date.now()),
    });

    return this.save(newOrderLine);
  }

  /**
   * Tạo nhiều chi tiết đơn hàng cùng lúc
   * @param orderLines Danh sách thông tin chi tiết đơn hàng
   * @returns Danh sách chi tiết đơn hàng đã tạo
   */
  async createOrderLines(orderLines: Partial<MarketOrderLine>[]): Promise<MarketOrderLine[]> {
    const timestamp = Math.floor(Date.now());
    const newOrderLines = orderLines.map(orderLine => this.create({
      ...orderLine,
      createdAt: timestamp,
      updatedAt: timestamp,
    }));

    return this.save(newOrderLines);
  }
}
