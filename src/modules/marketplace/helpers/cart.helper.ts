import { Injectable, Logger } from '@nestjs/common';
import { Cart, CartItem } from '../entities';
import { CartResponseDto, CartItemResponseDto } from '../user/dto';
import { ProductStatus } from '@modules/marketplace/enums';
import { CartAdminResponseDto } from '../admin/dto';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { User } from '@modules/user/entities';
import { CartItemRepository } from '../repositories/cart-item.repository';

/**
 * Helper class cho việc xử lý và chuyển đổi dữ liệu giỏ hàng
 */
@Injectable()
export class CartHelper {
  private readonly logger = new Logger(CartHelper.name);

  constructor(
    private readonly userRepository: UserRepository,
    private readonly cartItemRepository: CartItemRepository
  ) {}
  /**
   * Chuyển đổi entity Cart thành DTO CartResponseDto
   * @param cart Entity Cart với các CartItem đã được join
   * @returns CartResponseDto
   */
  mapToCartResponseDto(cart: Cart): CartResponseDto {
    // Kiểm tra xem cart có tồn tại không
    if (!cart) {
      return {
        items: [],
        totalValue: 0,
        createdAt: Date.now(),
        updatedAt: Date.now()
      };
    }

    // Lấy danh sách cart items từ kết quả join thủ công
    const cartItems = this.extractCartItems(cart);

    if (!cartItems || cartItems.length === 0) {
      return {
        items: [],
        totalValue: 0,
        createdAt: cart.created_at || Date.now(),
        updatedAt: cart.updated_at || Date.now()
      };
    }

    const items = cartItems.map(item => this.mapToCartItemResponseDto(item));
    const totalValue = this.calculateTotalValue(cartItems);

    return {
      items,
      totalValue,
      createdAt: cart.created_at || Date.now(),
      updatedAt: cart.updated_at || Date.now()
    };
  }

  /**
   * Chuyển đổi entity CartItem thành DTO CartItemResponseDto
   * @param cartItem Entity CartItem với Product đã được join
   * @returns CartItemResponseDto
   */
  /**
   * Trích xuất danh sách CartItem từ kết quả join thủ công
   * @param cart Cart với các trường đã được join
   * @returns Danh sách CartItem đã được xử lý
   */
  private extractCartItems(cart: any): CartItem[] {
    const cartItems: CartItem[] = [];

    // Kiểm tra xem có dữ liệu cartItems không
    if (!cart.cartItems || cart.cartItems.length === 0) {
      return cartItems;
    }

    // Xử lý mảng cartItems
    for (const item of cart.cartItems) {
      const cartItem = {
        id: item.cartItems_id,
        cartId: cart.id,
        productId: item.cartItems_product_id,
        quantity: item.cartItems_quantity,
        product: {
          id: item.product_id,
          name: item.product_name,
          description: item.product_description,
          listedPrice: item.product_listed_price,
          discountedPrice: item.product_discounted_price,
          images: item.product_images,
          status: item.product_status,
          user: {
            id: item.user_id,
            fullName: item.user_full_name,
            email: item.user_email,
            avatar: item.user_avatar
          },
          employee: {
            id: item.employee_id,
            fullName: item.employee_full_name,
            email: item.employee_email,
            avatar: null // Không có trường avatar cho employee trong truy vấn
          }
        }
      } as unknown as CartItem;

      cartItems.push(cartItem);
    }

    return cartItems;
  }

  mapToCartItemResponseDto(cartItem: any): CartItemResponseDto {
    if (!cartItem || !cartItem.product) {
      throw new Error('Cannot map null cart item to DTO');
    }

    const product = cartItem.product;
    let sellerName = 'Unknown';

    if (product.user && product.user.fullName) {
      sellerName = product.user.fullName || product.user.email;
    } else if (product.employee && product.employee.fullName) {
      sellerName = product.employee.fullName || product.employee.email;
    }

    // Chỉ hiển thị sản phẩm có trạng thái APPROVED
    if (product.status !== ProductStatus.APPROVED) {
      throw new Error(`Product with ID ${product.id} is not in APPROVED status`);
    }

    return {
      cartItemId: cartItem.id,
      productId: product.id,
      productName: product.name,
      discountedPrice: product.discountedPrice,
      quantity: cartItem.quantity,
      sellerName,
      createdAt: product.createdAt || Date.now(), // Sử dụng thời gian tạo của sản phẩm hoặc thời gian hiện tại
    };
  }

  /**
   * Tính tổng giá trị giỏ hàng
   * @param cartItems Danh sách CartItem với Product đã được join
   * @returns Tổng giá trị
   */
  calculateTotalValue(cartItems: any[]): number {
    return cartItems.reduce((total, item) => {
      if (item && item.product && item.product.discountedPrice) {
        return total + (item.product.discountedPrice * item.quantity);
      }
      return total;
    }, 0);
  }

  /**
   * Chuyển đổi entity Cart thành DTO CartAdminResponseDto cho admin
   * @param cart Entity Cart với các CartItem đã được join
   * @returns CartAdminResponseDto
   */
  async mapToCartAdminResponseDto(cart: Cart): Promise<CartAdminResponseDto> {
    if (!cart) {
      throw new Error('Cannot map null cart to DTO');
    }

    // Log cart object keys for debugging
    this.logger.debug(`Cart object keys: ${Object.keys(cart).join(', ')}`);

    // Lấy danh sách cart items trực tiếp từ CartItemRepository
    this.logger.debug(`Fetching cart items for cart ID: ${cart.id}`);
    const cartItemsFromRepo = await this.cartItemRepository.findApprovedItemsByCartId(cart.id);
    this.logger.debug(`Found ${cartItemsFromRepo.length} cart items from repository`);

    // Map cart items to DTOs - filter out null values
    const items = cartItemsFromRepo
      .map(item => this.mapToCartItemAdminResponseDto(item))
      .filter(item => item !== null) || [];

    this.logger.debug(`Mapped ${items.length} cart items to DTOs`);
    const totalValue = this.calculateTotalValue(cartItemsFromRepo || []);

    // Lấy thông tin người dùng trực tiếp từ UserRepository
    const userId = cart.user_id || cart.userId;
    this.logger.debug(`Fetching user information for userId: ${userId}`);

    // Lấy thông tin người dùng từ repository
    let userInfo: User | null = null;
    try {
      userInfo = await this.userRepository.findById(userId);
      this.logger.debug(`User info from repository: ${userInfo ? JSON.stringify(userInfo) : 'null'}`);
    } catch (error) {
      this.logger.error(`Error fetching user info: ${error.message}`, error.stack);
    }

    // Tạo thông tin người dùng cho DTO
    const user = {
      id: userId,
      fullName: userInfo?.fullName || 'Unknown',
      email: userInfo?.email || '<EMAIL>',
      avatar: userInfo?.avatar || null
    };

    // Log user info for debugging
    this.logger.debug(`Final user info: id=${user.id}, name=${user.fullName}, email=${user.email}`);
    this.logger.debug(`Cart items count: ${items.length}`);


    return {
      id: cart.id,
      user: {
        id: user.id,
        name: user.fullName || 'Unknown',
        email: user.email || '<EMAIL>',
        avatar: user.avatar || null,
      },
      items,
      totalValue,
      createdAt: cart.created_at || Date.now(),
      updatedAt: cart.updated_at || Date.now(),
    };
  }

  /**
   * Chuyển đổi entity CartItem thành DTO cho admin
   * @param cartItem Entity CartItem với Product đã được join
   * @returns Đối tượng DTO cho admin
   */
  mapToCartItemAdminResponseDto(cartItem: any): any {
    if (!cartItem) {
      this.logger.warn('Cannot map null cart item to DTO');
      return null;
    }

    if (!cartItem.product) {
      this.logger.warn(`Cart item ID ${cartItem.id} has no product information`);
      return {
        id: cartItem.id,
        product: {
          id: cartItem.productId,
          name: 'Unknown Product',
          discountedPrice: 0,
          category: null,
          seller: {
            id: 0,
            name: 'Unknown',
            type: 'user',
          },
        },
        quantity: cartItem.quantity,
        totalValue: 0,
      };
    }

    const product = cartItem.product;
    this.logger.debug(`Mapping product: ${JSON.stringify(product)}`);

    let seller = {
      id: 0,
      name: 'Unknown',
      type: 'user',
    };

    // Kiểm tra dữ liệu user từ join thủ công
    if (product.user && product.user.id) {
      seller = {
        id: product.user.id,
        name: product.user.fullName || product.user.email || 'Unknown',
        type: 'user',
      };
    }
    // Kiểm tra dữ liệu employee từ join thủ công
    else if (product.employee && product.employee.id) {
      seller = {
        id: product.employee.id,
        name: product.employee.fullName || product.employee.email || 'Unknown',
        type: 'employee',
      };
    }

    const discountedPrice = product.discountedPrice || 0;

    return {
      id: cartItem.id,
      product: {
        id: product.id,
        name: product.name || 'Unknown Product',
        discountedPrice: discountedPrice,
        category: product.category,
        seller,
      },
      quantity: cartItem.quantity || 0,
      totalValue: discountedPrice * (cartItem.quantity || 0),
    };
  }
}
