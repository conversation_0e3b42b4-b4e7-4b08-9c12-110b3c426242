# Module Subscription

## Tổng quan

Module Subscription cung cấp các chức năng quản lý gói dịch vụ và đăng ký trong hệ thống. Module này cho phép người dùng đăng ký, <PERSON><PERSON><PERSON> cấ<PERSON>, h<PERSON> cấp và quản lý các gói dịch vụ, cũng như theo dõi việc sử dụng và thanh toán.

## Cấu trúc module

```
subscription/
├── admin/                  # Chức năng quản lý dành cho admin
│   ├── controllers/        # Controllers xử lý request từ admin
│   └── services/           # Services xử lý logic nghiệp vụ cho admin
├── user/                   # Chức năng dành cho người dùng
│   ├── controllers/        # Controllers xử lý request từ người dùng
│   └── services/           # Services xử lý logic nghiệp vụ cho người dùng
├── entities/               # Entities mapping với database
├── repositories/           # Repositories tương tác với database
└── subscription.module.ts  # Module definition
```

## Các entity chính

1. **Plan**: <PERSON><PERSON><PERSON> nghĩa các gói dịch vụ
2. **PlanPricing**: Giá của các gói dịch vụ
3. **Subscription**: Đăng ký gói dịch vụ của người dùng
4. **UsageLog**: Log sử dụng dịch vụ
5. **OrderPlanHistory**: Lịch sử đặt hàng gói dịch vụ

## Chức năng chính

### Quản lý gói dịch vụ
- Tạo, cập nhật, xóa gói dịch vụ
- Quản lý giá và tính năng của gói dịch vụ
- Quản lý chu kỳ thanh toán

### Quản lý đăng ký
- Đăng ký gói dịch vụ mới
- Nâng cấp/hạ cấp gói dịch vụ
- Hủy đăng ký
- Gia hạn tự động

### Quản lý sử dụng
- Theo dõi việc sử dụng dịch vụ
- Giới hạn sử dụng theo gói
- Thông báo khi sắp đạt giới hạn

### Quản lý thanh toán
- Xử lý thanh toán
- Lịch sử thanh toán
- Hóa đơn và biên lai

## API Endpoints

### User Endpoints

- `GET /subscription/plans` - Lấy danh sách gói dịch vụ
- `GET /subscription/plans/:id` - Lấy thông tin chi tiết gói dịch vụ
- `GET /subscription/my-subscription` - Lấy thông tin đăng ký hiện tại
- `POST /subscription/subscribe` - Đăng ký gói dịch vụ mới
- `PUT /subscription/change-plan` - Thay đổi gói dịch vụ
- `DELETE /subscription/cancel` - Hủy đăng ký
- `GET /subscription/usage` - Lấy thông tin sử dụng
- `GET /subscription/payment-history` - Lấy lịch sử thanh toán

### Admin Endpoints

- `GET /admin/subscription/plans` - Lấy danh sách gói dịch vụ
- `POST /admin/subscription/plans` - Tạo gói dịch vụ mới
- `PUT /admin/subscription/plans/:id` - Cập nhật gói dịch vụ
- `DELETE /admin/subscription/plans/:id` - Xóa gói dịch vụ
- `GET /admin/subscription/subscriptions` - Lấy danh sách đăng ký
- `GET /admin/subscription/usage-logs` - Lấy log sử dụng
- `GET /admin/subscription/payment-history` - Lấy lịch sử thanh toán

## Cách sử dụng

### Đăng ký gói dịch vụ

```typescript
// Đăng ký gói dịch vụ mới
const subscription = await subscriptionService.subscribe({
  userId: 123,
  planId: 1,
  paymentMethod: 'credit_card',
  paymentDetails: {
    cardNumber: '****************',
    expiryMonth: 12,
    expiryYear: 2025,
    cvv: '123'
  }
});
```

### Kiểm tra giới hạn sử dụng

```typescript
// Kiểm tra xem người dùng có thể sử dụng tính năng không
const canUseFeature = await subscriptionService.canUseFeature(userId, 'feature_name');
```

### Ghi log sử dụng

```typescript
// Ghi log sử dụng
await usageLogService.logUsage({
  userId: 123,
  featureName: 'api_calls',
  quantity: 1
});
```

## Liên kết với các module khác

- **User Module**: Quản lý thông tin người dùng
- **Auth Module**: Xác thực và phân quyền
- **Payment Module**: Xử lý thanh toán
