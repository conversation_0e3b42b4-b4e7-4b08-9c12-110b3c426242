import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Plan } from '../../entities/plan.entity';
import { PlanRepository } from '../../repositories/plan.repository';
import { PlanPricing } from '@modules/subscription/entities';
import {QueryDto, SortDirection} from "@common/dto";
import {PaginatedResult} from "@common/response/api-response-dto";

@Injectable()
export class PlanUserService {
  constructor(
    @InjectRepository(Plan)
    private readonly planRepository: Repository<Plan>,
    @InjectRepository(PlanPricing)
    private readonly planPricingRepository: Repository<PlanPricing>,
    private readonly planCustomRepository: PlanRepository
  ) {}

  /**
   * Lấy danh sách gói dịch vụ
   * @param page Số trang
   * @param limit Số lượng bản ghi trên mỗi trang
   * @returns Danh sách gói dịch vụ đã phân trang
   */
  async findPlans(page: number = 1, limit: number = 10): Promise<PaginatedResult<Plan>> {
    // Tạo tham số phân trang
    const paginationParams: QueryDto = {
      page,
      limit,
      sortBy: 'createdAt',
      sortDirection: SortDirection.DESC
    };

    // Lấy danh sách gói dịch vụ
    return this.planCustomRepository.findPlans(paginationParams);
  }

  /**
   * Lấy chi tiết gói dịch vụ theo ID
   * @param id ID của gói dịch vụ
   * @returns Chi tiết gói dịch vụ
   */
  async findPlanById(id: number): Promise<Plan> {
    const plan = await this.planCustomRepository.findPlanById(id);

    if (!plan) {
      throw new NotFoundException(`Plan with ID ${id} not found`);
    }

    return plan;
  }

  /**
   * Lấy danh sách tùy chọn giá của gói dịch vụ
   * @param planId ID của gói dịch vụ
   * @returns Danh sách tùy chọn giá
   */
  async findPlanPricingByPlanId(planId: number): Promise<PlanPricing[]> {
    // Kiểm tra gói dịch vụ có tồn tại không
    const plan = await this.findPlanById(planId);

    if (!plan) {
      throw new NotFoundException(`Plan with ID ${planId} not found`);
    }

    // Lấy danh sách tùy chọn giá
    return this.planPricingRepository.find({
      where: { planId, isActive: true },
      order: { createdAt: 'DESC' }
    });
  }
}
