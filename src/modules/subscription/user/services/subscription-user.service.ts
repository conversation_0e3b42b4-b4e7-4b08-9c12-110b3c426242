import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In } from 'typeorm';
import { Subscription } from '@modules/subscription/entities';
import { PlanPricing } from '@modules/subscription/entities';
import { Plan } from '@modules/subscription/entities';
import { UsageLog } from '@modules/subscription/entities';
import { OrderPlanHistory } from '@modules/subscription/entities';
import { SubscriptionRepository } from '@modules/subscription/repositories';
import { UsageLogRepository } from '@modules/subscription/repositories';
import { PlanInfoDto, PricingInfoDto } from '@modules/subscription/dto';
import { Transactional } from 'typeorm-transactional';
import {PackageType, SubscriptionStatus} from "@modules/subscription/enums";
import {User} from "@modules/user/entities";
import {OrderStatus} from "@modules/subscription/enums/order-status.enum";
import {PaginatedResult} from "@common/response/api-response-dto";
import {QueryDto, SortDirection} from "@common/dto";

@Injectable()
export class SubscriptionUserService {
  constructor(
    @InjectRepository(Subscription)
    private readonly subscriptionRepository: Repository<Subscription>,
    @InjectRepository(PlanPricing)
    private readonly planPricingRepository: Repository<PlanPricing>,
    @InjectRepository(Plan)
    private readonly planRepository: Repository<Plan>,
    @InjectRepository(UsageLog)
    private readonly usageLogRepository: Repository<UsageLog>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(OrderPlanHistory)
    private readonly orderPlanHistoryRepository: Repository<OrderPlanHistory>,
    private readonly subscriptionCustomRepository: SubscriptionRepository,
    private readonly usageLogCustomRepository: UsageLogRepository,
    private readonly dataSource: DataSource
  ) {}

  /**
   * Lấy danh sách đăng ký của người dùng
   * @param userId ID của người dùng
   * @param status Trạng thái đăng ký (tùy chọn)
   * @param page Số trang
   * @param limit Số lượng bản ghi trên mỗi trang
   * @returns Danh sách đăng ký đã phân trang
   */
  async findSubscriptions(
    userId: number,
    status: SubscriptionStatus | null,
    page: number = 1,
    limit: number = 10
  ): Promise<PaginatedResult<Subscription>> {
    // Tạo tham số phân trang
    const paginationParams: QueryDto = {
      page,
      limit,
      sortBy: 'createdAt',
      sortDirection: SortDirection.DESC
    };

    // Lấy danh sách đăng ký
    const result = await this.subscriptionCustomRepository.findByUser(userId, status, paginationParams);

    // Lấy thông tin bổ sung cho mỗi đăng ký
    for (const subscription of result.items) {
      const planPricing = await this.planPricingRepository.findOne({
        where: { id: subscription.planPricingId }
      });

      if (planPricing) {
        const plan = await this.planRepository.findOne({
          where: { id: planPricing.planId }
        });

        if (plan) {
          (subscription as any).planInfo = {
            id: plan.id,
            name: plan.name,
            description: plan.description
          };
        }

        (subscription as any).pricingInfo = {
          billingCycle: planPricing.billingCycle,
          price: planPricing.price
        };
      }
    }

    return result;
  }

  /**
   * Lấy chi tiết đăng ký theo ID
   * @param id ID của đăng ký
   * @returns Chi tiết đăng ký
   */
  async findSubscriptionById(id: number): Promise<Subscription> {
    const subscription = await this.subscriptionCustomRepository.findSubscriptionById(id);

    if (!subscription) {
      throw new NotFoundException(`Subscription with ID ${id} not found`);
    }

    return subscription;
  }

  /**
   * Lấy chi tiết đăng ký với thông tin bổ sung
   * @param id ID của đăng ký
   * @returns Chi tiết đăng ký với thông tin bổ sung
   */
  async findSubscriptionWithDetails(id: number): Promise<{
    subscription: Subscription;
    planInfo: PlanInfoDto;
    pricingInfo: PricingInfoDto;
  }> {
    const subscription = await this.findSubscriptionById(id);

    const planPricing = await this.planPricingRepository.findOne({
      where: { id: subscription.planPricingId }
    });

    if (!planPricing) {
      throw new NotFoundException(`PlanPricing with ID ${subscription.planPricingId} not found`);
    }

    const plan = await this.planRepository.findOne({
      where: { id: planPricing.planId }
    });

    if (!plan) {
      throw new NotFoundException(`Plan with ID ${planPricing.planId} not found`);
    }

    const planInfo: PlanInfoDto = {
      id: plan.id,
      name: plan.name,
      description: plan.description,
      packageType: plan.packageType
    };

    const pricingInfo: PricingInfoDto = {
      billingCycle: planPricing.billingCycle,
      price: planPricing.price,
      usageLimit: planPricing.usageLimit,
      usageUnit: planPricing.usageUnit
    };

    return { subscription, planInfo, pricingInfo };
  }

  /**
   * Tạo đăng ký mới
   * @param userId ID của người dùng
   * @param planPricingId ID của tùy chọn giá
   * @param autoRenew Tự động gia hạn
   * @returns Đăng ký mới
   */
  @Transactional()
  async createSubscription(
    userId: number,
    planPricingId: number,
    autoRenew: boolean = true
  ): Promise<Subscription> {
    // Kiểm tra tùy chọn giá có tồn tại không
    const planPricing = await this.planPricingRepository.findOne({
      where: { id: planPricingId, isActive: true }
    });

    if (!planPricing) {
      throw new NotFoundException(`PlanPricing with ID ${planPricingId} not found or not active`);
    }

    // Lấy thông tin gói dịch vụ
    const plan = await this.planRepository.findOne({
      where: { id: planPricing.planId }
    });

    if (!plan) {
      throw new NotFoundException(`Plan with ID ${planPricing.planId} not found`);
    }

    // Kiểm tra người dùng có tồn tại không
    const user = await this.userRepository.findOne({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Kiểm tra số điểm của người dùng có đủ không
    const pointsRequired = Number(planPricing.price);
    if (user.pointsBalance < pointsRequired) {
      throw new BadRequestException(`User does not have enough points. Required: ${pointsRequired}, Available: ${user.pointsBalance}`);
    }

    // Lấy danh sách các planPricingId thuộc cùng plan
    const planPricings = await this.planPricingRepository.find({
      where: { planId: plan.id }
    });
    const planPricingIds = planPricings.map(pp => pp.id);

    // Kiểm tra người dùng đã có đăng ký nào với cùng gói dịch vụ này chưa
    const existingSubscription = await this.subscriptionRepository.findOne({
      where: {
        userId,
        status: SubscriptionStatus.ACTIVE,
        planPricingId: In(planPricingIds)
      }
    });

    // Nếu đã có đăng ký với cùng gói dịch vụ, thực hiện gia hạn
    if (existingSubscription) {
      return this.extendSubscription(existingSubscription.id, planPricingId, autoRenew);
    }

    // Tính toán thời gian bắt đầu và kết thúc
    const now = Date.now();
    let endDate: number;

    // Tính toán thời gian kết thúc dựa trên chu kỳ thanh toán và loại gói
    if (plan.packageType === PackageType.TIME_ONLY || plan.packageType === PackageType.HYBRID) {
      // Tính toán thời gian kết thúc dựa trên chu kỳ thanh toán
      switch (planPricing.billingCycle) {
        case 'MONTHLY':
          endDate = now + 30 * 24 * 60 * 60 * 1000; // 30 ngày
          break;
        case 'QUARTERLY':
          endDate = now + 90 * 24 * 60 * 60 * 1000; // 90 ngày
          break;
        case 'YEARLY':
          endDate = now + 365 * 24 * 60 * 60 * 1000; // 365 ngày
          break;
        default:
          endDate = now + 30 * 24 * 60 * 60 * 1000; // Mặc định 30 ngày
      }
    } else {
      // Đối với gói USAGE_BASED, thời gian kết thúc có thể là một giá trị xa trong tương lai
      endDate = now + 365 * 5 * 24 * 60 * 60 * 1000; // 5 năm
    }

    // Xác định usageLimit và remainingValue dựa trên loại gói
    let usageLimit = 0;
    let remainingValue = 0;

    if (plan.packageType === PackageType.USAGE_BASED || plan.packageType === PackageType.HYBRID) {
      usageLimit = planPricing.usageLimit;
      remainingValue = planPricing.usageLimit;
    }

    // Tạo đăng ký mới
    const subscription = this.subscriptionRepository.create({
      userId,
      planPricingId,
      startDate: now,
      endDate,
      autoRenew,
      status: SubscriptionStatus.ACTIVE,
      usageLimit,
      currentUsage: 0,
      remainingValue,
      usageUnit: planPricing.usageUnit,
      createdAt: now,
      updatedAt: now
    });

    // Lưu đăng ký
    const savedSubscription = await this.subscriptionRepository.save(subscription);

    // Trừ điểm của người dùng
    user.pointsBalance -= pointsRequired;
    user.updatedAt = now;
    await this.userRepository.save(user);

    // Tạo lịch sử mua gói dịch vụ
    const orderHistory = this.orderPlanHistoryRepository.create({
      userId,
      planId: plan.id,
      planPricingId,
      subscriptionId: savedSubscription.id,
      planName: plan.name,
      point: pointsRequired.toString(),
      billingCycle: planPricing.billingCycle,
      usageLimit: planPricing.usageLimit ? planPricing.usageLimit.toString() : null,
      usageUnit: planPricing.usageUnit,
      createdAt: now.toString(),
      isExtension: false,
      status: OrderStatus.COMPLETED
    });

    await this.orderPlanHistoryRepository.save(orderHistory);

    return savedSubscription;
  }

  /**
   * Gia hạn đăng ký hiện tại
   * @param subscriptionId ID của đăng ký cần gia hạn
   * @param planPricingId ID của tùy chọn giá mới
   * @param autoRenew Tự động gia hạn
   * @returns Đăng ký đã được gia hạn
   */
  @Transactional()
  async extendSubscription(
    subscriptionId: number,
    planPricingId: number,
    autoRenew: boolean = true
  ): Promise<Subscription> {
    // Kiểm tra đăng ký có tồn tại không
    const subscription = await this.subscriptionRepository.findOne({
      where: { id: subscriptionId, status: SubscriptionStatus.ACTIVE }
    });

    if (!subscription) {
      throw new NotFoundException(`Active subscription with ID ${subscriptionId} not found`);
    }

    // Kiểm tra tùy chọn giá có tồn tại không
    const planPricing = await this.planPricingRepository.findOne({
      where: { id: planPricingId, isActive: true }
    });

    if (!planPricing) {
      throw new NotFoundException(`PlanPricing with ID ${planPricingId} not found or not active`);
    }

    // Lấy thông tin gói dịch vụ mới
    const newPlan = await this.planRepository.findOne({
      where: { id: planPricing.planId }
    });

    if (!newPlan) {
      throw new NotFoundException(`Plan with ID ${planPricing.planId} not found`);
    }

    // Lấy thông tin gói dịch vụ hiện tại
    const currentPlanPricing = await this.planPricingRepository.findOne({
      where: { id: subscription.planPricingId }
    });

    if (!currentPlanPricing) {
      throw new NotFoundException(`Current PlanPricing with ID ${subscription.planPricingId} not found`);
    }

    const currentPlan = await this.planRepository.findOne({
      where: { id: currentPlanPricing.planId }
    });

    if (!currentPlan) {
      throw new NotFoundException(`Current Plan with ID ${currentPlanPricing.planId} not found`);
    }

    // Kiểm tra loại gói dịch vụ có tương thích không
    if (currentPlan.packageType !== newPlan.packageType) {
      throw new BadRequestException(`Cannot extend subscription with different package type. Current: ${currentPlan.packageType}, New: ${newPlan.packageType}`);
    }

    // Kiểm tra người dùng có tồn tại không
    const user = await this.userRepository.findOne({
      where: { id: subscription.userId }
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${subscription.userId} not found`);
    }

    // Kiểm tra số điểm của người dùng có đủ không
    const pointsRequired = Number(planPricing.price);
    if (user.pointsBalance < pointsRequired) {
      throw new BadRequestException(`User does not have enough points. Required: ${pointsRequired}, Available: ${user.pointsBalance}`);
    }

    const now = Date.now();
    let additionalTime = 0;

    // Tính toán thời gian gia hạn dựa trên chu kỳ thanh toán và loại gói
    if (newPlan.packageType === PackageType.TIME_ONLY || newPlan.packageType === PackageType.HYBRID) {
      // Tính toán thời gian gia hạn dựa trên chu kỳ thanh toán
      switch (planPricing.billingCycle) {
        case 'MONTHLY':
          additionalTime = 30 * 24 * 60 * 60 * 1000; // 30 ngày
          break;
        case 'QUARTERLY':
          additionalTime = 90 * 24 * 60 * 60 * 1000; // 90 ngày
          break;
        case 'YEARLY':
          additionalTime = 365 * 24 * 60 * 60 * 1000; // 365 ngày
          break;
        default:
          additionalTime = 30 * 24 * 60 * 60 * 1000; // Mặc định 30 ngày
      }

      // Cộng thêm thời gian vào endDate
      subscription.endDate += additionalTime;
    }

    // Cập nhật giới hạn sử dụng nếu cần
    if (newPlan.packageType === PackageType.USAGE_BASED || newPlan.packageType === PackageType.HYBRID) {
      if (planPricing.usageLimit) {
        // Nếu đăng ký hiện tại không có usageLimit, khởi tạo nó
        if (!subscription.usageLimit) {
          subscription.usageLimit = 0;
          subscription.remainingValue = 0;
        }

        subscription.usageLimit += planPricing.usageLimit;
        subscription.remainingValue += planPricing.usageLimit;
      }
    }

    // Cập nhật thông tin đăng ký
    subscription.autoRenew = autoRenew;
    subscription.updatedAt = now;

    // Lưu đăng ký đã cập nhật
    const updatedSubscription = await this.subscriptionRepository.save(subscription);

    // Trừ điểm của người dùng
    user.pointsBalance -= pointsRequired;
    user.updatedAt = now;
    await this.userRepository.save(user);

    // Tạo lịch sử mua gói dịch vụ
    const orderHistory = this.orderPlanHistoryRepository.create({
      userId: user.id,
      planId: newPlan.id,
      planPricingId,
      subscriptionId,
      planName: newPlan.name,
      point: pointsRequired.toString(),
      billingCycle: planPricing.billingCycle,
      usageLimit: planPricing.usageLimit ? planPricing.usageLimit.toString() : null,
      usageUnit: planPricing.usageUnit,
      createdAt: now.toString(),
      isExtension: true, // Đánh dấu đây là gia hạn
      status: OrderStatus.COMPLETED
    });

    await this.orderPlanHistoryRepository.save(orderHistory);

    return updatedSubscription;
  }

  /**
   * Hủy đăng ký
   * @param id ID của đăng ký
   * @returns Đăng ký đã hủy
   */
  async cancelSubscription(id: number): Promise<Subscription> {
    const subscription = await this.findSubscriptionById(id);

    if (subscription.status !== SubscriptionStatus.ACTIVE) {
      throw new BadRequestException('Only active subscriptions can be cancelled');
    }

    const updatedSubscription = await this.subscriptionCustomRepository.updateStatus(id, SubscriptionStatus.CANCELLED);

    if (!updatedSubscription) {
      throw new NotFoundException(`Subscription with ID ${id} not found`);
    }

    return updatedSubscription;
  }

  /**
   * Cập nhật cài đặt tự động gia hạn
   * @param id ID của đăng ký
   * @param autoRenew Cài đặt tự động gia hạn mới
   * @returns Đăng ký đã cập nhật
   */
  async updateAutoRenew(id: number, autoRenew: boolean): Promise<Subscription> {
    const subscription = await this.findSubscriptionById(id);

    if (subscription.status !== SubscriptionStatus.ACTIVE) {
      throw new BadRequestException('Only active subscriptions can be updated');
    }

    const updatedSubscription = await this.subscriptionCustomRepository.updateAutoRenew(id, autoRenew);

    if (!updatedSubscription) {
      throw new NotFoundException(`Subscription with ID ${id} not found`);
    }

    return updatedSubscription;
  }

  /**
   * Thay đổi gói đăng ký
   * @param id ID của đăng ký
   * @param newPlanPricingId ID của tùy chọn giá mới
   * @param effectiveImmediately Áp dụng ngay lập tức
   * @returns Đăng ký đã cập nhật
   */
  async changePlan(
    id: number,
    newPlanPricingId: number,
    effectiveImmediately: boolean = false
  ): Promise<Subscription> {
    const subscription = await this.findSubscriptionById(id);

    if (subscription.status !== SubscriptionStatus.ACTIVE) {
      throw new BadRequestException('Only active subscriptions can be changed');
    }

    // Kiểm tra tùy chọn giá mới có tồn tại không
    const newPlanPricing = await this.planPricingRepository.findOne({
      where: { id: newPlanPricingId, isActive: true }
    });

    if (!newPlanPricing) {
      throw new NotFoundException(`PlanPricing with ID ${newPlanPricingId} not found or not active`);
    }

    // Nếu áp dụng ngay lập tức, cập nhật đăng ký hiện tại
    if (effectiveImmediately) {
      const updatedSubscription = await this.subscriptionCustomRepository.changePlan(
        id,
        newPlanPricingId,
        newPlanPricing.usageLimit,
        newPlanPricing.usageUnit
      );

      if (!updatedSubscription) {
        throw new NotFoundException(`Subscription with ID ${id} not found`);
      }

      return updatedSubscription;
    }

    // Nếu không áp dụng ngay lập tức, tạo đăng ký mới có hiệu lực sau khi đăng ký hiện tại kết thúc
    // Đánh dấu đăng ký hiện tại là sẽ hết hạn
    await this.subscriptionCustomRepository.updateAutoRenew(id, false);

    // Tạo đăng ký mới có hiệu lực sau khi đăng ký hiện tại kết thúc
    const newSubscription = this.subscriptionRepository.create({
      userId: subscription.userId,
      planPricingId: newPlanPricingId,
      startDate: subscription.endDate,
      endDate: subscription.endDate + (subscription.endDate - subscription.startDate),
      autoRenew: true,
      status: SubscriptionStatus.PENDING,
      usageLimit: newPlanPricing.usageLimit,
      currentUsage: 0,
      remainingValue: newPlanPricing.usageLimit,
      usageUnit: newPlanPricing.usageUnit,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    await this.subscriptionRepository.save(newSubscription);

    // Trả về đăng ký hiện tại đã cập nhật
    return subscription;
  }

  /**
   * Lấy lịch sử sử dụng của đăng ký
   * @param subscriptionId ID của đăng ký
   * @param startDate Ngày bắt đầu (tùy chọn)
   * @param endDate Ngày kết thúc (tùy chọn)
   * @param page Số trang
   * @param limit Số lượng bản ghi trên mỗi trang
   * @returns Danh sách lịch sử sử dụng đã phân trang
   */
  async findUsageLogs(
    subscriptionId: number,
    startDate: number | null,
    endDate: number | null,
    page: number = 1,
    limit: number = 10
  ): Promise<PaginatedResult<UsageLog>> {
    // Kiểm tra đăng ký có tồn tại không
    const subscription = await this.findSubscriptionById(subscriptionId);

    if (!subscription) {
      throw new NotFoundException(`Subscription with ID ${subscriptionId} not found`);
    }

    // Tạo tham số phân trang
    const paginationParams: QueryDto = {
      page,
      limit,
      sortBy: 'createdAt',
      sortDirection: SortDirection.DESC
    };

    // Lấy danh sách lịch sử sử dụng
    return this.usageLogCustomRepository.findBySubscription(
      subscriptionId,
      startDate,
      endDate,
      paginationParams
    );
  }
}
