import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PlanPricingUserService } from '@modules/subscription/user/services';
import { PlanPricing } from '@modules/subscription/entities';
import { PlanPricingRepository } from '@modules/subscription/repositories';
import { PaginatedResult } from '@/common/response';

// Mock data
const mockPlanPricing: PlanPricing = {
  id: 1,
  planId: 1,
  billingCycle: 'MONTHLY',
  price: 9.99,
  usageLimit: 1000,
  usageUnit: 'API_CALLS',
  isActive: true,
  createdAt: Date.now(),
  updatedAt: Date.now()
};

const mockPlanPricingList: PlanPricing[] = [
  mockPlanPricing,
  {
    ...mockPlanPricing,
    id: 2,
    billingCycle: 'YEARLY',
    price: 99.99,
    usageLimit: 12000
  }
];

const mockPaginatedResult: PaginatedResult<PlanPricing> = {
  items: mockPlanPricingList,
  meta: {
    totalItems: 2,
    itemCount: 2,
    itemsPerPage: 10,
    totalPages: 1,
    currentPage: 1
  }
};

describe('PlanPricingUserService', () => {
  let service: PlanPricingUserService;
  let repository: Repository<PlanPricing>;
  let planPricingRepository: PlanPricingRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PlanPricingUserService,
        {
          provide: getRepositoryToken(PlanPricing),
          useValue: {
            findOne: jest.fn().mockResolvedValue(mockPlanPricing),
            find: jest.fn().mockResolvedValue(mockPlanPricingList),
          },
        },
        {
          provide: PlanPricingRepository,
          useValue: {
            findPlanPricing: jest.fn().mockResolvedValue(mockPaginatedResult),
            findByBillingCycle: jest.fn().mockResolvedValue(mockPaginatedResult),
          },
        },
      ],
    }).compile();

    service = module.get<PlanPricingUserService>(PlanPricingUserService);
    repository = module.get<Repository<PlanPricing>>(getRepositoryToken(PlanPricing));
    planPricingRepository = module.get<PlanPricingRepository>(PlanPricingRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findPlanPricing', () => {
    it('should return all plan pricing options when no billingCycle is provided', async () => {
      const result = await service.findPlanPricing(null, 1, 10);
      expect(planPricingRepository.findPlanPricing).toHaveBeenCalled();
      expect(result).toEqual(mockPaginatedResult);
    });

    it('should filter plan pricing by billingCycle when provided', async () => {
      const billingCycle = 'MONTHLY';
      const result = await service.findPlanPricing(billingCycle, 1, 10);
      expect(planPricingRepository.findByBillingCycle).toHaveBeenCalledWith(billingCycle, expect.any(Object));
      expect(result).toEqual(mockPaginatedResult);
    });
  });

  describe('findPlanPricingById', () => {
    it('should return a plan pricing by id', async () => {
      const result = await service.findPlanPricingById(1);
      expect(repository.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
      expect(result).toEqual(mockPlanPricing);
    });

    it('should return null if plan pricing is not found', async () => {
      jest.spyOn(repository, 'findOne').mockResolvedValueOnce(null);
      const result = await service.findPlanPricingById(999);
      expect(repository.findOne).toHaveBeenCalledWith({ where: { id: 999 } });
      expect(result).toBeNull();
    });
  });
});
