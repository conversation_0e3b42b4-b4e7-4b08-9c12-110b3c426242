import { Test, TestingModule } from '@nestjs/testing';
import { SubscriptionUserController } from '../controllers/subscription-user.controller';
import { SubscriptionUserService } from '../services/subscription-user.service';
import { Subscription, SubscriptionStatus } from '../../entities/subscription.entity';
import { UsageLog } from '../../entities/usage-log.entity';
import { SubscriptionFilterDto } from '../../dto/subscription-filter.dto';
import { SubscriptionCreateDto } from '../../dto/subscription-create.dto';
import { AutoRenewUpdateDto } from '../../dto/auto-renew-update.dto';
import { ChangePlanDto } from '../../dto/change-plan.dto';
import { UsageLogFilterDto } from '../../dto/usage-log-filter.dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { PaginatedResult } from '@/common/response';
import { JwtPayload } from '@/modules/auth/interfaces/jwt-payload.interface';

// Mock data
const mockSubscription: Subscription = {
  id: 1,
  userId: 10,
  planPricingId: 1,
  startDate: Date.now(),
  endDate: Date.now() + 30 * 24 * 60 * 60 * 1000,
  autoRenew: true,
  status: SubscriptionStatus.ACTIVE,
  usageLimit: 1000,
  currentUsage: 100,
  remainingValue: 900,
  usageUnit: 'API_CALLS',
  createdAt: Date.now(),
  updatedAt: Date.now()
};

const mockSubscriptionList: Subscription[] = [
  mockSubscription,
  {
    ...mockSubscription,
    id: 2,
    planPricingId: 2
  }
];

const mockPlanInfo = {
  id: 1,
  name: 'Basic Plan',
  description: 'Basic plan for new users',
  packageType: 'TIME_ONLY'
};

const mockPricingInfo = {
  billingCycle: 'MONTHLY',
  price: 9.99,
  usageLimit: 1000,
  usageUnit: 'API_CALLS'
};

const mockUsageLog: UsageLog = {
  id: 1,
  subscriptionId: 1,
  feature: 'API_CALL',
  amount: 5,
  usageTime: Date.now(),
  createdAt: Date.now()
};

const mockUsageLogList: UsageLog[] = [
  mockUsageLog,
  {
    ...mockUsageLog,
    id: 2,
    feature: 'STORAGE',
    amount: 10
  }
];

const mockPaginatedSubscriptionResult: PaginatedResult<Subscription> = {
  items: mockSubscriptionList,
  meta: {
    totalItems: 2,
    itemCount: 2,
    itemsPerPage: 10,
    totalPages: 1,
    currentPage: 1
  }
};

const mockPaginatedUsageLogResult: PaginatedResult<UsageLog> = {
  items: mockUsageLogList,
  meta: {
    totalItems: 2,
    itemCount: 2,
    itemsPerPage: 10,
    totalPages: 1,
    currentPage: 1
  }
};

const mockRequest = {
  user: {
    id: 10,
    email: '<EMAIL>',
    role: 'USER'
  } as JwtPayload
};

describe('SubscriptionUserController', () => {
  let controller: SubscriptionUserController;
  let service: SubscriptionUserService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SubscriptionUserController],
      providers: [
        {
          provide: SubscriptionUserService,
          useValue: {
            findSubscriptions: jest.fn().mockResolvedValue(mockPaginatedSubscriptionResult),
            findSubscriptionById: jest.fn().mockResolvedValue(mockSubscription),
            findSubscriptionWithDetails: jest.fn().mockResolvedValue({
              subscription: mockSubscription,
              planInfo: mockPlanInfo,
              pricingInfo: mockPricingInfo
            }),
            createSubscription: jest.fn().mockResolvedValue(mockSubscription),
            cancelSubscription: jest.fn().mockResolvedValue(mockSubscription),
            updateAutoRenew: jest.fn().mockResolvedValue(mockSubscription),
            changePlan: jest.fn().mockResolvedValue(mockSubscription),
            findUsageLogs: jest.fn().mockResolvedValue(mockPaginatedUsageLogResult),
          },
        },
      ],
    }).compile();

    controller = module.get<SubscriptionUserController>(SubscriptionUserController);
    service = module.get<SubscriptionUserService>(SubscriptionUserService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return all subscriptions for the user with pagination', async () => {
      const filterDto: SubscriptionFilterDto = { page: 1, limit: 10 };
      const result = await controller.findAll(mockRequest, filterDto);

      expect(service.findSubscriptions).toHaveBeenCalledWith(10, null, 1, 10);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(200);
      expect(result.message).toBe('Success');
      expect(result.result).toHaveProperty('items');
      expect(result.result).toHaveProperty('meta');
      expect(result.result.items.length).toBe(2);
    });

    it('should filter subscriptions by status when provided', async () => {
      const filterDto: SubscriptionFilterDto = {
        page: 1,
        limit: 10,
        status: SubscriptionStatus.ACTIVE
      };
      const result = await controller.findAll(mockRequest, filterDto);

      expect(service.findSubscriptions).toHaveBeenCalledWith(10, SubscriptionStatus.ACTIVE, 1, 10);
      expect(result.result.items.length).toBe(2);
    });
  });

  describe('findOne', () => {
    it('should return a subscription with details by id', async () => {
      const result = await controller.findOne(1);

      expect(service.findSubscriptionWithDetails).toHaveBeenCalledWith(1);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(200);
      expect(result.result.id).toBe(1);
      expect(result.result).toHaveProperty('planInfo');
      expect(result.result).toHaveProperty('pricingInfo');
    });
  });

  describe('create', () => {
    it('should create a new subscription', async () => {
      const createDto: SubscriptionCreateDto = {
        planPricingId: 1,
        autoRenew: true
      };
      const result = await controller.create(mockRequest, createDto);

      expect(service.createSubscription).toHaveBeenCalledWith(10, 1, true);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(201);
      expect(result.message).toBe('Subscription created successfully');
      expect(result.result.id).toBe(1);
    });
  });

  describe('cancel', () => {
    it('should cancel a subscription', async () => {
      const result = await controller.cancel(1);

      expect(service.cancelSubscription).toHaveBeenCalledWith(1);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(200);
      expect(result.message).toBe('Subscription cancelled successfully');
      expect(result.result).toHaveProperty('id');
      expect(result.result).toHaveProperty('status');
    });
  });

  describe('updateAutoRenew', () => {
    it('should update auto-renew setting', async () => {
      const updateDto: AutoRenewUpdateDto = {
        autoRenew: false
      };
      const result = await controller.updateAutoRenew(1, updateDto);

      expect(service.updateAutoRenew).toHaveBeenCalledWith(1, false);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(200);
      expect(result.message).toBe('Auto-renew setting updated successfully');
      expect(result.result).toHaveProperty('autoRenew');
      expect(result.result.autoRenew).toBe(true); // Mock returns true regardless of input
    });
  });

  describe('changePlan', () => {
    it('should change subscription plan', async () => {
      const changeDto: ChangePlanDto = {
        newPlanPricingId: 2,
        effectiveImmediately: true
      };
      const result = await controller.changePlan(1, changeDto);

      expect(service.changePlan).toHaveBeenCalledWith(1, 2, true);
      expect(service.findSubscriptionWithDetails).toHaveBeenCalledWith(1);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(200);
      expect(result.message).toBe('Subscription plan changed successfully');
    });
  });

  describe('findUsageLogs', () => {
    it('should return usage logs for a subscription with pagination', async () => {
      const filterDto: UsageLogFilterDto = { page: 1, limit: 10 };
      const result = await controller.findUsageLogs(1, filterDto);

      expect(service.findUsageLogs).toHaveBeenCalledWith(1, null, null, 1, 10);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(200);
      expect(result.result).toHaveProperty('items');
      expect(result.result).toHaveProperty('meta');
      expect(result.result.items.length).toBe(2);
    });

    it('should filter usage logs by date range when provided', async () => {
      const startDate = Date.now() - 7 * 24 * 60 * 60 * 1000;
      const endDate = Date.now();
      const filterDto: UsageLogFilterDto = {
        page: 1,
        limit: 10,
        startDate,
        endDate
      };
      const result = await controller.findUsageLogs(1, filterDto);

      expect(service.findUsageLogs).toHaveBeenCalledWith(1, startDate, endDate, 1, 10);
      expect(result.result.items.length).toBe(2);
    });
  });
});
