import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PlanUserService } from '../services/plan-user.service';
import { Plan, PackageType } from '../../entities/plan.entity';
import { PlanPricing } from '@modules/subscription/entities';
import { PlanRepository } from '../../repositories/plan.repository';
import { PaginatedResult } from '@/common/response';
import { NotFoundException } from '@nestjs/common';

// Mock data
const mockPlan: Plan = {
  id: 1,
  name: 'Basic Plan',
  description: 'Basic plan for new users',
  packageType: PackageType.TIME_ONLY,
  createdAt: Date.now(),
  updatedAt: Date.now()
};

const mockPlanList: Plan[] = [
  mockPlan,
  {
    ...mockPlan,
    id: 2,
    name: 'Premium Plan',
    description: 'Premium plan with more features'
  }
];

const mockPlanPricing: PlanPricing = {
  id: 1,
  planId: 1,
  billingCycle: 'MONTHLY',
  price: 9.99,
  usageLimit: 1000,
  usageUnit: 'API_CALLS',
  isActive: true,
  createdAt: Date.now(),
  updatedAt: Date.now()
};

const mockPlanPricingList: PlanPricing[] = [
  mockPlanPricing,
  {
    ...mockPlanPricing,
    id: 2,
    billingCycle: 'YEARLY',
    price: 99.99,
    usageLimit: 12000
  }
];

const mockPaginatedResult: PaginatedResult<Plan> = {
  items: mockPlanList,
  meta: {
    totalItems: 2,
    itemCount: 2,
    itemsPerPage: 10,
    totalPages: 1,
    currentPage: 1
  }
};

describe('PlanUserService', () => {
  let service: PlanUserService;
  let planRepository: Repository<Plan>;
  let planPricingRepository: Repository<PlanPricing>;
  let planCustomRepository: PlanRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PlanUserService,
        {
          provide: getRepositoryToken(Plan),
          useValue: {
            findOne: jest.fn().mockResolvedValue(mockPlan),
          },
        },
        {
          provide: getRepositoryToken(PlanPricing),
          useValue: {
            find: jest.fn().mockResolvedValue(mockPlanPricingList),
          },
        },
        {
          provide: PlanRepository,
          useValue: {
            findPlans: jest.fn().mockResolvedValue(mockPaginatedResult),
            findPlanById: jest.fn().mockResolvedValue(mockPlan),
          },
        },
      ],
    }).compile();

    service = module.get<PlanUserService>(PlanUserService);
    planRepository = module.get<Repository<Plan>>(getRepositoryToken(Plan));
    planPricingRepository = module.get<Repository<PlanPricing>>(getRepositoryToken(PlanPricing));
    planCustomRepository = module.get<PlanRepository>(PlanRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findPlans', () => {
    it('should return all plans with pagination', async () => {
      const result = await service.findPlans(1, 10);
      expect(planCustomRepository.findPlans).toHaveBeenCalled();
      expect(result).toEqual(mockPaginatedResult);
    });
  });

  describe('findPlanById', () => {
    it('should return a plan by id', async () => {
      const result = await service.findPlanById(1);
      expect(planCustomRepository.findPlanById).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockPlan);
    });

    it('should throw NotFoundException if plan is not found', async () => {
      jest.spyOn(planCustomRepository, 'findPlanById').mockResolvedValueOnce(null);
      await expect(service.findPlanById(999)).rejects.toThrow(NotFoundException);
    });
  });

  describe('findPlanPricingByPlanId', () => {
    it('should return plan pricing options for a plan', async () => {
      const result = await service.findPlanPricingByPlanId(1);
      expect(planCustomRepository.findPlanById).toHaveBeenCalledWith(1);
      expect(planPricingRepository.find).toHaveBeenCalledWith({
        where: { planId: 1, isActive: true },
        order: { createdAt: 'DESC' }
      });
      expect(result).toEqual(mockPlanPricingList);
    });

    it('should throw NotFoundException if plan is not found', async () => {
      jest.spyOn(planCustomRepository, 'findPlanById').mockResolvedValueOnce(null);
      await expect(service.findPlanPricingByPlanId(999)).rejects.toThrow(NotFoundException);
    });
  });
});
