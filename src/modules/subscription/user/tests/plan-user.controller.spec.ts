import { Test, TestingModule } from '@nestjs/testing';
import { PlanUserController } from '../controllers/plan-user.controller';
import { PlanUserService } from '../services/plan-user.service';
import { Plan } from '../../entities/plan.entity';
import { PlanPricing } from '@modules/subscription/entities';
import {ApiResponseDto, PaginatedResult} from '@common/response/api-response-dto';
import {PackageType} from "@modules/subscription/enums";

// Mock data
const mockPlan: Plan = {
  id: 1,
  name: 'Basic Plan',
  description: 'Basic plan for new users',
  packageType: PackageType.TIME_ONLY,
  createdAt: Date.now(),
  updatedAt: Date.now()
};

const mockPlanList: Plan[] = [
  mockPlan,
  {
    ...mockPlan,
    id: 2,
    name: 'Premium Plan',
    description: 'Premium plan with more features'
  }
];

const mockPlanPricing: PlanPricing = {
  id: 1,
  planId: 1,
  billingCycle: 'MONTHLY',
  price: 9.99,
  usageLimit: 1000,
  usageUnit: 'API_CALLS',
  isActive: true,
  createdAt: Date.now(),
  updatedAt: Date.now()
};

const mockPlanPricingList: PlanPricing[] = [
  mockPlanPricing,
  {
    ...mockPlanPricing,
    id: 2,
    billingCycle: 'YEARLY',
    price: 99.99,
    usageLimit: 12000
  }
];

const mockPaginatedResult: PaginatedResult<Plan> = {
  items: mockPlanList,
  meta: {
    totalItems: 2,
    itemCount: 2,
    itemsPerPage: 10,
    totalPages: 1,
    currentPage: 1
  }
};

describe('PlanUserController', () => {
  let controller: PlanUserController;
  let service: PlanUserService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PlanUserController],
      providers: [
        {
          provide: PlanUserService,
          useValue: {
            findPlans: jest.fn().mockResolvedValue(mockPaginatedResult),
            findPlanById: jest.fn().mockResolvedValue(mockPlan),
            findPlanPricingByPlanId: jest.fn().mockResolvedValue(mockPlanPricingList),
          },
        },
      ],
    }).compile();

    controller = module.get<PlanUserController>(PlanUserController);
    service = module.get<PlanUserService>(PlanUserService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return all plans with pagination', async () => {
      const result = await controller.findAll(1, 10);

      expect(service.findPlans).toHaveBeenCalledWith(1, 10);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(200);
      expect(result.message).toBe('Success');
      expect(result.result).toHaveProperty('items');
      expect(result.result).toHaveProperty('meta');
      expect(result?.result?.items.length).toBe(2);
    });
  });

  describe('findOne', () => {
    it('should return a plan by id', async () => {
      const result = await controller.findOne(1);

      expect(service.findPlanById).toHaveBeenCalledWith(1);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(200);
      expect(result?.result?.id).toBe(1);
      expect(result?.result?.name).toBe('Basic Plan');
    });
  });

  describe('findPlanPricing', () => {
    it('should return pricing options for a plan', async () => {
      const result = await controller.findPlanPricing(1);

      expect(service.findPlanPricingByPlanId).toHaveBeenCalledWith(1);
      expect(result).toBeInstanceOf(ApiResponseDto);
      expect(result.code).toBe(200);
      expect(result?.result).toHaveProperty('items');
      expect(result?.result?.items.length).toBe(2);
    });
  });
});
