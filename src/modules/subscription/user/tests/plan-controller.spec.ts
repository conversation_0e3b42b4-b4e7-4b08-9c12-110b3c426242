import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';

// Mock interfaces
enum PackageType {
  TIME_ONLY = 'TIME_ONLY',
  USAGE_BASED = 'USAGE_BASED',
  HYBRID = 'HYBRID'
}

interface Plan {
  id: number;
  name: string;
  description: string;
  packageType: PackageType;
  createdAt: number;
  updatedAt: number;
}

interface PlanPricing {
  id: number;
  planId: number;
  billingCycle: string;
  price: number;
  usageLimit: number;
  usageUnit: string;
  isActive: boolean;
  createdAt: number;
  updatedAt: number;
}

interface PaginatedResult<T> {
  items: T[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}

interface ApiResponse<T = any> {
  code: number;
  message: string;
  result: T;
}

// Mock service
class MockPlanUserService {
  async findPlans(page: number = 1, limit: number = 10): Promise<PaginatedResult<Plan>> {
    const mockPlan: Plan = {
      id: 1,
      name: 'Basic Plan',
      description: 'Basic plan for new users',
      packageType: PackageType.TIME_ONLY,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    const mockPlanList: Plan[] = [
      mockPlan,
      {
        ...mockPlan,
        id: 2,
        name: 'Premium Plan',
        description: 'Premium plan with more features',
        packageType: PackageType.USAGE_BASED
      }
    ];

    return {
      items: mockPlanList,
      meta: {
        totalItems: mockPlanList.length,
        itemCount: mockPlanList.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(mockPlanList.length / limit),
        currentPage: page
      }
    };
  }

  async findPlanById(id: number): Promise<Plan> {
    if (id === 999) {
      throw new NotFoundException(`Plan with ID ${id} not found`);
    }

    return {
      id,
      name: 'Basic Plan',
      description: 'Basic plan for new users',
      packageType: PackageType.TIME_ONLY,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };
  }

  async findPlanPricingByPlanId(planId: number): Promise<PlanPricing[]> {
    if (planId === 999) {
      throw new NotFoundException(`Plan with ID ${planId} not found`);
    }

    const mockPlanPricing: PlanPricing = {
      id: 1,
      planId,
      billingCycle: 'MONTHLY',
      price: 9.99,
      usageLimit: 1000,
      usageUnit: 'API_CALLS',
      isActive: true,
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    return [
      mockPlanPricing,
      {
        ...mockPlanPricing,
        id: 2,
        billingCycle: 'YEARLY',
        price: 99.99,
        usageLimit: 12000
      }
    ];
  }
}

// Mock controller
class MockPlanUserController {
  constructor(private readonly planUserService: MockPlanUserService) {}

  async findAll(page = 1, limit = 10) {
    const result = await this.planUserService.findPlans(+page, +limit);
    
    const responseData = {
      items: result.items.map(item => ({ ...item })),
      meta: result.meta
    };
    
    return {
      code: 200,
      message: 'Success',
      result: responseData
    } as ApiResponse;
  }

  async findOne(id: number) {
    const plan = await this.planUserService.findPlanById(id);
    
    return {
      code: 200,
      message: 'Success',
      result: plan
    } as ApiResponse;
  }

  async findPlanPricing(planId: number) {
    const planPricing = await this.planUserService.findPlanPricingByPlanId(planId);
    
    const responseData = {
      items: planPricing.map(item => ({ ...item }))
    };
    
    return {
      code: 200,
      message: 'Success',
      result: responseData
    } as ApiResponse;
  }
}

describe('PlanUserController', () => {
  let controller: MockPlanUserController;
  let service: MockPlanUserService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MockPlanUserService,
        {
          provide: MockPlanUserController,
          useFactory: (service: MockPlanUserService) => new MockPlanUserController(service),
          inject: [MockPlanUserService],
        },
      ],
    }).compile();

    controller = module.get<MockPlanUserController>(MockPlanUserController);
    service = module.get<MockPlanUserService>(MockPlanUserService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return all plans with pagination', async () => {
      const result = await controller.findAll(1, 10);
      
      expect(result.code).toBe(200);
      expect(result.message).toBe('Success');
      expect(result.result).toHaveProperty('items');
      expect(result.result).toHaveProperty('meta');
      expect(result.result.items.length).toBeGreaterThan(0);
      expect(result.result.items[0]).toHaveProperty('name');
      expect(result.result.items[0]).toHaveProperty('packageType');
    });
  });

  describe('findOne', () => {
    it('should return a plan by id', async () => {
      const result = await controller.findOne(1);
      
      expect(result.code).toBe(200);
      expect(result.result.id).toBe(1);
      expect(result.result.name).toBe('Basic Plan');
    });

    it('should throw NotFoundException if plan is not found', async () => {
      await expect(controller.findOne(999)).rejects.toThrow(NotFoundException);
    });
  });

  describe('findPlanPricing', () => {
    it('should return pricing options for a plan', async () => {
      const result = await controller.findPlanPricing(1);
      
      expect(result.code).toBe(200);
      expect(result.result).toHaveProperty('items');
      expect(result.result.items.length).toBeGreaterThan(0);
      expect(result.result.items[0]).toHaveProperty('planId');
      expect(result.result.items[0].planId).toBe(1);
    });

    it('should throw NotFoundException if plan is not found', async () => {
      await expect(controller.findPlanPricing(999)).rejects.toThrow(NotFoundException);
    });
  });
});
