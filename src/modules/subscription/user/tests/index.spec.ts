/**
 * This file imports all the test files in the subscription module
 * to make it easier to run all tests at once.
 */

// Import service tests
import './plan-pricing-user.service.spec';
import './plan-user.service.spec';
import './subscription-user.service.spec';

// Import controller tests
import './plan-pricing-user.controller.spec';
import './plan-user.controller.spec';
import './subscription-user.controller.spec';

// Import integration tests
// Note: Integration tests are more resource-intensive and might be run separately
// import './subscription-user.integration.spec';

describe('Subscription Module Tests', () => {
  it('should run all subscription module tests', () => {
    // This is just a placeholder test to ensure the test suite runs
    expect(true).toBe(true);
  });
});
