# Subscription Module Tests

This directory contains tests for the subscription module, including unit tests for services and controllers, as well as integration tests.

## Test Structure

- **Unit Tests**:
  - `plan-pricing-user.service.spec.ts`: Tests for the PlanPricingUserService
  - `plan-user.service.spec.ts`: Tests for the PlanUserService
  - `subscription-user.service.spec.ts`: Tests for the SubscriptionUserService
  - `plan-pricing-user.controller.spec.ts`: Tests for the PlanPricingUserController
  - `plan-user.controller.spec.ts`: Tests for the PlanUserController
  - `subscription-user.controller.spec.ts`: Tests for the SubscriptionUserController

- **Integration Tests**:
  - `subscription-user.integration.spec.ts`: Integration tests for the subscription module

- **Index File**:
  - `index.spec.ts`: Imports all test files to run them together

## Running Tests

### Running All Tests

To run all tests in the project:

```bash
npm run test
```

### Running Subscription Module Tests Only

To run only the subscription module tests:

```bash
npm run test -- src/modules/subscription
```

### Running Specific Test Files

To run a specific test file:

```bash
npm run test -- src/modules/subscription/user/tests/plan-user.service.spec.ts
```

### Running Tests in Watch Mode

To run tests in watch mode (tests will automatically re-run when files change):

```bash
npm run test:watch -- src/modules/subscription
```

### Running Integration Tests

Integration tests are more resource-intensive and might be run separately:

```bash
npm run test -- src/modules/subscription/user/tests/subscription-user.integration.spec.ts
```

## Test Coverage

To generate a test coverage report:

```bash
npm run test:cov
```

This will generate a coverage report in the `coverage` directory.

## Writing New Tests

When adding new features to the subscription module, please add corresponding tests. Follow the existing patterns for unit tests and integration tests.

- **Unit Tests**: Test individual components in isolation, mocking dependencies
- **Integration Tests**: Test how components work together, using an in-memory database

## Troubleshooting

If you encounter issues with the tests:

1. Make sure all dependencies are installed: `npm install`
2. Check that the test environment is properly configured
3. Verify that the database entities and repositories are correctly mocked
4. Check for any changes in the API that might require test updates
