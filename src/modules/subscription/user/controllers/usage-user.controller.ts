import { Controller, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { UsageUserService } from '../services/usage-user.service';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';

@ApiTags('Subscription - User Usage')
@ApiBearerAuth()
@UseGuards(JwtUserGuard)
@Controller('user/subscription/usage')
export class UsageUserController {
  constructor(private readonly usageUserService: UsageUserService) {}
}
