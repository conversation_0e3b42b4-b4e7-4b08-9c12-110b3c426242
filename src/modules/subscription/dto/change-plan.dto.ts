import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional } from 'class-validator';

export class ChangePlanDto {
  @ApiProperty({
    description: 'ID của tùy chọn giá mới',
    example: 2,
  })
  @IsNumber()
  newPlanPricingId: number;

  @ApiProperty({
    description: 'Áp dụng ngay lập tức',
    example: true,
    required: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  effectiveImmediately?: boolean = false;
}
