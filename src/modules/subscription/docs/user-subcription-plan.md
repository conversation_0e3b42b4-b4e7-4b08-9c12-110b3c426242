# API Documentation cho Subscription Plan - User

## Giới thiệu

API Subscription Plan cung cấp các endpoint để người dùng có thể xem, đăng ký và quản lý các gói dịch vụ trong hệ thống. API này cho phép người dùng:

- Xem danh sách các gói dịch vụ (plans) có sẵn
- Xem chi tiết các tùy chọn giá (plan pricing) của mỗi gói
- Đăng ký sử dụng gói dịch vụ
- Quản lý các gói đã đăng ký (gia hạn, hủy, nâng cấp/hạ cấp)
- Xem lịch sử sử dụng

## Chuẩn Param Dùng Chung

### Param URL

| Param           | Kiểu dữ liệu | Mô tả                                                | Ví dụ                                |
| --------------- | ------------ | ---------------------------------------------------- | ------------------------------------ |
| id              | number       | ID của đối tượng                                     | /user/plans/1                        |
| planId          | number       | ID của gói dịch vụ                                   | /user/plans/1/pricing                |
| planPricingId   | number       | ID của tùy chọn giá                                  | /user/plan-pricing/1/subscribe       |
| subscriptionId  | number       | ID của đăng ký                                       | /user/subscriptions/1                |

### Param Query

| Param      | Kiểu dữ liệu | Mô tả                                                | Ví dụ                                |
| ---------- | ------------ | ---------------------------------------------------- | ------------------------------------ |
| page       | number       | Trang hiện tại (mặc định: 1)                         | ?page=1                              |
| limit      | number       | Số lượng bản ghi trên mỗi trang (mặc định: 10)       | ?limit=10                            |
| status     | string       | Lọc theo trạng thái                                  | ?status=ACTIVE                       |
| startDate  | number       | Lọc theo ngày bắt đầu (Unix timestamp)               | ?startDate=1632474086123             |
| endDate    | number       | Lọc theo ngày kết thúc (Unix timestamp)              | ?endDate=1632474086123               |

### Mã Status và Thông báo lỗi

| Code | Description           | Thông báo                     |
| ---- | --------------------- | ----------------------------- |
| 200  | OK                    | Success                       |
| 201  | Created               | Resource created successfully |
| 400  | Bad Request           | Invalid input data            |
| 401  | Unauthorized          | Authentication required       |
| 403  | Forbidden             | Permission denied             |
| 404  | Not Found             | Resource not found            |
| 409  | Conflict              | Resource already exists       |
| 500  | Internal Server Error | Internal server error         |

## API Endpoints

### 1. Lấy danh sách gói dịch vụ

**Endpoint**: GET /user/plans

**Params**:

- `page`: (number) Trang hiện tại (mặc định: 1)
- `limit`: (number) Số lượng bản ghi trên mỗi trang (mặc định: 10)

**Response**:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "items": [
      {
        "id": 1,
        "name": "Basic Plan",
        "description": "Gói cơ bản cho người dùng mới",
        "packageType": "TIME_ONLY",
        "createdAt": 1632474086123,
        "updatedAt": 1632474086123
      },
      {
        "id": 2,
        "name": "Premium Plan",
        "description": "Gói cao cấp với nhiều tính năng hơn",
        "packageType": "HYBRID",
        "createdAt": 1632474086123,
        "updatedAt": 1632474086123
      }
    ],
    "meta": {
      "totalItems": 5,
      "itemCount": 2,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

### 2. Lấy chi tiết gói dịch vụ

**Endpoint**: GET /user/plans/{id}

**Params**:

- `id`: (number) ID của gói dịch vụ

**Response**:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "id": 1,
    "name": "Basic Plan",
    "description": "Gói cơ bản cho người dùng mới",
    "packageType": "TIME_ONLY",
    "createdAt": 1632474086123,
    "updatedAt": 1632474086123
  }
}
```

### 3. Lấy danh sách tùy chọn giá của gói dịch vụ

**Endpoint**: GET /user/plans/{planId}/pricing

**Params**:

- `planId`: (number) ID của gói dịch vụ

**Response**:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "items": [
      {
        "id": 1,
        "planId": 1,
        "billingCycle": "MONTHLY",
        "price": 9.99,
        "usageLimit": 1000,
        "usageUnit": "API_CALLS",
        "isActive": true,
        "createdAt": 1632474086123,
        "updatedAt": 1632474086123
      },
      {
        "id": 2,
        "planId": 1,
        "billingCycle": "YEARLY",
        "price": 99.99,
        "usageLimit": 12000,
        "usageUnit": "API_CALLS",
        "isActive": true,
        "createdAt": 1632474086123,
        "updatedAt": 1632474086123
      }
    ]
  }
}
```

### 4. Đăng ký gói dịch vụ

**Endpoint**: POST /user/subscriptions

**Request Body**:

```json
{
  "planPricingId": 1,
  "autoRenew": true
}
```

**Response**:

```json
{
  "code": 201,
  "message": "Subscription created successfully",
  "result": {
    "id": 1,
    "userId": 10,
    "planPricingId": 1,
    "startDate": 1632474086123,
    "endDate": 1635066086123,
    "autoRenew": true,
    "status": "ACTIVE",
    "usageLimit": 1000,
    "currentUsage": 0,
    "remainingValue": 1000,
    "usageUnit": "API_CALLS",
    "createdAt": 1632474086123,
    "updatedAt": 1632474086123
  }
}
```

### 5. Lấy danh sách đăng ký của người dùng

**Endpoint**: GET /user/subscriptions

**Params**:

- `page`: (number) Trang hiện tại (mặc định: 1)
- `limit`: (number) Số lượng bản ghi trên mỗi trang (mặc định: 10)
- `status`: (string) Lọc theo trạng thái (ACTIVE, CANCELLED, EXPIRED, PENDING, SUSPENDED)

**Response**:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "items": [
      {
        "id": 1,
        "userId": 10,
        "planPricingId": 1,
        "planInfo": {
          "id": 1,
          "name": "Basic Plan",
          "description": "Gói cơ bản cho người dùng mới"
        },
        "pricingInfo": {
          "billingCycle": "MONTHLY",
          "price": 9.99
        },
        "startDate": 1632474086123,
        "endDate": 1635066086123,
        "autoRenew": true,
        "status": "ACTIVE",
        "usageLimit": 1000,
        "currentUsage": 100,
        "remainingValue": 900,
        "usageUnit": "API_CALLS",
        "createdAt": 1632474086123,
        "updatedAt": 1632474086123
      }
    ],
    "meta": {
      "totalItems": 1,
      "itemCount": 1,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

### 6. Lấy chi tiết đăng ký

**Endpoint**: GET /user/subscriptions/{id}

**Params**:

- `id`: (number) ID của đăng ký

**Response**:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "id": 1,
    "userId": 10,
    "planPricingId": 1,
    "planInfo": {
      "id": 1,
      "name": "Basic Plan",
      "description": "Gói cơ bản cho người dùng mới",
      "packageType": "TIME_ONLY"
    },
    "pricingInfo": {
      "billingCycle": "MONTHLY",
      "price": 9.99,
      "usageLimit": 1000,
      "usageUnit": "API_CALLS"
    },
    "startDate": 1632474086123,
    "endDate": 1635066086123,
    "autoRenew": true,
    "status": "ACTIVE",
    "usageLimit": 1000,
    "currentUsage": 100,
    "remainingValue": 900,
    "usageUnit": "API_CALLS",
    "createdAt": 1632474086123,
    "updatedAt": 1632474086123
  }
}
```

### 7. Hủy đăng ký

**Endpoint**: PUT /user/subscriptions/{id}/cancel

**Params**:

- `id`: (number) ID của đăng ký

**Response**:

```json
{
  "code": 200,
  "message": "Subscription cancelled successfully",
  "result": {
    "id": 1,
    "status": "CANCELLED",
    "updatedAt": 1632474086123
  }
}
```

### 8. Bật/tắt tự động gia hạn

**Endpoint**: PUT /user/subscriptions/{id}/auto-renew

**Params**:

- `id`: (number) ID của đăng ký

**Request Body**:

```json
{
  "autoRenew": false
}
```

**Response**:

```json
{
  "code": 200,
  "message": "Auto-renew setting updated successfully",
  "result": {
    "id": 1,
    "autoRenew": false,
    "updatedAt": 1632474086123
  }
}
```

### 9. Nâng cấp/hạ cấp đăng ký

**Endpoint**: PUT /user/subscriptions/{id}/change-plan

**Params**:

- `id`: (number) ID của đăng ký

**Request Body**:

```json
{
  "newPlanPricingId": 2,
  "effectiveImmediately": true
}
```

**Response**:

```json
{
  "code": 200,
  "message": "Subscription plan changed successfully",
  "result": {
    "id": 1,
    "userId": 10,
    "planPricingId": 2,
    "startDate": 1632474086123,
    "endDate": 1635066086123,
    "autoRenew": true,
    "status": "ACTIVE",
    "usageLimit": 2000,
    "currentUsage": 0,
    "remainingValue": 2000,
    "usageUnit": "API_CALLS",
    "createdAt": 1632474086123,
    "updatedAt": 1632474086123
  }
}
```

### 10. Lấy lịch sử sử dụng

**Endpoint**: GET /user/subscriptions/{subscriptionId}/usage-logs

**Params**:

- `subscriptionId`: (number) ID của đăng ký
- `page`: (number) Trang hiện tại (mặc định: 1)
- `limit`: (number) Số lượng bản ghi trên mỗi trang (mặc định: 10)
- `startDate`: (number) Lọc theo ngày bắt đầu (Unix timestamp)
- `endDate`: (number) Lọc theo ngày kết thúc (Unix timestamp)

**Response**:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "items": [
      {
        "id": 1,
        "subscriptionId": 1,
        "feature": "API_CALL",
        "amount": 5,
        "usageTime": 1632474086123,
        "createdAt": 1632474086123
      },
      {
        "id": 2,
        "subscriptionId": 1,
        "feature": "STORAGE",
        "amount": 10.5,
        "usageTime": 1632474086123,
        "createdAt": 1632474086123
      }
    ],
    "meta": {
      "totalItems": 2,
      "itemCount": 2,
      "itemsPerPage": 10,
      "totalPages": 1,
      "currentPage": 1
    }
  }
}
```

## Cấu trúc Đối tượng

### Plan (Gói dịch vụ)

```json
{
  "id": number,
  "name": string,
  "description": string,
  "packageType": "TIME_ONLY" | "USAGE_BASED" | "HYBRID",
  "createdAt": number,
  "updatedAt": number
}
```

### PlanPricing (Tùy chọn giá)

```json
{
  "id": number,
  "planId": number,
  "billingCycle": string,
  "price": number,
  "usageLimit": number,
  "usageUnit": string,
  "isActive": boolean,
  "createdAt": number,
  "updatedAt": number
}
```

### Subscription (Đăng ký)

```json
{
  "id": number,
  "userId": number,
  "planPricingId": number,
  "startDate": number,
  "endDate": number,
  "autoRenew": boolean,
  "status": "ACTIVE" | "CANCELLED" | "EXPIRED" | "PENDING" | "SUSPENDED",
  "usageLimit": number,
  "currentUsage": number,
  "remainingValue": number,
  "usageUnit": string,
  "createdAt": number,
  "updatedAt": number
}
```

### UsageLog (Lịch sử sử dụng)

```json
{
  "id": number,
  "subscriptionId": number,
  "feature": string,
  "amount": number,
  "usageTime": number,
  "createdAt": number
}
```