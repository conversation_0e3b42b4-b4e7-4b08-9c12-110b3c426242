import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { PackageType } from '@modules/subscription/enums';
import { QueryDto } from '@common/dto';

export class AdminPlanFilterDto extends QueryDto {
  @ApiProperty({
    description: 'Tì<PERSON> kiếm theo tên gói dịch vụ',
    required: false,
    example: 'Premium'
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: 'Lọc theo loại gói dịch vụ',
    enum: PackageType,
    required: false,
    example: PackageType.TIME_ONLY
  })
  @IsOptional()
  @IsEnum(PackageType)
  packageType?: PackageType;
}
