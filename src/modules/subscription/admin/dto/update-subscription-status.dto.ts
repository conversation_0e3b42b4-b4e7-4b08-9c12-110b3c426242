import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { SubscriptionStatus } from '@modules/subscription/enums';

export class UpdateSubscriptionStatusDto {
  @ApiProperty({
    description: 'Trạng thái mới của đăng ký',
    enum: SubscriptionStatus,
    example: SubscriptionStatus.ACTIVE
  })
  @IsNotEmpty()
  @IsEnum(SubscriptionStatus)
  status: SubscriptionStatus;
}
