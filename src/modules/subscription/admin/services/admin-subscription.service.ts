import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Subscription, UsageLog } from '@modules/subscription/entities';
import { SubscriptionRepository, UsageLogRepository } from '@modules/subscription/repositories';
import { AppException, ErrorCode } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';
import { QueryDto, SortDirection } from '@common/dto';
import { AdminSubscriptionFilterDto, AdminUsageLogFilterDto, UpdateSubscriptionStatusDto } from '../dto';
import { SubscriptionStatus } from '@modules/subscription/enums';

@Injectable()
export class AdminSubscriptionService {
  private readonly logger = new Logger(AdminSubscriptionService.name);

  constructor(
    @InjectRepository(Subscription)
    private readonly subscriptionRepository: Repository<Subscription>,
    @InjectRepository(UsageLog)
    private readonly usageLogRepository: Repository<UsageLog>,
    private readonly subscriptionCustomRepository: SubscriptionRepository,
    private readonly usageLogCustomRepository: UsageLogRepository
  ) {}

  /**
   * Lấy danh sách đăng ký với bộ lọc
   * @param filterDto Bộ lọc
   * @returns Danh sách đăng ký đã phân trang
   */
  async findSubscriptions(filterDto: AdminSubscriptionFilterDto): Promise<PaginatedResult<Subscription>> {
    try {
      const { userId, planId, status, page = 1, limit = 10, sortBy = 'createdAt', sortDirection = SortDirection.DESC } = filterDto;

      // Tạo tham số phân trang
      const paginationParams: QueryDto = {
        page,
        limit,
        sortBy,
        sortDirection
      };

      // Tạo điều kiện tìm kiếm
      const where: any = {};

      if (userId) {
        where.userId = userId;
      }

      if (planId) {
        where.planId = planId;
      }

      if (status) {
        where.status = status;
      }

      // Lấy danh sách đăng ký
      return this.subscriptionCustomRepository.findSubscriptions(paginationParams);
    } catch (error) {
      this.logger.error(`Error finding subscriptions: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách đăng ký'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết đăng ký
   * @param id ID của đăng ký
   * @returns Thông tin chi tiết đăng ký
   */
  async findSubscriptionById(id: number): Promise<Subscription> {
    try {
      const subscription = await this.subscriptionRepository.findOne({ where: { id } });

      if (!subscription) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          `Không tìm thấy đăng ký với ID ${id}`
        );
      }

      return subscription;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error finding subscription by ID: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy thông tin đăng ký'
      );
    }
  }

  /**
   * Cập nhật trạng thái đăng ký
   * @param id ID của đăng ký
   * @param updateStatusDto Thông tin trạng thái mới
   * @returns Đăng ký đã cập nhật
   */
  async updateSubscriptionStatus(id: number, updateStatusDto: UpdateSubscriptionStatusDto): Promise<Subscription> {
    try {
      // Kiểm tra đăng ký tồn tại
      const subscription = await this.findSubscriptionById(id);

      // Cập nhật trạng thái
      const now = Math.floor(Date.now() / 1000);
      const updatedSubscription = {
        ...subscription,
        status: updateStatusDto.status,
        updatedAt: now
      };

      // Nếu trạng thái là hết hạn, cập nhật thời gian kết thúc thành thời gian hiện tại
      if (updateStatusDto.status === SubscriptionStatus.EXPIRED) {
        updatedSubscription.endDate = now;
      }

      // Lưu vào database
      await this.subscriptionRepository.update(id, updatedSubscription);

      // Trả về thông tin đã cập nhật
      return this.findSubscriptionById(id);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error updating subscription status: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi cập nhật trạng thái đăng ký'
      );
    }
  }

  /**
   * Lấy danh sách nhật ký sử dụng với bộ lọc
   * @param filterDto Bộ lọc
   * @returns Danh sách nhật ký sử dụng đã phân trang
   */
  async findUsageLogs(filterDto: AdminUsageLogFilterDto): Promise<PaginatedResult<UsageLog>> {
    try {
      const { subscriptionId, feature, startDate, endDate, page = 1, limit = 10, sortBy = 'createdAt', sortDirection = SortDirection.DESC } = filterDto;

      // Tạo tham số phân trang
      const paginationParams: QueryDto = {
        page,
        limit,
        sortBy,
        sortDirection
      };

      // Tạo điều kiện tìm kiếm
      const where: any = {};

      if (subscriptionId) {
        where.subscriptionId = subscriptionId;
      }

      if (feature) {
        where.feature = feature;
      }

      // Không cần xử lý startDate và endDate ở đây vì sẽ được xử lý trong findBySubscription

      // Lấy danh sách nhật ký sử dụng
      return this.usageLogCustomRepository.findBySubscription(
        subscriptionId || 0,
        startDate || null,
        endDate || null,
        paginationParams
      );
    } catch (error) {
      this.logger.error(`Error finding usage logs: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy danh sách nhật ký sử dụng'
      );
    }
  }

  /**
   * Lấy thống kê sử dụng của đăng ký
   * @param subscriptionId ID của đăng ký
   * @returns Thống kê sử dụng
   */
  async getSubscriptionUsageStats(subscriptionId: number): Promise<{ totalUsage: number; usageByFeature: { feature: string; count: number }[] }> {
    try {
      // Kiểm tra đăng ký tồn tại
      await this.findSubscriptionById(subscriptionId);

      // Lấy tổng số lượng sử dụng
      const totalUsage = await this.usageLogRepository.count({ where: { subscriptionId } });

      // Lấy số lượng sử dụng theo tính năng
      const usageByFeatureResult = await this.usageLogRepository
        .createQueryBuilder('usageLog')
        .select('usageLog.feature', 'feature')
        .addSelect('COUNT(usageLog.id)', 'count')
        .where('usageLog.subscriptionId = :subscriptionId', { subscriptionId })
        .groupBy('usageLog.feature')
        .getRawMany();

      const usageByFeature = usageByFeatureResult.map(item => ({
        feature: item.feature,
        count: parseInt(item.count)
      }));

      return {
        totalUsage,
        usageByFeature
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Error getting subscription usage stats: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Lỗi khi lấy thống kê sử dụng của đăng ký'
      );
    }
  }
}
