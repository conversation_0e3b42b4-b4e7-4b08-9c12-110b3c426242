import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, MaxLength, MinLength, Matches, ValidateNested, IsObject } from 'class-validator';
import { Type } from 'class-transformer';
import { GenericFormConfig } from '../../interfaces/generic-form-config.interface';

/**
 * DTO cho việc tạo trang mới
 */
export class CreateGenericPageDto {
  /**
   * Tên của trang
   * @example "Trang liên hệ"
   */
  @ApiProperty({
    description: 'Tên của trang',
    example: 'Trang liên hệ',
  })
  @IsNotEmpty({ message: 'Tên trang không được để trống' })
  @IsString({ message: 'Tên trang phải là chuỗi' })
  @MinLength(3, { message: 'Tên trang phải có ít nhất 3 ký tự' })
  @MaxLength(255, { message: 'Tên trang không được vượt quá 255 ký tự' })
  name: string;

  /**
   * <PERSON><PERSON> tả về trang
   * @example "Form liên hệ cho khách hàng"
   */
  @ApiProperty({
    description: 'Mô tả về trang',
    example: 'Form liên hệ cho khách hàng',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;

  /**
   * Đường dẫn URL của trang
   * @example "lien-he"
   */
  @ApiProperty({
    description: 'Đường dẫn URL của trang',
    example: 'lien-he',
  })
  @IsNotEmpty({ message: 'Đường dẫn không được để trống' })
  @IsString({ message: 'Đường dẫn phải là chuỗi' })
  @MinLength(3, { message: 'Đường dẫn phải có ít nhất 3 ký tự' })
  @MaxLength(255, { message: 'Đường dẫn không được vượt quá 255 ký tự' })
  @Matches(/^[a-z0-9-]+$/, {
    message: 'Đường dẫn chỉ được chứa chữ thường, số và dấu gạch ngang',
  })
  path: string;

  /**
   * Cấu hình trang dạng JSON
   * @example { "formId": "contact-form", "title": "Liên hệ với chúng tôi", "groups": [] }
   */
  @ApiProperty({
    description: 'Cấu hình trang dạng JSON',
    example: {
      formId: 'contact-form',
      title: 'Liên hệ với chúng tôi',
      subtitle: 'Hãy để lại thông tin, chúng tôi sẽ liên hệ lại với bạn',
      groups: [],
    },
  })
  @IsNotEmpty({ message: 'Cấu hình trang không được để trống' })
  @IsObject({ message: 'Cấu hình trang phải là đối tượng JSON' })
  @Type(() => Object)
  config: GenericFormConfig;
}
