import { Module } from '@nestjs/common';
import { GenericPageAdminController, GenericPageTemplateAdminController } from './controllers';
import { GenericPageAdminService, GenericPageTemplateAdminService } from './services';

@Module({
  controllers: [
    GenericPageAdminController,
    GenericPageTemplateAdminController,
  ],
  providers: [
    GenericPageAdminService,
    GenericPageTemplateAdminService,
  ],
  exports: [
    GenericPageAdminService,
    GenericPageTemplateAdminService,
  ],
})
export class GenericAdminModule {}
