import { Body, Controller, Delete, Get, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JWTPayload } from '@modules/auth/interfaces';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { GenericPageTemplateAdminService } from '../services';
import { CreateGenericPageTemplateDto, GenericPageTemplateResponseDto, QueryGenericPageTemplateDto, UpdateGenericPageTemplateDto } from '../dto';

@ApiTags('Admin Generic Page Template')
@Controller('admin/generic-page-templates')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(ApiResponseDto, GenericPageTemplateResponseDto, PaginatedResult)
export class GenericPageTemplateAdminController {
  constructor(private readonly genericPageTemplateAdminService: GenericPageTemplateAdminService) {}

  /**
   * Tạo mẫu trang mới
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mẫu trang mới' })
  @ApiResponse({
    status: 201,
    description: 'Mẫu trang đã được tạo',
    schema: ApiResponseDto.getSchema(GenericPageTemplateResponseDto),
  })
  async createGenericPageTemplate(
    @Body() createGenericPageTemplateDto: CreateGenericPageTemplateDto,
    @CurrentEmployee() employee: JWTPayload,
  ): Promise<ApiResponseDto<GenericPageTemplateResponseDto>> {
    const result = await this.genericPageTemplateAdminService.createGenericPageTemplate(
      createGenericPageTemplateDto,
      String(employee.id),
    );
    return ApiResponseDto.created(result);
  }

  /**
   * Cập nhật mẫu trang
   */
  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật mẫu trang' })
  @ApiParam({ name: 'id', description: 'ID của mẫu trang' })
  @ApiResponse({
    status: 200,
    description: 'Mẫu trang đã được cập nhật',
    schema: ApiResponseDto.getSchema(GenericPageTemplateResponseDto),
  })
  async updateGenericPageTemplate(
    @Param('id') id: string,
    @Body() updateGenericPageTemplateDto: UpdateGenericPageTemplateDto,
    @CurrentEmployee() employee: JWTPayload,
  ): Promise<ApiResponseDto<GenericPageTemplateResponseDto>> {
    const result = await this.genericPageTemplateAdminService.updateGenericPageTemplate(
      id,
      updateGenericPageTemplateDto,
      String(employee.id),
    );
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy thông tin mẫu trang theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin mẫu trang theo ID' })
  @ApiParam({ name: 'id', description: 'ID của mẫu trang' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin mẫu trang',
    schema: ApiResponseDto.getSchema(GenericPageTemplateResponseDto),
  })
  async getGenericPageTemplateById(
    @Param('id') id: string,
  ): Promise<ApiResponseDto<GenericPageTemplateResponseDto>> {
    const result = await this.genericPageTemplateAdminService.getGenericPageTemplateById(id);
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa mẫu trang
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa mẫu trang' })
  @ApiParam({ name: 'id', description: 'ID của mẫu trang' })
  @ApiResponse({
    status: 200,
    description: 'Mẫu trang đã được xóa',
    schema: ApiResponseDto.getSchema(null),
  })
  async deleteGenericPageTemplate(
    @Param('id') id: string,
  ): Promise<ApiResponseDto<null>> {
    await this.genericPageTemplateAdminService.deleteGenericPageTemplate(id);
    return ApiResponseDto.success(null, 'Mẫu trang đã được xóa thành công');
  }
}
