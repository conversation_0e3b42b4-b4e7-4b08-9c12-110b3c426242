import { ApiProperty } from '@nestjs/swagger';
import { StrategyAgentVersion } from '@modules/strategy/entities';
import { StrategyAgentSchema } from './strategy-agent.schema';

export class StrategyAgentVersionSchema {
  @ApiProperty({
    description: 'ID định danh duy nhất cho mỗi phiên bản chiến lược',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của chiến lược agent mà phiên bản này thuộc về',
    example: 1,
  })
  strategyAgentId: number;

  @ApiProperty({
    description: 'Thông tin về chiến lược agent',
    type: StrategyAgentSchema,
    nullable: true,
  })
  strategyAgent: StrategyAgentSchema;

  @ApiProperty({
    description: 'Số phiên bản',
    example: 1,
  })
  versionNumber: number;

  @ApiProperty({
    description: 'Ghi chú về những thay đổi trong phiên bản này',
    example: '<PERSON><PERSON><PERSON> thiện khả năng xử lý các câu hỏi phức tạp và thêm hỗ trợ đa ngôn ngữ',
    nullable: true,
  })
  changeNotes: string;

  @ApiProperty({
    description: 'Cấu hình chi tiết của phiên bản chiến lược',
    example: {
      priorityHandling: {
        urgent: 'immediate',
        high: 'within-1-hour',
        medium: 'within-24-hours',
        low: 'within-48-hours'
      },
      responseTemplates: {
        greeting: 'Xin chào, tôi có thể giúp gì cho bạn?',
        farewell: 'Cảm ơn bạn đã liên hệ với chúng tôi!'
      },
      languageSupport: ['vi', 'en', 'fr']
    },
  })
  config: any;

  @ApiProperty({
    description: 'ID của nhân viên tạo phiên bản này',
    example: 1,
  })
  createdBy: number;

  @ApiProperty({
    description: 'Thời điểm tạo phiên bản (timestamp millis)',
    example: 1625097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Trạng thái của phiên bản',
    example: 'published',
    enum: ['draft', 'published', 'deprecated'],
  })
  status: string;

  @ApiProperty({
    description: 'Giá của phiên bản chiến lược này',
    example: 99.99,
  })
  price: number;

  constructor(partial: Partial<StrategyAgentVersion>) {
    Object.assign(this, partial);
  }
}

export class StrategyAgentVersionListResponseSchema {
  @ApiProperty({
    description: 'Danh sách phiên bản chiến lược agent',
    type: [StrategyAgentVersionSchema],
  })
  items: StrategyAgentVersionSchema[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    type: 'object',
    properties: {
      totalItems: {
        type: 'number',
        example: 100,
        description: 'Tổng số phiên bản chiến lược agent',
      },
      itemCount: {
        type: 'number',
        example: 10,
        description: 'Số phiên bản chiến lược agent trên trang hiện tại',
      },
      itemsPerPage: {
        type: 'number',
        example: 10,
        description: 'Số phiên bản chiến lược agent trên mỗi trang',
      },
      totalPages: {
        type: 'number',
        example: 10,
        description: 'Tổng số trang',
      },
      currentPage: {
        type: 'number',
        example: 1,
        description: 'Trang hiện tại',
      },
    },
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
