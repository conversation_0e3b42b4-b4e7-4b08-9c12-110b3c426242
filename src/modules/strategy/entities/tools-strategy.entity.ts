import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng functions_strategy trong cơ sở dữ liệu
 * Bảng lưu trữ mối quan hệ giữa các phiên bản chiến lư<PERSON> và các hàm đư<PERSON><PERSON> sử dụng
 */
@Entity('tools_strategy')
export class ToolsStrategy {
  /**
   * ID của phiên bản chiến lư<PERSON>, tham chiếu đến bảng strategy_agent_versions
   * Là một phần của khóa chính
   */
  @PrimaryColumn({ name: 'strategy_version_id' })
  strategyVersionId: number;

  /**
   * ID của hàm, tham chiếu đến bảng admin_functions
   * Là một phần của khóa chính
   */
  @PrimaryColumn('uuid', { name: 'tool_id' })
  toolId: string;

  /**
   * ID của phiên bản hàm, tham chiếu đến bảng admin_function_versions
   */
  @Column({ name: 'version_tool_id' })
  versionToolId: string;
}
