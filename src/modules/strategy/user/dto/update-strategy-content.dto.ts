import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID } from 'class-validator';

/**
 * DTO cho việc cập nhật nội dung chiến lược của người dùng
 */
export class UpdateStrategyContentDto {
  @ApiProperty({
    description: 'ID của agent người dùng',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  agentId?: string;

  @ApiProperty({
    description: 'Nội dung có thể chỉnh sửa của bước',
    example: 'Nội dung đã được tùy chỉnh bởi người dùng',
  })
  @IsString()
  @IsOptional()
  editableContent?: string;
}
