import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho việc gỡ Strategy khỏi tất cả Agent
 */
export class RemoveStrategyDto {
  @ApiProperty({
    description: 'ID của Strategy cần gỡ khỏi tất cả Agent',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @IsNotEmpty({ message: 'ID của Strategy không được để trống' })
  @IsString({ message: 'ID của Strategy phải là chuỗi' })
  strategyId: string;
}
