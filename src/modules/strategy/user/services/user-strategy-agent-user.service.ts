import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserStrategyAgent } from '../../../agent/entities/user-strategy-agent.entity';
import { UserStrategyAgentRepository } from '../../repositories/user-strategy-agent.repository';
import { StrategyAgentUserService } from './strategy-agent-user.service';
import { StrategyAgentVersionUserService } from './strategy-agent-version-user.service';

@Injectable()
export class UserStrategyAgentUserService {
  constructor(
    @InjectRepository(UserStrategyAgent)
    private readonly userStrategyAgentRepository: Repository<UserStrategyAgent>,
    private readonly userStrategyAgentCustomRepository: UserStrategyAgentRepository,
    private readonly strategyAgentUserService: StrategyAgentUserService,
    private readonly strategyAgentVersionUserService: StrategyAgentVersionUserService
  ) { }
}
