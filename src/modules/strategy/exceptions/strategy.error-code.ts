/**
 * <PERSON><PERSON><PERSON> nghĩa các mã lỗi cho Strategy
 */
import { ErrorCode } from '@common/exceptions/app.exception';
import { HttpStatus } from '@nestjs/common';

// Định nghĩa mã lỗi cho module Strategy từ 10000 đến 10099
export const STRATEGY_ERROR_CODES = {
  // Lỗi chung
  GENERAL_ERROR: new ErrorCode(10000, 'Lỗi xử lý chiến lược', HttpStatus.INTERNAL_SERVER_ERROR),
  
  // Lỗi khi truy vấn
  FETCH_FAILED: new ErrorCode(10001, 'Lỗi khi lấy dữ liệu chiến lược', HttpStatus.INTERNAL_SERVER_ERROR),
  NOT_FOUND: new ErrorCode(10002, 'Không tìm thấy chiến lược', HttpStatus.NOT_FOUND),
  STRATEGY_NOT_FOUND: new ErrorCode(10003, 'Không tìm thấy chiến lược', HttpStatus.NOT_FOUND),
  VERSION_NOT_FOUND: new ErrorCode(10004, 'Không tìm thấy phiên bản', HttpStatus.NOT_FOUND),
  ASSIGNMENT_NOT_FOUND: new ErrorCode(10005, 'Không tìm thấy gán chiến lược', HttpStatus.NOT_FOUND),
  RANK_STRATEGY_NOT_FOUND: new ErrorCode(10006, 'Không tìm thấy cấp bậc chiến lược', HttpStatus.NOT_FOUND),
  
  // Lỗi khi tạo/cập nhật
  CREATE_FAILED: new ErrorCode(10020, 'Lỗi khi tạo chiến lược', HttpStatus.INTERNAL_SERVER_ERROR),
  UPDATE_FAILED: new ErrorCode(10021, 'Lỗi khi cập nhật chiến lược', HttpStatus.INTERNAL_SERVER_ERROR),
  DELETE_FAILED: new ErrorCode(10022, 'Lỗi khi xóa chiến lược', HttpStatus.INTERNAL_SERVER_ERROR),
  ASSIGN_FAILED: new ErrorCode(10023, 'Lỗi khi gán chiến lược', HttpStatus.INTERNAL_SERVER_ERROR),
  REMOVE_FAILED: new ErrorCode(10024, 'Lỗi khi gỡ bỏ chiến lược', HttpStatus.INTERNAL_SERVER_ERROR),
  
  // Lỗi khi validate
  INVALID_FORMAT: new ErrorCode(10040, 'Định dạng chiến lược không hợp lệ', HttpStatus.BAD_REQUEST),
  INVALID_STATUS: new ErrorCode(10041, 'Trạng thái chiến lược không hợp lệ', HttpStatus.BAD_REQUEST),
  INVALID_INPUT: new ErrorCode(10042, 'Dữ liệu đầu vào không hợp lệ', HttpStatus.BAD_REQUEST),
};
