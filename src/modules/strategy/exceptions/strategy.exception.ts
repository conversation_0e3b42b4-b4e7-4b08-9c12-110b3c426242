import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi cho module Strategy
 * Dải mã lỗi: 10300-10399
 */
export const STRATEGY_ERROR_CODES = {
  // Lỗi chung
  GENERAL_ERROR: new ErrorCode(
    10300,
    'Lỗi xử lý Strategy',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi khi lấy danh sách Strategy
  FETCH_FAILED: new ErrorCode(
    10301,
    'Không thể lấy danh sách Strategy',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi khi không tìm thấy Strategy
  NOT_FOUND: new ErrorCode(
    10302,
    'Không tìm thấy Strategy',
    HttpStatus.NOT_FOUND,
  ),

  // Lỗi khi tạo Strategy
  CREATE_FAILED: new ErrorCode(
    10303,
    'Không thể tạo Strategy',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi khi cập nhật Strategy
  UPDATE_FAILED: new ErrorCode(
    10304,
    'Không thể cập nhật Strategy',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi khi xóa Strategy
  DELETE_FAILED: new ErrorCode(
    10305,
    'Không thể xóa Strategy',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi khi không tìm thấy phiên bản Strategy
  VERSION_NOT_FOUND: new ErrorCode(
    10306,
    'Không tìm thấy phiên bản Strategy',
    HttpStatus.NOT_FOUND,
  ),

  // Lỗi khi tạo phiên bản Strategy
  VERSION_CREATE_FAILED: new ErrorCode(
    10307,
    'Không thể tạo phiên bản Strategy',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi khi cập nhật phiên bản Strategy
  VERSION_UPDATE_FAILED: new ErrorCode(
    10308,
    'Không thể cập nhật phiên bản Strategy',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  // Lỗi khi không có quyền truy cập Strategy
  ACCESS_DENIED: new ErrorCode(
    10309,
    'Không có quyền truy cập Strategy',
    HttpStatus.FORBIDDEN,
  ),

  // Lỗi khi không tìm thấy liên kết giữa Agent và Strategy
  AGENT_STRATEGY_LINK_NOT_FOUND: new ErrorCode(
    10310,
    'Không tìm thấy liên kết giữa Agent và Strategy',
    HttpStatus.NOT_FOUND,
  ),

  // Lỗi khi gỡ Strategy khỏi Agent
  REMOVE_FROM_AGENT_FAILED: new ErrorCode(
    10311,
    'Không thể gỡ Strategy khỏi Agent',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
  
  // Lỗi khi function không hợp lệ hoặc không tồn tại
  INVALID_FUNCTIONS: new ErrorCode(
    10312,
    'Function không hợp lệ hoặc không tồn tại',
    HttpStatus.BAD_REQUEST,
  ),

  // Lỗi khi ID rank không hợp lệ
  INVALID_RANK_ID: new ErrorCode(
    10315,
    'ID cấp bậc không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  // Lỗi khi dữ liệu đầu vào không hợp lệ
  INVALID_INPUT: new ErrorCode(
    10399,
    'Dữ liệu đầu vào không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),
};
