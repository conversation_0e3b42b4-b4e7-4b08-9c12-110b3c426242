import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { RankStrategy, RankStrategyStatus } from '../entities/rank-strategy.entity';
import { PaginatedResult } from '@common/response/api-response-dto';

/**
 * Repository xử lý các thao tác liên quan đến cấp bậc chiến lược
 */
@Injectable()
export class RankStrategyRepository extends Repository<RankStrategy> {
  constructor(private dataSource: DataSource) {
    super(RankStrategy, dataSource.createEntityManager());
  }

  /**
   * Tạo query cơ bản cho RankStrategy
   * @returns SelectQueryBuilder cho RankStrategy
   */
  private createBaseQuery(): SelectQueryBuilder<RankStrategy> {
    return this.createQueryBuilder('rank_strategy');
  }

  /**
   * Tìm kiếm cấp bậc chiến lược với phân trang
   * @param page Trang hiện tại
   * @param limit Số lượng item trên mỗi trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param status Trạng thái lọc (tùy chọn)
   * @returns Kết quả phân trang
   */
  async findPaginated(
    page: number,
    limit: number,
    search?: string,
    status?: RankStrategyStatus
  ): Promise<PaginatedResult<RankStrategy>> {
    const qb = this.createBaseQuery();

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('(rank_strategy.name ILIKE :search OR rank_strategy.description ILIKE :search)', {
        search: `%${search}%`,
      });
    }

    // Lọc theo trạng thái nếu có
    if (status) {
      qb.andWhere('rank_strategy.status = :status', { status });
    }

    // Thêm phân trang
    qb.skip((page - 1) * limit).take(limit);

    // Sắp xếp theo thời gian tạo mới nhất
    qb.orderBy('rank_strategy.createdAt', 'DESC');

    // Lấy kết quả
    const [items, totalItems] = await qb.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Tìm cấp bậc chiến lược theo ID
   * @param id ID của cấp bậc
   * @returns Thông tin cấp bậc hoặc null nếu không tìm thấy
   */
  async findById(id: number): Promise<RankStrategy | null> {
    return this.createBaseQuery().where('rank_strategy.id = :id', { id }).getOne();
  }
}
