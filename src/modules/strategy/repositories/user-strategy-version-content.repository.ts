import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserStrategyVersionContent } from '@modules/strategy/entities';
import { Transactional } from 'typeorm-transactional';

/**
 * Repository cho entity UserStrategyVersionContent
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến nội dung tùy chỉnh của người dùng cho các bước chiến lược
 */
@Injectable()
export class UserStrategyVersionContentRepository extends Repository<UserStrategyVersionContent> {
  private readonly logger = new Logger(UserStrategyVersionContentRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserStrategyVersionContent, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản
   * @returns Query builder
   */
  createBaseQuery(): SelectQueryBuilder<UserStrategyVersionContent> {
    return this.createQueryBuilder('content');
  }

  /**
   * Tìm nội dung chiến lược theo agent ID và strategy ID
   * @param agentId ID của agent
   * @param strategyId ID của strategy
   * @returns Danh sách nội dung chiến lược
   */
  async findByAgentIdAndStrategyId(
    agentId: string,
    strategyId: string,
  ): Promise<UserStrategyVersionContent[]> {
    return this.createBaseQuery()
      .innerJoin('user_strategy_versions', 'version', 'content.strategy_version_id = version.id')
      .innerJoin('user_strategy_agents', 'usa', 'version.user_strategy_agent_id = usa.id')
      .where('content.agent_id = :agentId', { agentId })
      .andWhere('usa.strategy_agent_id = :strategyId', { strategyId })
      .orderBy('content.step_order', 'ASC')
      .getMany();
  }

  /**
   * Kiểm tra xem agent có nội dung chiến lược không
   * @param agentId ID của agent
   * @param strategyId ID của strategy
   * @returns true nếu có, false nếu không
   */
  async existsByAgentIdAndStrategyId(
    agentId: string,
    strategyId: string,
  ): Promise<boolean> {
    const content = await this.createBaseQuery()
      .innerJoin('user_strategy_versions', 'version', 'content.strategy_version_id = version.id')
      .innerJoin('user_strategy_agents', 'usa', 'version.user_strategy_agent_id = usa.id')
      .where('content.agent_id = :agentId', { agentId })
      .andWhere('usa.strategy_agent_id = :strategyId', { strategyId })
      .getOne();

    return !!content;
  }

  /**
   * Xóa nội dung chiến lược theo agent ID
   * @param agentId ID của agent
   */
  async deleteByAgentId(agentId: string): Promise<void> {
    await this.createQueryBuilder()
      .delete()
      .where('agent_id = :agentId', { agentId })
      .execute();
  }

  /**
   * Tạo nội dung chiến lược mới
   * @param data Dữ liệu nội dung chiến lược
   * @returns Nội dung chiến lược đã tạo
   */
  async createContent(data: Partial<UserStrategyVersionContent>): Promise<UserStrategyVersionContent> {
    const content = this.create(data);
    return this.save(content);
  }

  /**
   * Tìm nội dung chiến lược theo strategy version ID và agent ID
   * @param strategyVersionId ID của phiên bản chiến lược
   * @param agentId ID của agent
   * @returns Danh sách nội dung chiến lược
   */
  async findByStrategyVersionIdAndAgentId(
    strategyVersionId: number,
    agentId: string,
  ): Promise<UserStrategyVersionContent[]> {
    return this.createBaseQuery()
      .where('content.strategy_version_id = :strategyVersionId', { strategyVersionId })
      .andWhere('content.agent_id = :agentId', { agentId })
      .orderBy('content.step_order', 'ASC')
      .getMany();
  }

  /**
   * Tạo nhiều nội dung chiến lược cùng lúc
   * @param strategyVersionId ID của phiên bản chiến lược
   * @param agentId ID của agent
   * @param contentSteps Danh sách các bước nội dung
   */
  @Transactional()
  async createBulkContent(
    strategyVersionId: number,
    agentId: string,
    contentSteps: { id: number, example: string, stepOrder: number }[],
  ): Promise<void> {
    const values = contentSteps.map(step => ({
      strategy_version_id: strategyVersionId,
      strategy_content_step_id: step.id,
      agent_id: agentId,
      editable_example: step.example,
      step_order: step.stepOrder,
      edited: false,
    }));

    if (values.length > 0) {
      await this.createQueryBuilder()
        .insert()
        .into('user_strategy_version_contents')
        .values(values)
        .execute();
    }
  }
}
