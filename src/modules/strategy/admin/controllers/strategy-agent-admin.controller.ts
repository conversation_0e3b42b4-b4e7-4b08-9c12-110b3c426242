import { Body, Controller, Get, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { CurrentEmployee } from '@modules/auth/decorators';
import { StrategyAgentAdminService } from '../services/strategy-agent-admin.service';
import {
  CreateStrategyAgentDto,
  QueryStrategyAgentDto,
  UpdateStrategyAgentDto,
  StrategyAgentResponseDto,
  PaginatedStrategyAgentResponseDto
} from '../dto';

import { SWAGGER_API_TAGS } from '@common/swagger';

@ApiTags(SWAGGER_API_TAGS.ADMIN_STRATEGY)
@Controller('admin/strategies')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class StrategyAgentAdminController {
  constructor(private readonly strategyAgentService: StrategyAgentAdminService) {}

  @Get()
  @ApiOperation({ summary: 'Lấy danh sách Strategy Agent có phân trang' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách Strategy Agent',
    type: PaginatedStrategyAgentResponseDto
  })
  async getStrategies(@Query() queryDto: QueryStrategyAgentDto) {
    const result = await this.strategyAgentService.getStrategies(queryDto);
    return ApiResponseDto.success(result);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết Strategy Agent theo ID' })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết Strategy Agent',
    type: StrategyAgentResponseDto
  })
  async getStrategyDetail(@Param('id') id: string) {
    const result = await this.strategyAgentService.getStrategyDetail(id);
    return ApiResponseDto.success(result);
  }

  @Post()
  @ApiOperation({ summary: 'Tạo mới Strategy Agent' })
  @ApiResponse({
    status: 201,
    description: 'Strategy Agent đã được tạo',
    type: StrategyAgentResponseDto
  })
  async createStrategy(
    @CurrentEmployee('id') employeeId: number,
    @Body() createDto: CreateStrategyAgentDto
  ) {
    await this.strategyAgentService.createStrategy(employeeId, createDto);
    return ApiResponseDto.success(null, 'Tạo Strategy Agent thành công');
  }

  @Put(':id')
  @ApiOperation({ summary: 'Cập nhật Strategy Agent' })
  @ApiResponse({
    status: 200,
    description: 'Strategy Agent đã được cập nhật',
    type: StrategyAgentResponseDto
  })
  async updateStrategy(
    @CurrentEmployee('id') employeeId: number,
    @Param('id') id: string,
    @Body() updateDto: UpdateStrategyAgentDto
  ) {
    const result = await this.strategyAgentService.updateStrategy(employeeId, id, updateDto);
    return ApiResponseDto.success(result);
  }
}
