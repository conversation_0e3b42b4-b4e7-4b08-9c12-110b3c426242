import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';
import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { StrategyAgentVersionAdminService } from '../services';
import { CreateStrategyAgentVersionDto } from '../dto';
import { ApiResponseDto } from '@common/response/api-response-dto';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { CurrentEmployee } from '@modules/auth/decorators';

@ApiTags(SWAGGER_API_TAGS.ADMIN_STRATEGY)
@Controller('admin/strategies')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class StrategyAgentVersionAdminController {
  constructor(private readonly strategyAgentVersionAdminService: StrategyAgentVersionAdminService) { }

  @Post(':strategyId/versions')
  @ApiOperation({ summary: 'Cập nhật hoặc tạo mới phiên bản Strategy' })
  @ApiResponse({ status: 200, description: 'Cập nhật phiên bản Strategy thành công.' })
  @ApiResponse({ status: 201, description: 'Tạo mới phiên bản Strategy thành công.' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy Strategy.' })
  async createOrUpdateVersion(
    @CurrentEmployee('id') employeeId: number,
    @Param('strategyId') strategyId: string,
    @Body() versionDto: CreateStrategyAgentVersionDto
  ) {
    const result = await this.strategyAgentVersionAdminService.createOrUpdateVersion(employeeId, strategyId, versionDto);

    if (result.isNewVersion) {
      return ApiResponseDto.created({ version: result.version }, 'Tạo mới phiên bản Strategy thành công.');
    }

    return ApiResponseDto.success({ version: result.version }, 'Cập nhật phiên bản Strategy thành công.');
  }

  @Get(':strategyId/versions/:versionId')
  @ApiOperation({ summary: 'Lấy chi tiết phiên bản Strategy' })
  @ApiResponse({ status: 200, description: 'Lấy chi tiết phiên bản Strategy thành công.' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy phiên bản Strategy.' })
  async getVersionDetail(
    @Param('strategyId') strategyId: string,
    @Param('versionId') versionId: number
  ) {
    const result = await this.strategyAgentVersionAdminService.getVersionDetail(strategyId, versionId);
    return ApiResponseDto.success({ version: result }, 'Lấy chi tiết phiên bản Strategy thành công.');
  }
}
