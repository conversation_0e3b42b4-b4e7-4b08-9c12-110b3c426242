import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { StrategyStepDto, ConfigAgentDto } from './create-strategy-agent-version.dto';
import { StrategyStatusEnum } from '../../constants/strategy-status.enum';

/**
 * DTO cho việc cập nhật phiên bản Strategy Agent
 */
export class UpdateStrategyAgentVersionDto {
  @ApiProperty({
    description: 'ID của phiên bản (chỉ cần khi cập nhật phiên bản đã tồn tại)',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID phiên bản phải là số' })
  id?: number;

  @ApiProperty({
    description: 'Tên phiên bản',
    example: 'C<PERSON><PERSON> nhật tháng 7',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên phiên bản phải là chuỗi' })
  versionName?: string;

  @ApiProperty({
    description: 'Trạng thái phiên bản',
    enum: StrategyStatusEnum,
    example: StrategyStatusEnum.DRAFT,
    required: false,
  })
  @IsOptional()
  @IsEnum(StrategyStatusEnum, { message: 'Trạng thái phiên bản không hợp lệ' })
  status?: StrategyStatusEnum;

  @ApiProperty({
    description: 'Mô tả những thay đổi so với phiên bản trước đó',
    example: 'Cập nhật nội dung và thêm function phân tích xu hướng',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả thay đổi phải là chuỗi' })
  changeDescription?: string;

  @ApiProperty({
    description: 'Cấu hình chi tiết cho agent',
    type: ConfigAgentDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ConfigAgentDto)
  configAgent?: ConfigAgentDto;

  @ApiProperty({
    description: 'Các bước xử lý của chiến lược',
    type: [StrategyStepDto],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: 'Các bước xử lý phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => StrategyStepDto)
  steps?: StrategyStepDto[];
}
