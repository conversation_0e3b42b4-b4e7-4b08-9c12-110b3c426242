import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';
import { QueryDto } from '@common/dto';
import { RankStrategyStatus } from '../../entities/rank-strategy.entity';

/**
 * DTO cho việc tạo mới cấp bậc chiến lược
 */
export class CreateRankStrategyDto {
  /**
   * Tên của cấp bậc
   * @example "Cấp bậc 1"
   */
  @ApiProperty({ description: 'Tên của cấp bậc', example: 'Cấp bậc 1' })
  @IsNotEmpty({ message: 'Tên cấp bậc không được để trống' })
  @IsString({ message: 'Tên cấp bậc phải là chuỗi' })
  @MaxLength(255, { message: 'Tên cấp bậc không được vượt quá 255 ký tự' })
  name: string;

  /**
   * Đường dẫn hình ảnh đại diện cho cấp bậc
   * @example "strategies/rank-1.png"
   */
  @ApiProperty({ description: 'Đường dẫn hình ảnh đại diện cho cấp bậc', example: 'strategies/rank-1.png' })
  @IsNotEmpty({ message: 'Đường dẫn hình ảnh không được để trống' })
  @IsString({ message: 'Đường dẫn hình ảnh phải là chuỗi' })
  @MaxLength(255, { message: 'Đường dẫn hình ảnh không được vượt quá 255 ký tự' })
  image: string;

  /**
   * Mô tả chi tiết về cấp bậc
   * @example "Cấp bậc dành cho người mới bắt đầu"
   */
  @ApiPropertyOptional({ description: 'Mô tả chi tiết về cấp bậc', example: 'Cấp bậc dành cho người mới bắt đầu' })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;

  /**
   * Trạng thái của cấp bậc
   * @example "ENABLED"
   */
  @ApiPropertyOptional({ 
    description: 'Trạng thái của cấp bậc', 
    enum: RankStrategyStatus, 
    default: RankStrategyStatus.ENABLED,
    example: RankStrategyStatus.ENABLED
  })
  @IsOptional()
  @IsEnum(RankStrategyStatus, { message: 'Trạng thái phải là một trong các giá trị: ENABLED, DISABLED' })
  status?: RankStrategyStatus;
}

/**
 * DTO cho việc cập nhật cấp bậc chiến lược
 */
export class UpdateRankStrategyDto {
  /**
   * Tên của cấp bậc
   * @example "Cấp bậc 1"
   */
  @ApiPropertyOptional({ description: 'Tên của cấp bậc', example: 'Cấp bậc 1' })
  @IsOptional()
  @IsString({ message: 'Tên cấp bậc phải là chuỗi' })
  @MaxLength(255, { message: 'Tên cấp bậc không được vượt quá 255 ký tự' })
  name?: string;

  /**
   * Loại hình ảnh (MIME type) để tạo URL upload
   * @example "image/png"
   */
  @ApiPropertyOptional({ 
    description: 'Loại hình ảnh (MIME type) để tạo URL upload', 
    example: 'image/png' 
  })
  @IsOptional()
  @IsString({ message: 'Loại hình ảnh phải là chuỗi' })
  imageType?: string;

  /**
   * Mô tả chi tiết về cấp bậc
   * @example "Cấp bậc dành cho người mới bắt đầu"
   */
  @ApiPropertyOptional({ description: 'Mô tả chi tiết về cấp bậc', example: 'Cấp bậc dành cho người mới bắt đầu' })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;

  /**
   * Trạng thái của cấp bậc
   * @example "ENABLED"
   */
  @ApiPropertyOptional({ 
    description: 'Trạng thái của cấp bậc', 
    enum: RankStrategyStatus,
    example: RankStrategyStatus.ENABLED
  })
  @IsOptional()
  @IsEnum(RankStrategyStatus, { message: 'Trạng thái phải là một trong các giá trị: ENABLED, DISABLED' })
  status?: RankStrategyStatus;
}

/**
 * DTO cho việc truy vấn danh sách cấp bậc chiến lược
 */
export class QueryRankStrategyDto extends QueryDto {
  /**
   * Lọc theo trạng thái
   * @example "ENABLED"
   */
  @ApiPropertyOptional({ 
    description: 'Lọc theo trạng thái', 
    enum: RankStrategyStatus,
    example: RankStrategyStatus.ENABLED
  })
  @IsOptional()
  @IsEnum(RankStrategyStatus, { message: 'Trạng thái phải là một trong các giá trị: ENABLED, DISABLED' })
  status?: RankStrategyStatus;
}

/**
 * DTO cho việc trả về thông tin cấp bậc chiến lược
 */
export class RankStrategyResponseDto {
  /**
   * ID của cấp bậc
   * @example 1
   */
  @ApiProperty({ description: 'ID của cấp bậc', example: 1 })
  id: number;

  /**
   * Tên của cấp bậc
   * @example "Cấp bậc 1"
   */
  @ApiProperty({ description: 'Tên của cấp bậc', example: 'Cấp bậc 1' })
  name: string;

  /**
   * Đường dẫn hình ảnh đại diện cho cấp bậc
   * @example "https://example.com/images/rank-1.png"
   */
  @ApiProperty({ description: 'Đường dẫn hình ảnh đại diện cho cấp bậc', example: 'https://example.com/images/rank-1.png' })
  image: string;

  /**
   * Mô tả chi tiết về cấp bậc
   * @example "Cấp bậc dành cho người mới bắt đầu"
   */
  @ApiProperty({ description: 'Mô tả chi tiết về cấp bậc', example: 'Cấp bậc dành cho người mới bắt đầu' })
  description: string;

  /**
   * Trạng thái của cấp bậc
   * @example "ENABLED"
   */
  @ApiProperty({ description: 'Trạng thái của cấp bậc', enum: RankStrategyStatus, example: RankStrategyStatus.ENABLED })
  status: RankStrategyStatus;

  /**
   * Thời gian tạo (timestamp)
   * @example 1682506892000
   */
  @ApiProperty({ description: 'Thời gian tạo (timestamp)', example: 1682506892000 })
  createdAt: number;

  /**
   * Thời gian cập nhật gần nhất (timestamp)
   * @example 1682506892000
   */
  @ApiProperty({ description: 'Thời gian cập nhật gần nhất (timestamp)', example: 1682506892000 })
  updatedAt: number;

  /**
   * ID của người tạo
   * @example 1
   */
  @ApiProperty({ description: 'ID của người tạo', example: 1 })
  createdBy: number;

  /**
   * ID của người cập nhật gần nhất
   * @example 1
   */
  @ApiProperty({ description: 'ID của người cập nhật gần nhất', example: 1 })
  updatedBy: number;

  /**
   * Cấp bậc có đang được sử dụng bởi strategy nào không
   * @example true
   */
  @ApiProperty({ description: 'Cấp bậc có đang được sử dụng bởi strategy nào không', example: true })
  isInUse: boolean;
}
