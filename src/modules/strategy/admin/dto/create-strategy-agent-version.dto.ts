import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { StrategyStatusEnum } from '../../constants/strategy-status.enum';

export class StrategyStepDto {
  @ApiProperty({
    description: 'ID của bước (chỉ cần khi cập nhật bước đã tồn tại)',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID bước phải là số' })
  id?: number;

  @ApiProperty({
    description: 'Thứ tự của bước trong chuỗi xử lý',
    example: 1,
  })
  @IsNotEmpty({ message: 'Thứ tự bước không được để trống' })
  @IsNumber({}, { message: 'Thứ tự bước phải là số' })
  stepOrder: number;

  @ApiProperty({
    description: 'Nội dung của bướ<PERSON>, được sử dụng trong xử lý',
    example: 'Hướng dẫn người dùng về sản phẩm mới theo các bước sau...',
  })
  @IsNotEmpty({ message: 'Nội dung không được để trống' })
  @IsString({ message: 'Nội dung phải là chuỗi' })
  content: string;

  @ApiProperty({
    description: 'Ví dụ mẫu cho bước này, người dùng có thể tham khảo',
    example: 'Giới thiệu sản phẩm X với khách hàng bằng cách nhấn mạnh các tính năng chính',
  })
  @IsNotEmpty({ message: 'Ví dụ mẫu không được để trống' })
  @IsString({ message: 'Ví dụ mẫu phải là chuỗi' })
  example: string;
}

export class FunctionDto {
  @ApiProperty({
    description: 'ID của function',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty({ message: 'Function ID không được để trống' })
  @IsUUID('all', { message: 'Function ID phải là UUID hợp lệ' })
  functionId: string;

  @ApiProperty({
    description: 'ID của phiên bản function',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsNotEmpty({ message: 'Version function ID không được để trống' })
  @IsUUID('all', { message: 'Version function ID phải là UUID hợp lệ' })
  versionFunctionId: string;
}

export class ModelConfigDto {
  @ApiProperty({
    description: 'Cấu hình top_p',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'top_p phải là số' })
  top_p?: number;

  @ApiProperty({
    description: 'Cấu hình temperature',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'temperature phải là số' })
  temperature?: number;
}

export class ConfigAgentDto {
  @ApiProperty({
    description: 'ID của model sử dụng',
    example: 'gpt-4o',
  })
  @IsNotEmpty({ message: 'Model ID không được để trống' })
  @IsString({ message: 'Model ID phải là chuỗi' })
  modelId: string;

  @ApiProperty({
    description: 'Cấu hình chi tiết cho model',
    type: ModelConfigDto,
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ModelConfigDto)
  modelConfig?: ModelConfigDto;

  @ApiProperty({
    description: 'Prompt hệ thống định nghĩa cho agent',
    example: 'Bạn là một trợ lý AI chuyên về tư vấn tài chính...',
  })
  @IsNotEmpty({ message: 'System prompt không được để trống' })
  @IsString({ message: 'System prompt phải là chuỗi' })
  systemPrompt: string;

  @ApiProperty({
    description: 'Danh sách các function sử dụng',
    type: [FunctionDto],
  })
  @IsNotEmpty({ message: 'Functions không được để trống' })
  @IsArray({ message: 'Functions phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => FunctionDto)
  functions: FunctionDto[];

  @ApiProperty({
    description: 'ID của vector store sử dụng',
    example: 'vs-123456',
  })
  @IsNotEmpty({ message: 'Vector store ID không được để trống' })
  @IsString({ message: 'Vector store ID phải là chuỗi' })
  vectorStoreId: string;
}

/**
 * DTO cho việc tạo mới hoặc cập nhật phiên bản Strategy Agent
 */
export class CreateStrategyAgentVersionDto {
  @ApiProperty({
    description: 'ID của phiên bản (chỉ cần khi cập nhật phiên bản đã tồn tại)',
    example: 2,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID phiên bản phải là số' })
  id?: number;

  @ApiProperty({
    description: 'Tên phiên bản',
    example: 'Cập nhật tháng 7',
  })
  @IsNotEmpty({ message: 'Tên phiên bản không được để trống' })
  @IsString({ message: 'Tên phiên bản phải là chuỗi' })
  versionName: string;

  @ApiProperty({
    description: 'Trạng thái phiên bản',
    enum: StrategyStatusEnum,
    example: StrategyStatusEnum.DRAFT,
  })
  @IsNotEmpty({ message: 'Trạng thái phiên bản không được để trống' })
  @IsEnum(StrategyStatusEnum, { message: 'Trạng thái phiên bản không hợp lệ' })
  status: StrategyStatusEnum;

  @ApiProperty({
    description: 'Mô tả những thay đổi so với phiên bản trước đó',
    example: 'Cập nhật nội dung và thêm function phân tích xu hướng',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả thay đổi phải là chuỗi' })
  changeDescription?: string;

  @ApiProperty({
    description: 'Cấu hình chi tiết cho agent',
    type: ConfigAgentDto,
  })
  @IsNotEmpty({ message: 'Cấu hình không được để trống' })
  @ValidateNested()
  @Type(() => ConfigAgentDto)
  configAgent: ConfigAgentDto;

  @ApiProperty({
    description: 'Các bước xử lý của chiến lược',
    type: [StrategyStepDto],
  })
  @IsNotEmpty({ message: 'Các bước xử lý không được để trống' })
  @IsArray({ message: 'Các bước xử lý phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => StrategyStepDto)
  steps: StrategyStepDto[];
}
