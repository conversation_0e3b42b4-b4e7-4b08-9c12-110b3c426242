import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response/api-response-dto';
import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CdnService } from '@shared/services/cdn.service';
import { S3Service } from '@shared/services/s3.service';
import { TimeIntervalEnum } from '@shared/utils';
import { Repository, In } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { StrategyStatusEnum } from '../../constants/strategy-status.enum';
import { RankStrategy } from '../../entities/rank-strategy.entity';
import { StrategyAgent } from '../../entities/strategy-agent.entity';
import { STRATEGY_ERROR_CODES } from '../../exceptions';
import { StrategyAgentRepository } from '../../repositories/strategy-agent.repository';
import {
  CreateStrategyAgentDto,
  PaginatedStrategyAgentResponseDto,
  QueryStrategyAgentDto,
  StrategyAgentResponseDto,
  UpdateStrategyAgentDto,
} from '../dto';
import { StrategyAgentVersionAdminService } from './strategy-agent-version-admin.service';

@Injectable()
export class StrategyAgentAdminService {
  private readonly logger = new Logger(StrategyAgentAdminService.name);

  constructor(
    @InjectRepository(StrategyAgent)
    private readonly strategyAgentRepository: Repository<StrategyAgent>,
    private readonly strategyAgentCustomRepository: StrategyAgentRepository,
    @InjectRepository(RankStrategy)
    private readonly rankStrategyRepository: Repository<RankStrategy>,
    @Inject(forwardRef(() => StrategyAgentVersionAdminService))
    private readonly strategyAgentVersionService: StrategyAgentVersionAdminService,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Tạo mới Strategy
   * @param employeeId ID của nhân viên tạo
   * @param createDto Thông tin Strategy cần tạo
   * @returns URL của ảnh xếp hạng
   */
  @Transactional()
  async createStrategy(
    employeeId: number,
    createDto: CreateStrategyAgentDto,
  ): Promise<string> {
    try {
      // Kiểm tra tồn tại của rankId
      const rankId = createDto.rankId || 1;
      const rank = await this.rankStrategyRepository.findOne({
        where: { id: rankId },
      });
      if (!rank) {
        throw new AppException(
          STRATEGY_ERROR_CODES.INVALID_RANK_ID,
          `Không tìm thấy cấp bậc với ID ${rankId}.`,
        );
      }

      // Tạo đối tượng Strategy mới
      const strategy = new StrategyAgent();
      strategy.name = createDto.name;
      strategy.description = createDto.description || '';
      strategy.rankId = rankId;
      strategy.status =
        createDto.versionInfo.status || StrategyStatusEnum.DRAFT; // Sử dụng trạng thái từ DTO

      strategy.tags = createDto.tags || [];
      strategy.createdBy = employeeId;
      strategy.updatedBy = employeeId;

      // Lưu Strategy vào database
      await this.strategyAgentRepository.save(strategy);

      // Tạo phiên bản đầu tiên cho Strategy
      const versionDto = {
        versionName: createDto.versionInfo.versionName,
        status: createDto.versionInfo.status || StrategyStatusEnum.DRAFT, // Sử dụng trạng thái từ DTO
        configAgent: createDto.config,
        steps: createDto.steps,
      };

      // Gọi service tạo phiên bản
      await this.strategyAgentVersionService.createOrUpdateVersion(
        employeeId,
        strategy.id,
        versionDto,
      );

      // Không còn cần trả về URL tạm thởi nữa vì không còn xử lý ảnh riêng
      return '';
    } catch (error) {
      this.logger.error(`Failed to create strategy: ${error.message}`);
      throw new AppException(STRATEGY_ERROR_CODES.CREATE_FAILED, error.message);
    }
  }

  /**
   * Lấy danh sách Strategy với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách Strategy với phân trang
   */
  async getStrategies(queryDto: QueryStrategyAgentDto): Promise<PaginatedStrategyAgentResponseDto> {
    try {
      const paginatedResult =
        await this.strategyAgentCustomRepository.findPaginated(queryDto);
      return this.mapToPaginatedResponseDto(paginatedResult);
    } catch (error) {
      this.logger.error(`Failed to get strategies: ${error.message}`);
      throw new AppException(STRATEGY_ERROR_CODES.FETCH_FAILED, error.message);
    }
  }

  /**
   * Lấy chi tiết Strategy theo ID
   * @param strategyId ID của Strategy
   * @returns Thông tin chi tiết Strategy
   */
  async getStrategyDetail(
    strategyId: string,
  ): Promise<StrategyAgentResponseDto> {
    try {
      const strategy =
        await this.strategyAgentCustomRepository.findById(strategyId);
      if (!strategy) {
        throw new AppException(
          STRATEGY_ERROR_CODES.NOT_FOUND,
          `Không tìm thấy Strategy với ID ${strategyId}.`,
        );
      }

      return this.mapToResponseDto(strategy);
    } catch (error) {
      this.logger.error(`Failed to get strategy detail: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(STRATEGY_ERROR_CODES.FETCH_FAILED, error.message);
    }
  }

  /**
   * Cập nhật thông tin Strategy
   * @param employeeId ID của nhân viên cập nhật
   * @param strategyId ID của Strategy cần cập nhật
   * @param updateDto Thông tin cập nhật
   * @returns Thông tin Strategy sau khi cập nhật
   */
  @Transactional()
  async updateStrategy(
    employeeId: number,
    strategyId: string,
    updateDto: UpdateStrategyAgentDto,
  ): Promise<StrategyAgentResponseDto> {
    try {
      // Kiểm tra Strategy có tồn tại không
      const strategy = await this.getStrategyDetail(strategyId);

      // Kiểm tra rankId có hợp lệ không
      if (updateDto.rankId !== undefined) {
        const rank = await this.rankStrategyRepository.findOne({
          where: { id: updateDto.rankId },
        });
        if (!rank) {
          throw new AppException(
            STRATEGY_ERROR_CODES.INVALID_RANK_ID,
            `Không tìm thấy cấp bậc với ID ${updateDto.rankId}.`,
          );
        }
      }

      // Cập nhật thông tin
      const updateData: Partial<StrategyAgent> = {
        ...updateDto,
        updatedBy: employeeId,
      };

      const affected =
        await this.strategyAgentCustomRepository.updateStrategyAgent(
          strategyId,
          updateData,
        );
      if (affected === 0) {
        throw new AppException(
          STRATEGY_ERROR_CODES.NOT_FOUND,
          `Không tìm thấy Strategy với ID ${strategyId}.`,
        );
      }

      // Lấy thông tin mới nhất
      return await this.getStrategyDetail(strategyId);
    } catch (error) {
      this.logger.error(`Failed to update strategy: ${error.message}`);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(STRATEGY_ERROR_CODES.UPDATE_FAILED, error.message);
    }
  }

  /**
   * Chuyển đổi entity StrategyAgent sang DTO response
   * @param strategy Entity StrategyAgent
   * @returns DTO response
   */
  private async mapToResponseDto(strategy: StrategyAgent): Promise<StrategyAgentResponseDto> {
    const response = new StrategyAgentResponseDto();
    response.id = strategy.id;
    response.name = strategy.name;
    response.description = strategy.description;
    response.tags = strategy.tags;
    response.status = strategy.status;
    response.createdAt = strategy.createdAt;
    response.updatedAt = strategy.updatedAt;
    response.createdBy = strategy.createdBy;
    response.updatedBy = strategy.updatedBy;

    // Xử lý thông tin rank nếu có
    if (strategy.rankId) {
      // Lấy thông tin rank từ repository
      const rank = await this.rankStrategyRepository.findOne({
        where: { id: strategy.rankId }
      });

      if (rank) {
        response.rank = {
          id: rank.id,
          name: rank.name,
          imageUrl: rank.image
            ? this.cdnService.generateUrlView(
                rank.image,
                TimeIntervalEnum.ONE_DAY,
              ) || ''
            : '',
        };
      } else {
        // Nếu không tìm thấy rank, cung cấp giá trị mặc định
        response.rank = {
          id: 0,
          name: '',
          imageUrl: '',
        };
      }
    } else {
      // Nếu không có thông tin rank, cung cấp giá trị mặc định
      response.rank = {
        id: 0,
        name: '',
        imageUrl: '',
      };
    }

    return response;
  }

  /**
   * Chuyển đổi danh sách entity StrategyAgent sang DTO response có phân trang
   * @param paginatedResult Kết quả phân trang của entity
   * @returns DTO response có phân trang
   */
  private async mapToPaginatedResponseDto(
    paginatedResult: PaginatedResult<StrategyAgent>,
  ): Promise<PaginatedStrategyAgentResponseDto> {
    const { items, meta } = paginatedResult;

    // Xử lý Promise.all để đảm bảo tất cả các promise đều hoàn thành
    const mappedItems = await Promise.all(items.map((item) => this.mapToResponseDto(item)));

    return {
      items: mappedItems,
      meta,
    };
  }
}
