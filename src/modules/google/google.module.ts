import { Module } from '@nestjs/common';
import { MulterModule } from '@nestjs/platform-express';
import { GoogleController } from './google.controller';
import { GoogleAdsController } from './google-ads.controller';
import { GoogleApiModule } from '@/shared/services/google';

@Module({
  imports: [
    GoogleApiModule,
    MulterModule.register({
      limits: {
        fileSize: 10 * 1024 * 1024, // 10MB
      },
    }),
  ],
  controllers: [GoogleController, GoogleAdsController],
})
export class GoogleModule {}
