import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Agent } from '../interfaces/run-chat.interface';

/**
 * DTO cho model config trong response
 */
export class ModelConfigResponseDto {
  @ApiProperty({
    description: 'ID của model',
    example: 'gpt-4',
  })
  model_id: string;

  @ApiProperty({
    description: 'Giá trị top_p',
    example: 0.9,
  })
  top_p: number;

  @ApiProperty({
    description: 'Giá trị top_k',
    example: 50,
  })
  top_k: number;

  @ApiProperty({
    description: 'Giá trị temperature',
    example: 0.7,
  })
  temperature: number;

  @ApiProperty({
    description: 'Số lượng token tối đa',
    example: 2000,
  })
  max_token: number;

  @ApiProperty({
    description: 'Nhà cung cấp model',
    example: 'openai',
  })
  provider: string;
}

/**
 * DTO cho reconnect config trong response
 */
export class ReconnectResponseDto {
  @ApiProperty({
    description: 'Bật/tắt tính năng reconnect',
    example: true,
  })
  enable: boolean;

  @ApiProperty({
    description: 'Thời gian delay giữa các lần kết nối lại (ms)',
    example: 1000,
  })
  delayMs: number;

  @ApiProperty({
    description: 'Số lần thử kết nối lại tối đa',
    example: 3,
  })
  maxAttempts: number;
}

/**
 * DTO cho MCP config trong response
 */
export class McpConfigResponseDto {
  @ApiProperty({
    description: 'Tên server MCP',
    example: 'mcp.example.com',
  })
  mcpNameServer: string;

  @ApiProperty({
    description: 'Port của server MCP',
    example: 8080,
  })
  mcpPort: number;

  @ApiProperty({
    description: 'URL của server MCP',
    example: 'https://mcp.example.com:8080',
  })
  url: string;

  @ApiProperty({
    description: 'Sử dụng Node EventSource',
    example: true,
  })
  useNodeEventSource: boolean;

  @ApiProperty({
    description: 'Header cho request',
    example: { Authorization: 'Bearer token' },
  })
  header: Record<string, string>;

  @ApiProperty({
    description: 'Cấu hình reconnect',
    type: ReconnectResponseDto,
  })
  reconnect: ReconnectResponseDto;
}

/**
 * DTO cho agent trong response
 */
export class AgentResponseDto {
  @ApiProperty({
    description: 'ID của người dùng',
    example: 1,
  })
  user_id: number;

  @ApiProperty({
    description: 'ID của agent',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiPropertyOptional({
    description: 'Tên của agent',
    example: 'Assistant',
  })
  name?: string;

  @ApiPropertyOptional({
    description: 'Mô tả của agent',
    example: 'Trợ lý ảo thông minh',
  })
  description?: string;

  @ApiProperty({
    description: 'Cấu hình model',
    type: ModelConfigResponseDto,
  })
  model: ModelConfigResponseDto;

  @ApiProperty({
    description: 'Hướng dẫn cho agent',
    example: 'Bạn là trợ lý ảo thông minh, hãy giúp người dùng giải đáp các thắc mắc.',
  })
  instruction: string;

  @ApiPropertyOptional({
    description: 'Cấu hình MCP',
    type: McpConfigResponseDto,
  })
  mcp_config?: McpConfigResponseDto;

  @ApiPropertyOptional({
    description: 'Danh sách agent con',
    type: [AgentResponseDto],
  })
  children?: AgentResponseDto[];
}

/**
 * DTO cho response của run chat
 */
export class RunChatResponseDto {
  @ApiProperty({
    description: 'Thông tin agent',
    type: AgentResponseDto,
  })
  agent: AgentResponseDto;

  constructor(agent: Agent) {
    this.agent = agent as AgentResponseDto;
  }
}
