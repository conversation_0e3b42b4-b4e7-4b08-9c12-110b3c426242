import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { MessageReq } from "../interfaces/message-req.interface";

enum TypeAgent {
    USER = 'USER',
    SYSTEM = 'SYSTEM'
}

/**
 * DTO cho việc tạo một run chat mới
 */
export class CreateRunDto {
  @ApiProperty({
    description: 'ID của agent',
    example: 1,
    required: true,
    type: Number
  })
  @IsNumber({}, { message: 'ID agent phải là số' })
  @IsNotEmpty({ message: 'ID agent không được để trống' })
  agentId: number;

   @ApiProperty({
    description: 'Type Agent',
    enum: TypeAgent,
    example: TypeAgent.USER,
    required: true
  })
  @IsEnum({})
  @IsNotEmpty({ message: 'Type Agent không được để trống' })
  typeAgent: TypeAgent;

  @ApiProperty({
    description: 'Thông tin tin nhắn',
    type: Object,
    example: {
      text: 'Xin chào, tôi cần hỗ trợ',
      image: 'base64...',
      file: 'base64...',
      template: 'template_id'
    },
    isArray: false
  })
  @ValidateNested()
  @Type(() => Object)
  @IsNotEmpty({ message: 'Tin nhắn không được để trống' })
  message: MessageReq;
}
