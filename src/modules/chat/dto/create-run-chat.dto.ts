import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString, IsUUID } from 'class-validator';

/**
 * Enum cho loại agent
 */
export enum TypeAgentEnum {
  ADMIN = 'admin',
  USER = 'user',
}

/**
 * DTO cho việc tạo run chat
 */
export class CreateRunChatDto {
  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsUUID()
  @IsNotEmpty()
  agent_id: string;

  /**
   * Loại agent (admin hoặc user)
   */
  @ApiProperty({
    description: 'Loại agent (admin hoặc user)',
    enum: TypeAgentEnum,
    example: TypeAgentEnum.USER,
  })
  @IsEnum(TypeAgentEnum)
  @IsNotEmpty()
  typeAgent: TypeAgentEnum;

  /**
   * Nội dung tin nhắn
   */
  @ApiProperty({
    description: 'Nội dung tin nhắn',
    example: '<PERSON><PERSON> chào, tôi cần giúp đỡ về...',
  })
  @IsString()
  @IsNotEmpty()
  message: string;
}
