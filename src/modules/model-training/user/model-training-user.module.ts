import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  BaseModel,
  DataFineTuning,
  DataFineTuningModel,
  FineTuningModel,
  UserDataFineTuning,
  UserProviderModel,
  UserFineTuningModel
} from '../entities';
import { ApiKeyEncryptionHelper } from '../helpers/api-key-encryption.helper';
import { BaseModelValidationHelper } from '../helpers/base-model-validation.helper';
import { DataFineTuningValidationHelper } from '../helpers/data-fine-tuning-validation.helper';
import { FineTuningModelValidationHelper } from '../helpers/fine-tuning-model-validation.helper';
import { FineTuningModelIdValidatorHelper } from '../helpers/fine-tuning-model-id-validator.helper';
import {
  AdminProviderModelRepository,
  BaseModelRepository,
  DataFineTuningModelRepository,
  FineTuningModelRepository,
  UserDataFineTuningRepository,
  UserProviderModelRepository,
  UserFineTuningModelRepository
} from '../repositories';
import {
  BaseModelUserController,
  UserProviderModelController,
  UserDataFineTuningController,
  FineTuningModelUserController
} from './controllers';
import {
  BaseModelUserService,
  UserProviderModelService,
  UserDataFineTuningService,
  FineTuningModelUserService
} from './services';
import { DataFineTuningRepository } from '../repositories/data-fine-tuning.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      BaseModel,
      FineTuningModel,
      DataFineTuningModel,
      DataFineTuning,
      UserDataFineTuning,
      UserProviderModel,
      UserFineTuningModel
    ]),
    ConfigModule
  ],
  controllers: [
    BaseModelUserController,
    UserProviderModelController,
    UserDataFineTuningController,
    FineTuningModelUserController
  ],
  providers: [
    BaseModelUserService,
    UserProviderModelService,
    UserDataFineTuningService,
    FineTuningModelUserService,
    AdminProviderModelRepository,
    BaseModelRepository,
    FineTuningModelRepository,
    DataFineTuningModelRepository,
    UserDataFineTuningRepository,
    DataFineTuningRepository,
    UserProviderModelRepository,
    UserFineTuningModelRepository,
    BaseModelValidationHelper,
    FineTuningModelValidationHelper,
    DataFineTuningValidationHelper,
    ApiKeyEncryptionHelper,
    FineTuningModelIdValidatorHelper,
  ],
  exports: [
    BaseModelUserService,
    UserProviderModelService,
    UserDataFineTuningService,
    FineTuningModelUserService
  ],
})
export class ModelTrainingUserModule { }
