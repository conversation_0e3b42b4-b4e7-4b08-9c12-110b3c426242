import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@/common/dto';
import { DataFineTunningStatusEnum } from '@modules/model-training/constants/data-fine-tunning.enum';

/**
 * DTO cho truy vấn dữ liệu fine-tuning của admin
 */
export class AdminDataFineTuningQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên',
    example: 'Training Dataset'
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái',
    enum: DataFineTunningStatusEnum,
    example: DataFineTunningStatusEnum.DRAFT
  })
  @IsOptional()
  @IsEnum(DataFineTunningStatusEnum)
  @Type(() => Number)
  status?: DataFineTunningStatusEnum;

  @ApiPropertyOptional({
    description: '<PERSON>ọ<PERSON> theo nhân viên tạo',
    example: 1
  })
  @IsOptional()
  @Type(() => Number)
  createdBy?: number;

  @ApiPropertyOptional({
    description: 'Lọc theo nhân viên cập nhật',
    example: 1
  })
  @IsOptional()
  @Type(() => Number)
  updatedBy?: number;

  @ApiPropertyOptional({
    description: 'Lọc theo nhân viên xóa',
    example: 1
  })
  @IsOptional()
  @Type(() => Number)
  deletedBy?: number;
} 