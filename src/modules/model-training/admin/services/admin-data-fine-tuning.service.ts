import { QueryDto } from '@/common/dto';
import { AppException } from '@/common/exceptions/app.exception';
import { PaginatedResult } from '@/common/response';
import { DataFineTunningStatusEnum } from '@modules/model-training/constants/data-fine-tunning.enum';
import { AdminDataFineTuningResponseDto } from '@modules/model-training/admin/dto/data-fine-tuning/admin-data-fine-tuning-response.dto';
import { CreateDataFineTuningReq } from '@modules/model-training/dto/data-fine-tuning/create-data-fine-tuning.dto';
import {
  DeleteManyDto,
  UpdateDataFineTuningDto,
} from '@modules/model-training/dto/data-fine-tuning/data-fine-tuning.dto';
import { DataFineTuning, AdminDataFineTuning } from '@modules/model-training/entities';
import { MODEL_TRAINING_ERROR_CODES } from '@modules/model-training/exceptions';
import {
  AdminDataFineTuningRepository,
  DataFineTuningRepository,
} from '@modules/model-training/repositories';
import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { Transactional } from 'typeorm-transactional';

/**
 * Service xử lý các thao tác liên quan đến quản lý dữ liệu fine-tuning cho admin
 */
@Injectable()
export class AdminDataFineTuningService {
  private readonly logger = new Logger(AdminDataFineTuningService.name);

  constructor(
    private readonly dataFineTuningRepository: DataFineTuningRepository,
    private readonly adminDataFineTuningRepository: AdminDataFineTuningRepository,
    private readonly employeeInfoService: EmployeeInfoService,
  ) {}

  /**
   * Tạo mới một bộ dữ liệu fine-tuning
   * @param createDto Dữ liệu để tạo bộ dữ liệu
   * @param employeeId ID của nhân viên tạo bộ dữ liệu
   * @returns ID của bộ dữ liệu đã tạo
   */
  @Transactional()
  async createDataFineTuning(
    createDto: CreateDataFineTuningReq,
    employeeId: number,
  ): Promise<string> {
    try {
      const now = Date.now();

      // Tạo bản ghi dữ liệu fine-tuning
      const newData = this.dataFineTuningRepository.create({
        name: createDto.name,
        description: createDto.description,
        trainData: createDto.trainData,
        validationData: createDto.validationData,
        createdAt: now,
        updatedAt: now,
        isForSale: false,
        status: DataFineTunningStatusEnum.APPROVED,
      });

      // Lưu bản ghi dữ liệu fine-tuning
      const savedData = await this.dataFineTuningRepository.save(newData);

      // Tạo bản ghi admin data fine tuning
      const adminDataFineTuning = this.adminDataFineTuningRepository.create({
        id: savedData.id,
        createdBy: employeeId,
        updatedBy: employeeId, // Thiết lập updatedBy giống với createdBy khi tạo mới
      });

      // Lưu bản ghi admin
      await this.adminDataFineTuningRepository.save(adminDataFineTuning);

      return savedData.id;
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo dữ liệu fine-tuning: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_FAILED,
        `Lỗi khi tạo dữ liệu fine-tuning: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật thông tin bộ dữ liệu fine-tuning
   * @param id ID của bộ dữ liệu cần cập nhật
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên thực hiện cập nhật
   */
  @Transactional()
  async updateDataFineTuning(
    id: string,
    updateDto: UpdateDataFineTuningDto,
    employeeId: number,
  ): Promise<void> {
    try {
      // Tìm dữ liệu fine-tuning
      const dataFineTuning = await this.dataFineTuningRepository.findById(id);
      if (!dataFineTuning) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND,
          'Không tìm thấy dữ liệu fine-tuning',
        );
      }

      // Chỉ cho phép cập nhật khi trạng thái là DRAFT
      if (dataFineTuning.status !== DataFineTunningStatusEnum.DRAFT) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.INVALID_STATUS,
          'Chỉ có thể cập nhật dữ liệu ở trạng thái DRAFT',
        );
      }

      // Không cho phép cập nhật khi isForSale là true
      if (dataFineTuning.isForSale) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.INVALID_STATUS,
          'Không thể cập nhật dữ liệu đang được rao bán',
        );
      }

      // Cập nhật thông tin
      await this.dataFineTuningRepository.update(id, {
        name: updateDto.name,
        description: updateDto.description,
        trainData: updateDto.trainData,
        validationData: updateDto.validationData,
        updatedAt: Date.now(),
      });

      // Cập nhật thông tin admin
      await this.adminDataFineTuningRepository.update(id, {
        updatedBy: employeeId,
      });
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật dữ liệu fine-tuning: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.UPDATE_FAILED,
        `Lỗi khi cập nhật dữ liệu fine-tuning: ${error.message}`,
      );
    }
  }

  /**
   * Xóa một bộ dữ liệu fine-tuning
   * @param id ID của bộ dữ liệu cần xóa
   * @param employeeId ID của nhân viên thực hiện xóa
   */
  @Transactional()
  async deleteDataFineTuning(id: string, employeeId: number): Promise<void> {
    try {
      // Tìm dữ liệu fine-tuning
      const dataFineTuning = await this.dataFineTuningRepository.findById(id);
      if (!dataFineTuning) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND,
          'Không tìm thấy dữ liệu fine-tuning',
        );
      }

      // Không cho phép xóa khi isForSale là true
      if (dataFineTuning.isForSale) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.INVALID_STATUS,
          'Không thể xóa dữ liệu đang được rao bán',
        );
      }

      // Xóa mềm bản ghi
      const now = Date.now();
      await this.dataFineTuningRepository.update(id, {
        deletedAt: now,
        status: DataFineTunningStatusEnum.DELETED,
      });

      // Cập nhật thông tin admin
      await this.adminDataFineTuningRepository.update(id, {
        deletedBy: employeeId,
      });
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa dữ liệu fine-tuning: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.DELETE_FAILED,
        `Lỗi khi xóa dữ liệu fine-tuning: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều bộ dữ liệu fine-tuning
   * @param deleteDto Danh sách ID của các bộ dữ liệu cần xóa
   * @param employeeId ID của nhân viên thực hiện xóa
   */
  @Transactional()
  async deleteMultipleDataFineTuning(
    deleteDto: DeleteManyDto,
    employeeId: number,
  ): Promise<void> {
    try {
      // Kiểm tra từng bản ghi
      for (const id of deleteDto.ids) {
        const dataFineTuning = await this.dataFineTuningRepository.findById(id);
        if (!dataFineTuning) {
          throw new AppException(
            MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND,
            `Không tìm thấy dữ liệu fine-tuning với ID ${id}`,
          );
        }

        // Không cho phép xóa khi isForSale là true
        if (dataFineTuning.isForSale) {
          throw new AppException(
            MODEL_TRAINING_ERROR_CODES.INVALID_STATUS,
            `Không thể xóa dữ liệu đang được rao bán (ID: ${id})`,
          );
        }
      }

      // Xóa mềm các bản ghi
      const now = Date.now();
      for (const id of deleteDto.ids) {
        await this.dataFineTuningRepository.update(id, {
          deletedAt: now,
          status: DataFineTunningStatusEnum.DELETED,
        });

        // Cập nhật thông tin admin
        await this.adminDataFineTuningRepository.update(id, {
          deletedBy: employeeId,
        });
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa nhiều dữ liệu fine-tuning: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.DELETE_FAILED,
        `Lỗi khi xóa nhiều dữ liệu fine-tuning: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách dữ liệu fine-tuning
   * @param query Tham số truy vấn và phân trang
   * @returns Danh sách dữ liệu fine-tuning
   */
  async getDataFineTuningList(
    query: QueryDto,
  ): Promise<PaginatedResult<DataFineTuning>> {
    try {
      // Sử dụng phương thức findAll đã được thêm vào repository
      return await this.dataFineTuningRepository.findAll(query);
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách dữ liệu fine-tuning: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR,
        `Lỗi khi lấy danh sách dữ liệu fine-tuning: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết dữ liệu fine-tuning
   * @param id ID của dữ liệu fine-tuning
   * @returns Thông tin chi tiết dữ liệu fine-tuning với thông tin người tạo và người cập nhật
   */
  async getDataFineTuningDetail(id: string): Promise<AdminDataFineTuningResponseDto> {
    try {
      // Tìm dữ liệu fine-tuning và thông tin admin
      const [dataFineTuning, adminDataFineTuning] = await Promise.all([
        this.dataFineTuningRepository.findById(id),
        this.adminDataFineTuningRepository.findById(id),
      ]);

      if (!dataFineTuning || !adminDataFineTuning) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.DATA_FINE_TUNING_NOT_FOUND,
          'Không tìm thấy dữ liệu fine-tuning',
        );
      }

      // Lấy thông tin nhân viên tạo, cập nhật và xóa từ EmployeeInfoService
      const [createdByEmployee, updatedByEmployee, deletedByEmployee] = await Promise.all([
        adminDataFineTuning.createdBy ? this.employeeInfoService.getEmployeeInfo(adminDataFineTuning.createdBy) : null,
        adminDataFineTuning.updatedBy ? this.employeeInfoService.getEmployeeInfo(adminDataFineTuning.updatedBy) : null,
        adminDataFineTuning.deletedBy ? this.employeeInfoService.getEmployeeInfo(adminDataFineTuning.deletedBy) : null,
      ]);

      // Chuyển đổi dữ liệu sang DTO để trả về
      const responseDto = plainToInstance(AdminDataFineTuningResponseDto, {
        ...dataFineTuning,
        createdByEmployee,
        updatedByEmployee,
        deletedByEmployee,
      }, { excludeExtraneousValues: true });

      return responseDto;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy chi tiết dữ liệu fine-tuning: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR,
        `Lỗi khi lấy chi tiết dữ liệu fine-tuning: ${error.message}`,
      );
    }
  }
}
