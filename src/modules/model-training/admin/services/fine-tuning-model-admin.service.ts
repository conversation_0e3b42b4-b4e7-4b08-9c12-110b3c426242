import { PaginatedResult } from '@/common/response';
import { EmployeeInfoService } from '@/modules/employee/services/employee-info.service';
import { CreateAdminFineTuningModelDto } from '@/modules/model-training/dto/fine-tuning-model/create-admin-fine-tuning-model.dto';
import { FineTuningModelQueryDto } from '@/modules/model-training/dto/fine-tuning-model/fine-tuning-model-query.dto';
import { AdminFineTuningModelDetailRes, FineTuningModelPaginatedRes } from '@/modules/model-training/dto/fine-tuning-model/fine-tuning-model.dto';
import { MODEL_TRAINING_ERROR_CODES } from '@/modules/model-training/exceptions/model-training.exception';
import { AdminFineTuningModelRepository } from '@/modules/model-training/repositories/admin-fine-tuning-model.repository';
import { BaseModelRepository } from '@/modules/model-training/repositories/base-model.repository';
import { CdnService } from '@/shared/services/cdn.service';
import { TimeIntervalEnum } from '@/shared/utils';
import { AppException } from '@common/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';

/**
 * Service xử lý các thao tác liên quan đến mô hình fine-tuning cho admin
 */
@Injectable()
export class FineTuningModelAdminService {
  private readonly logger = new Logger(FineTuningModelAdminService.name);

  constructor(
    private readonly adminFineTuningModelRepository: AdminFineTuningModelRepository,
    private readonly baseModelRepository: BaseModelRepository,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly cdnService: CdnService,
  ) { }

  /**
   * Tạo mới mô hình fine-tuning
   * @param dto Dữ liệu tạo mô hình
   * @param employeeId ID của nhân viên tạo mô hình
   * @returns Thông tin mô hình đã tạo
   */
  async create(dto: CreateAdminFineTuningModelDto, employeeId: number): Promise<AdminFineTuningModelDetailRes> {
    try {
      // Kiểm tra base model có tồn tại không
      const baseModel = await this.baseModelRepository.findById(dto.modelBaseId);
      if (!baseModel) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.MODEL_NOT_FOUND, `Không tìm thấy base model với ID ${dto.modelBaseId}`);
      }

      // Kiểm tra ID fine-tuning model có tồn tại trong provider không
      await this.adminFineTuningModelRepository.validateFineTuningModelId(
        dto.id,
        dto.modelBaseId,
        baseModel.providerId,
      );

      // Tạo mô hình fine-tuning
      const adminFineTuningModel = await this.adminFineTuningModelRepository.createAdminFineTuningModel(dto, employeeId);
      if (!adminFineTuningModel) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.FINE_TUNING_FAILED, 'Tạo mô hình fine-tuning thất bại');
      }

      // Lấy thông tin chi tiết mô hình vừa tạo
      return await this.findById(adminFineTuningModel.id);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo mô hình fine-tuning: ${error.message}`, error.stack);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.FINE_TUNING_FAILED, 'Tạo mô hình fine-tuning thất bại');
    }
  }

  /**
   * Lấy danh sách mô hình fine-tuning với phân trang
   * @param query Tham số truy vấn và phân trang
   * @returns Danh sách mô hình fine-tuning với phân trang
   */
  async findAll(query: FineTuningModelQueryDto): Promise<PaginatedResult<FineTuningModelPaginatedRes>> {
    try {
      const result = await this.adminFineTuningModelRepository.findAllPaginated(query);

      // Chuyển đổi kết quả sang DTO
      const items = await Promise.all(result.items.map(async (item: any) => {
        const dto = plainToInstance(FineTuningModelPaginatedRes, {
          id: item.id,
          name: item.name,
          description: item.description,
          token: item.token,
          status: item.status,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
          baseModelName: item.baseModelName,
          isPrivate: item.isPrivate,
        });
        return dto;
      }));

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách mô hình fine-tuning: ${error.message}`, error.stack);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR, 'Lấy danh sách mô hình fine-tuning thất bại');
    }
  }

  /**
   * Lấy thông tin chi tiết mô hình fine-tuning theo ID
   * @param id ID của mô hình fine-tuning
   * @returns Thông tin chi tiết mô hình fine-tuning
   */
  async findById(id: string): Promise<AdminFineTuningModelDetailRes> {
    try {
      const adminFineTuningModel = await this.adminFineTuningModelRepository.findByIdWithDetails(id);
      if (!adminFineTuningModel) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.FINE_TUNING_NOT_FOUND, `Không tìm thấy mô hình fine-tuning với ID ${id}`);
      }

      // Chuyển đổi kết quả sang DTO
      const modelData = adminFineTuningModel as any;
      const dto = plainToInstance(AdminFineTuningModelDetailRes, {
        id: modelData.id,
        name: modelData.name,
        description: modelData.description,
        token: modelData.token,
        status: modelData.status,
        createdAt: modelData.createdAt,
        updatedAt: modelData.updatedAt,
        createdBy: modelData.createdBy,
        updatedBy: modelData.updatedBy,
        deletedBy: modelData.deletedBy,
        modelBaseId: modelData.modelBaseId,
        baseModelName: modelData.baseModelName,
        isPrivate: modelData.isPrivate,
      });

      // Lấy thông tin chi tiết về người tạo, người cập nhật
      if (modelData.createdBy) {
        const createdByInfo = await this.employeeInfoService.getEmployeeInfo(modelData.createdBy);
        if (createdByInfo) {
          dto.createdByInfo = {
            id: createdByInfo.employeeId,
            fullName: createdByInfo.name,
            avatar: createdByInfo.avatar ? this.cdnService.generateUrlView(createdByInfo.avatar, TimeIntervalEnum.FIVE_MINUTES) || undefined : undefined,
          };
        }
      }

      if (modelData.updatedBy) {
        const updatedByInfo = await this.employeeInfoService.getEmployeeInfo(modelData.updatedBy);
        if (updatedByInfo) {
          dto.updatedByInfo = {
            id: updatedByInfo.employeeId,
            fullName: updatedByInfo.name,
            avatar: updatedByInfo.avatar ? this.cdnService.generateUrlView(updatedByInfo.avatar, TimeIntervalEnum.FIVE_MINUTES) || undefined : undefined,
          };
        }
      }

      return dto;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy thông tin mô hình fine-tuning: ${error.message}`, error.stack);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR, 'Lấy thông tin mô hình fine-tuning thất bại');
    }
  }

  /**
   * Xóa mô hình fine-tuning
   * @param id ID của mô hình fine-tuning
   * @param employeeId ID của nhân viên xóa mô hình
   * @returns true nếu xóa thành công
   */
  async delete(id: string, employeeId: number): Promise<boolean> {
    try {
      const adminFineTuningModel = await this.adminFineTuningModelRepository.findById(id);
      if (!adminFineTuningModel) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.FINE_TUNING_NOT_FOUND, `Không tìm thấy mô hình fine-tuning với ID ${id}`);
      }

      const result = await this.adminFineTuningModelRepository.deleteAdminFineTuningModel(id, employeeId);
      if (!result) {
        throw new AppException(MODEL_TRAINING_ERROR_CODES.DELETE_FAILED, 'Xóa mô hình fine-tuning thất bại');
      }

      return true;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa mô hình fine-tuning: ${error.message}`, error.stack);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.DELETE_FAILED, 'Xóa mô hình fine-tuning thất bại');
    }
  }

  /**
   * Lấy danh sách tất cả mô hình fine-tuning (cả admin và user) với phân trang
   * @param query Tham số truy vấn và phân trang
   * @returns Danh sách mô hình fine-tuning với phân trang
   */
  async findAllPublic(query: FineTuningModelQueryDto): Promise<PaginatedResult<any>> {
    try {
      return await this.adminFineTuningModelRepository.findAllPublicPaginated(query);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách mô hình fine-tuning: ${error.message}`, error.stack);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR, 'Lấy danh sách mô hình fine-tuning thất bại');
    }
  }

  /**
   * Lấy danh sách tất cả mô hình fine-tuning của user với phân trang
   * @param query Tham số truy vấn và phân trang
   * @returns Danh sách mô hình fine-tuning của user với phân trang
   */
  async findAllUserModels(query: FineTuningModelQueryDto): Promise<PaginatedResult<any>> {
    try {
      return await this.adminFineTuningModelRepository.findAllUserModelsPaginated(query);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách mô hình fine-tuning của user: ${error.message}`, error.stack);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR, 'Lấy danh sách mô hình fine-tuning của user thất bại');
    }
  }
}