import { AppException } from '@common/exceptions';
import { Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { BaseModelStatusEnum } from '../constants/base-model-status.enum';
import { TypeProviderEnum, TypeProviderUtil } from '../constants/type-provider.enum';
import { BaseModelUserResponseDto } from '../dto/base-model/base-model-user-response.dto';
import { BaseModelRes } from "../dto/base-model/base-model.dto";
import { MODEL_TRAINING_ERROR_CODES } from '../exceptions';
import { ConfigModelBase } from '../interfaces/config-model-base.interface';
import { BaseModelRepository } from '../repositories/base-model.repository';

export class BaseModelUserMapper {
    private static readonly logger = new Logger(BaseModelUserMapper.name);

    /**
     * Xác định provider type dựa trên id bằng cách truy vấn cơ sở dữ liệu
     * @param id ID của model
     * @param baseModelRepository Repository để truy vấn thông tin model
     * @returns Provider type tương ứng với id
     * @throws AppException nếu không tìm thấy provider type
     */
    private static async getProviderTypeFromId(
        id: string,
        baseModelRepository: BaseModelRepository
    ): Promise<TypeProviderEnum> {
        try {
            // Truy vấn thông tin provider type từ cơ sở dữ liệu
            const providerType = await baseModelRepository.getProviderTypeById(id);

            if (providerType) {
                return TypeProviderUtil.getMimeType(providerType);
            }

            // Ném lỗi nếu không tìm thấy provider type
            throw new AppException(
                MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND,
                `Không tìm thấy provider type cho model ${id}`
            );
        } catch (error) {
            this.logger.error(`Lỗi khi lấy thông tin provider type từ cơ sở dữ liệu cho model ${id}: ${error.message}`);
            // Nếu lỗi đã là AppException thì ném lại
            if (error instanceof AppException) {
                throw error;
            }
            // Nếu là lỗi khác, ném lỗi provider not found
            throw new AppException(
                MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND,
                `Không tìm thấy provider type cho model ${id}`
            );
        }
    }

    /**
     * Chuyển đổi từ BaseModelRes sang BaseModelUserResponseDto
     * @param baseModel BaseModelRes
     * @param providerType Type của provider (nếu đã biết)
     * @returns BaseModelUserResponseDto
     */
    private static toUserResponseDto(
        baseModel: BaseModelRes,
        providerType?: TypeProviderEnum
    ): BaseModelUserResponseDto {
        const dto = new BaseModelUserResponseDto();
        dto.id = baseModel.id;
        dto.description = baseModel.description;

        // Thêm model_id từ baseModel.modelId nếu có
        dto.model_id = baseModel.modelId || baseModel.id;

        // Sử dụng config từ BaseModelRes nếu có, nếu không thì dùng đối tượng rỗng
        dto.config = baseModel.config || {} as ConfigModelBase;
        dto.createdAt = baseModel.createAt || Date.now(); // Sử dụng createAt nếu có, nếu không thì dùng thời gian hiện tại

        // Thêm status để các service khác có thể sử dụng
        dto.status = baseModel.status || BaseModelStatusEnum.APPROVED;

        // Sử dụng providerType nếu được cung cấp, nếu không thì sử dụng fallback
        dto.providerType = providerType;

        return dto;
    }

    /**
     * Chuyển đổi danh sách BaseModelRes sang danh sách BaseModelUserResponseDto
     * @param baseModelList Danh sách BaseModelRes
     * @param cdnService CdnService để tạo URL cho avatar
     * @returns Danh sách BaseModelUserResponseDto
     */
    static async toDtoList(
        baseModelList: BaseModelRes[],
        baseModelRepository?: BaseModelRepository,
    ): Promise<BaseModelUserResponseDto[]> {
        // Sử dụng Promise.all để chạy đồng thời các yêu cầu lấy URL
        const baseModelListWithUrl = await Promise.all(
            baseModelList.map(async (baseModel) => {
                // Tạo DTO mới
                let providerType: TypeProviderEnum | undefined;

                // Xác định providerType từ id nếu có baseModelRepository
                if (baseModelRepository) {
                    providerType = await this.getProviderTypeFromId(baseModel.id, baseModelRepository);
                }

                const dto = this.toUserResponseDto(baseModel, providerType);

                return dto;
            }),
        );

        return baseModelListWithUrl;
    }

    /**
     * Chuyển đổi một BaseModelRes sang BaseModelUserResponseDto
     * @param baseModel BaseModelRes
     * @param cdnService CdnService để tạo URL cho avatar
     * @returns BaseModelUserResponseDto
     */
    static async toDto(
        baseModel: BaseModelRes,
        cdnService: CdnService,
        baseModelRepository?: BaseModelRepository,
    ): Promise<BaseModelUserResponseDto> {
        // Tạo DTO mới
        let providerType: TypeProviderEnum | undefined;

        // Xác định providerType từ id nếu có baseModelRepository
        if (baseModelRepository) {
            providerType = await this.getProviderTypeFromId(baseModel.id, baseModelRepository);
        }

        const dto = this.toUserResponseDto(baseModel, providerType);

        return dto;
    }
}
