import { PaginatedResult } from '@/common/response';
import { FineTuningModel } from '@modules/model-training/entities';
import { Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { DataSource, Repository } from 'typeorm';
import { FineTuningModelQueryDto } from '../dto/fine-tuning-model/fine-tuning-model-query.dto';
import { FineTuningModelPaginatedRes } from '../dto/fine-tuning-model/fine-tuning-model.dto';

@Injectable()
export class FineTuningModelRepository extends Repository<FineTuningModel> {
  constructor(private dataSource: DataSource) {
    super(FineTuningModel, dataSource.createEntityManager());
  }

  async findAllForAdmin(
    query: FineTuningModelQueryDto,
  ): Promise<PaginatedResult<FineTuningModelPaginatedRes>> {
    const page = query.page ?? 1;
    const limit = query.limit ?? 10;

    const queryBuilder = this.createQueryBuilder('fineTuningModel')
      .select([
        'fineTuningModel.id',
        'fineTuningModel.name',
        'fineTuningModel.description',
        'fineTuningModel.token',
        'fineTuningModel.status',
        'fineTuningModel.ownerType',
        'fineTuningModel.ownerBy',
        'fineTuningModel.createdAt',
        'fineTuningModel.updatedAt',
      ])
      .innerJoin('admin_fine_tuning_models', 'adminModel', 'adminModel.id = fineTuningModel.id')
      .leftJoin('employees', 'employee', 'employee.id = adminModel.created_by')
      .addSelect(['employee.full_name AS user_full_name', 'employee.avatar AS user_avatar'])
      .where('1 = 1');

    // 🔍 Search theo name & description
    if (query.search) {
      queryBuilder.andWhere(
        '(fineTuningModel.name ILIKE :search OR fineTuningModel.description ILIKE :search)',
        { search: `%${query.search}%` },
      );
    }

    // Lọc theo quyền riêng tư
    if (query.isPrivate !== undefined) {
      queryBuilder.andWhere('adminModel.is_private = :isPrivate', { isPrivate: query.isPrivate });
    }

    // 📌 Sắp xếp
    queryBuilder.orderBy(`fineTuningModel.${query.sortBy}`, query.sortDirection);

    // 📄 Phân trang
    queryBuilder.skip((page - 1) * limit);
    queryBuilder.take(limit);

    const rawItems = await queryBuilder.getRawMany(); 
    const fineTuningModelEntities = await queryBuilder.getMany();
    const items = fineTuningModelEntities.map((fineTuningModel, index) => {
      const raw = rawItems[index];
      const dto = plainToInstance(FineTuningModelPaginatedRes, fineTuningModel);
      return dto;
    });

    const totalItems = await queryBuilder.getCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  async findAllForUser(
    query: FineTuningModelQueryDto,
    userId: number,
  ): Promise<PaginatedResult<FineTuningModel>> {
    const page = query.page ?? 1;
    const limit = query.limit ?? 10;

    const queryBuilder = this.createQueryBuilder('fineTuningModel')
      .innerJoin('user_fine_tuning_models', 'userModel', 'userModel.id = fineTuningModel.id')
      .where('userModel.user_id = :userId', { userId });

    // 🔍 Search theo name & description
    if (query.search) {
      queryBuilder.andWhere(
        '(fineTuningModel.name ILIKE :search OR fineTuningModel.description ILIKE :search)',
        { search: `%${query.search}%` },
      );
    }

    // Lọc theo nhà cung cấp
    if (query.providerId) {
      queryBuilder.andWhere('userModel.provider_id = :providerId', { providerId: query.providerId });
    }

    // 📌 Sắp xếp
    queryBuilder.orderBy(`fineTuningModel.${query.sortBy}`, query.sortDirection);

    // 📄 Phân trang
    queryBuilder.skip((page - 1) * limit);
    queryBuilder.take(limit);

    const [items, totalItems] = await queryBuilder.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }
}
