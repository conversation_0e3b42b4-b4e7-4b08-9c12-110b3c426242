import { ConfigService, ConfigType, SecretKeyModelConfig } from '@/config';
import { Injectable } from '@nestjs/common';
import * as crypto from 'crypto';

/**
 * Helper class để mã hóa và giải mã API key
 */
@Injectable()
export class ApiKeyEncryptionHelper {
  private readonly algorithm = 'aes-256-cbc';
  private adminSecretKey;
  private userSecretKey;

  constructor(private readonly configService: ConfigService) {
    const secretKeyModelConfig = this.configService.getConfig<SecretKeyModelConfig>(ConfigType.SecretKeyModel);

    this.adminSecretKey = secretKeyModelConfig.adminSecretKey;
    this.userSecretKey = secretKeyModelConfig.userSecretKey;
  } 

  /**
   * Mã hóa API key cho admin
   * @param apiKey API key cần mã hóa
   * @returns API key đã được mã hóa
   */
  encryptAdminApiKey(apiKey: string): string {
    if (!this.adminSecretKey) {
      throw new Error('ADMIN_SECRECT_MODEL không được cấu hình');
    }
    
    return this.encrypt(apiKey, this.adminSecretKey);
  }

  /**
   * Giải mã API key cho admin
   * @param encryptedApiKey API key đã mã hóa
   * @returns API key gốc
   */
  decryptAdminApiKey(encryptedApiKey: string): string {
    if (!this.adminSecretKey) {
      throw new Error('ADMIN_SECRECT_MODEL không được cấu hình');
    }
    
    return this.decrypt(encryptedApiKey, this.adminSecretKey);
  }

  /**
   * Mã hóa API key cho user
   * @param apiKey API key cần mã hóa
   * @param userId ID của user
   * @returns API key đã được mã hóa
   */
  encryptUserApiKey(apiKey: string, userId: number): string {
    if (!this.userSecretKey) {
      throw new Error('USER_SECRECT_MODEL không được cấu hình');
    }
    
    // Kết hợp secretKey với userId để tạo key mã hóa riêng cho mỗi user
    const userSpecificKey = `${this.userSecretKey}_${userId}`;
    return this.encrypt(apiKey, userSpecificKey);
  }

  /**
   * Giải mã API key cho user
   * @param encryptedApiKey API key đã mã hóa
   * @param userId ID của user
   * @returns API key gốc
   */
  decryptUserApiKey(encryptedApiKey: string, userId: number): string {
    if (!this.userSecretKey) {
      throw new Error('USER_SECRECT_MODEL không được cấu hình');
    }
    
    // Kết hợp secretKey với userId để tạo key mã hóa riêng cho mỗi user
    const userSpecificKey = `${this.userSecretKey}_${userId}`;
    return this.decrypt(encryptedApiKey, userSpecificKey);
  }

  /**
   * Hàm mã hóa chung
   * @param text Chuỗi cần mã hóa
   * @param secretKey Khóa bí mật
   * @returns Chuỗi đã mã hóa
   */
  private encrypt(text: string, secretKey: string): string {
    // Tạo key từ secretKey bằng cách hash với SHA-256
    const key = crypto.createHash('sha256').update(secretKey).digest('base64').substring(0, 32);
    
    // Tạo IV (Initialization Vector) ngẫu nhiên
    const iv = crypto.randomBytes(16);
    
    // Tạo cipher
    const cipher = crypto.createCipheriv(this.algorithm, key, iv);
    
    // Mã hóa
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // Kết hợp IV và chuỗi đã mã hóa (IV cần được lưu cùng để giải mã)
    return iv.toString('hex') + ':' + encrypted;
  }

  /**
   * Hàm giải mã chung
   * @param encryptedText Chuỗi đã mã hóa
   * @param secretKey Khóa bí mật
   * @returns Chuỗi gốc
   */
  private decrypt(encryptedText: string, secretKey: string): string {
    // Tạo key từ secretKey bằng cách hash với SHA-256
    const key = crypto.createHash('sha256').update(secretKey).digest('base64').substring(0, 32);
    
    // Tách IV và chuỗi đã mã hóa
    const textParts = encryptedText.split(':');
    const iv = Buffer.from(textParts[0], 'hex');
    const encryptedData = textParts[1];
    
    // Tạo decipher
    const decipher = crypto.createDecipheriv(this.algorithm, key, iv);
    
    // Giải mã
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}
