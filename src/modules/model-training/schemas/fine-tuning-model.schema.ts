import { ApiProperty } from '@nestjs/swagger';
import { FineTuningModel } from '../entities/fine-tuning-model.entity';
import { BaseModelSchema } from './base-model.schema';
import { DataFineTuningModelSchema } from './data-fine-tuning-model.schema';

export class FineTuningModelSchema {
  @ApiProperty({
    description: 'ID định danh duy nhất cho mô hình fine-tuning',
    example: 'ft:gpt-4-turbo:org-123:custom-model-name:789',
  })
  id: string;

  @ApiProperty({
    description: 'Tên hiển thị của mô hình fine-tuning',
    example: 'Mô hình hỗ trợ tư vấn tài chính',
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả chi tiết về mô hình và mục đích sử dụng',
    example: '<PERSON><PERSON> hình được huấn luyện để tư vấn tài chính cá nhân với kiến thức chuyên sâu về thị trường Việt Nam',
    nullable: true,
  })
  description: string;

  @ApiProperty({
    description: 'Số lượng token đã sử dụng trong quá trình fine-tuning',
    example: 50000,
  })
  token: number;

  @ApiProperty({
    description: 'Trạng thái hiện tại của mô hình',
    example: 'completed',
    enum: ['pending', 'training', 'completed', 'failed'],
    nullable: true,
  })
  status: string;

  @ApiProperty({
    description: 'ID của chi tiết kỹ thuật của mô hình fine-tuning',
    example: '123e4567-e89b-12d3-a456-426614174000',
    nullable: true,
  })
  detailId: string;

  @ApiProperty({
    description: 'Chi tiết kỹ thuật của mô hình fine-tuning',
    type: DataFineTuningModelSchema,
    nullable: true,
  })
  detail: DataFineTuningModelSchema;

  @ApiProperty({
    description: 'ID của mô hình nền tảng được sử dụng để fine-tuning',
    example: 'gpt-4-turbo',
    nullable: true,
  })
  baseModelId: string;

  @ApiProperty({
    description: 'Thông tin về mô hình nền tảng được sử dụng để fine-tuning',
    type: BaseModelSchema,
    nullable: true,
  })
  baseModel: BaseModelSchema;

  @ApiProperty({
    description: 'Loại người sở hữu mô hình',
    example: 'user',
    enum: ['user', 'organization', 'system'],
  })
  ownerType: string;

  @ApiProperty({
    description: 'ID của người sở hữu mô hình',
    example: 1,
  })
  ownerBy: number;

  @ApiProperty({
    description: 'Thời điểm tạo mô hình (timestamp millis)',
    example: 1625097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật mô hình gần nhất (timestamp millis)',
    example: 1625097600000,
  })
  updatedAt: number;

  constructor(partial: Partial<FineTuningModel>) {
    Object.assign(this, partial);
  }
}

export class FineTuningModelListResponseSchema {
  @ApiProperty({
    description: 'Danh sách mô hình fine-tuning',
    type: [FineTuningModelSchema],
  })
  items: FineTuningModelSchema[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    type: 'object',
    properties: {
      totalItems: {
        type: 'number',
        example: 100,
        description: 'Tổng số mô hình',
      },
      itemCount: {
        type: 'number',
        example: 10,
        description: 'Số mô hình trên trang hiện tại',
      },
      itemsPerPage: {
        type: 'number',
        example: 10,
        description: 'Số mô hình trên mỗi trang',
      },
      totalPages: {
        type: 'number',
        example: 10,
        description: 'Tổng số trang',
      },
      currentPage: {
        type: 'number',
        example: 1,
        description: 'Trang hiện tại',
      },
    },
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
