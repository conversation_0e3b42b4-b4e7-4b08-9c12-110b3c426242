import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng fine_tuning_models trong cơ sở dữ liệu
 * Bảng quản lý các mô hình đã được fine-tuning từ các mô hình nền tảng
 */
@Entity('fine_tuning_models')
export class FineTuningModel {
  /**
   * ID định danh duy nhất cho mô hình fine-tuning
   */
  @PrimaryColumn({ name: 'id', length: 100 })
  id: string;

  /**
   * Tên hiển thị của mô hình fine-tuning
   */
  @Column({ name: 'name', length: 64 })
  name: string;

  /**
   * Mô tả chi tiết về mô hình và mục đích sử dụng
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;

  /**
   * Số lượng token đã sử dụng trong quá trình fine-tuning
   */
  @Column({ name: 'token', default: 0 })
  token: number;

  /**
   * Trạng thái hiện tại của mô hình (ví dụ: pending, training, completed, failed)
   */
  @Column({ name: 'status', length: 50, nullable: true })
  status: string;

  /**
   * Cờ đánh dấu mô hình có phải là mô hình được tạo bởi người dùng hay không
   */
  @Column({ name: 'is_manual', type: 'boolean', default: true })
  isManual: boolean;

  /**
   * Tham chiếu đến bảng chi tiết kỹ thuật của mô hình fine-tuning
   */
  @Column({ name: 'detail_id', nullable: true })
  detailId: string;

  /**
   * Thời điểm tạo mô hình (timestamp millis)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint"
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật mô hình gần nhất (timestamp millis)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint"
  })
  updatedAt: number;

  /**
   * Thời điểm xóa mềm (nếu có)
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;
}
