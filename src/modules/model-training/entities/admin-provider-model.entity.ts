import {
    Column,
    Entity,
    PrimaryGeneratedColumn
} from 'typeorm';
import { TypeProviderEnum } from '../constants/type-provider.enum';

/**
 * Entity đại diện cho bảng providers_model trong cơ sở dữ liệu
 * Quản lý thông tin nhà cung cấp dịch vụ
 */
@Entity({ name: 'admin_provider_models' })
export class AdminProviderModel {
    /**
     * ID tự động tăng
     */
   @PrimaryGeneratedColumn('uuid')
    id: string;

    /**
     * Tên nhà cung cấp
     */
    @Column({ type: 'varchar', length: 255, comment: 'Tên nhà cung cấp' })
    name: string;

    /**
     * Người tạo
     */
    @Column({ name: 'created_by', type: 'integer', nullable: true, comment: 'Người tạo' })
    createdBy: number;

    /**
     * Người cập nhật
     */
    @Column({ name: 'updated_by', type: 'integer', nullable: true })
    updatedBy: number;

    /**
     * Thời điểm tạo
     */
    @Column({
        name: 'created_at',
        type: 'bigint',
        default: () => `(EXTRACT(EPOCH FROM now()) * 1000)::bigint`,
    })
    createdAt: number;

    /**
     * Thời điểm cập nhật
     */
    @Column({
        name: 'updated_at',
        type: 'bigint',
        default: () => `(EXTRACT(EPOCH FROM now()) * 1000)::bigint`,
    })
    updatedAt: number;

    /**
     * Loại nhà cung cấp
     */
    @Column({
        name: 'type',
        type: 'enum',
        enum: TypeProviderEnum,
        default: TypeProviderEnum.OPENAI,
        comment: 'Loại nhà cung cấp'
    })
    type: TypeProviderEnum;

    /**
     * API key của nhà cung cấp
     */
    @Column({ name: 'api-key', type: 'text' })
    apiKey: string;

    /**
     * Người xóa
     */
    @Column({ name: 'deleted_by', type: 'integer', nullable: true })
    deletedBy: number;

    /**
     * Thời điểm xóa
     */
    @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
    deletedAt: number;
}
