import {
    Column,
    Entity,
    PrimaryGeneratedColumn
} from 'typeorm';
import { TypeProviderEnum } from '../constants/type-provider.enum';

/**
 * Entity đại diện cho bảng user_provider_models trong cơ sở dữ liệu
 * Quản lý danh sách mô hình nhà cung cấp API mà người dùng có quyền sử dụng
 */
@Entity({ name: 'user_provider_models' })
export class UserProviderModel {
    /**
     * ID định danh duy nhất (UUID) cho user_provider_model
     */
    @PrimaryGeneratedColumn('uuid')
    id: string;

    /**
     * ID người dùng liên kết (tham chiếu đến bảng users)
     */
    @Column({ name: 'user_id', type: 'integer' })
    userId: number;

    /**
     * Tên định danh tùy chọn cho mô hình API của người dùng
     */
    @Column({ type: 'varchar', length: 255, comment: 'Tên định danh tùy chọn cho mô hình API của người dùng' })
    name: string;

    /**
     * Loại nhà cung cấp mô hình, ví dụ: OPENAI, AZURE,...
     */
    @Column({
        name: 'type',
        type: 'enum',
        enum: TypeProviderEnum,
        default: TypeProviderEnum.OPENAI,
        comment: 'Loại nhà cung cấp mô hình, ví dụ: OPENAI, AZURE,...'
    })
    type: TypeProviderEnum;

    /**
     * Khóa API do người dùng cung cấp để kết nối với nhà cung cấp
     */
    @Column({ name: 'api-key', type: 'text', comment: 'Khóa API do người dùng cung cấp để kết nối với nhà cung cấp' })
    apiKey: string;

    /**
     * Thời điểm tạo (epoch time, tính theo millisecond)
     */
    @Column({
        name: 'created_at',
        type: 'bigint',
        default: () => `(EXTRACT(EPOCH FROM now()) * 1000)::bigint`,
        comment: 'Thời điểm tạo (epoch time, tính theo millisecond)'
    })
    createdAt: number;

    /**
     * Thời điểm cập nhật gần nhất (epoch time, tính theo millisecond)
     */
    @Column({
        name: 'updated_at',
        type: 'bigint',
        default: () => `(EXTRACT(EPOCH FROM now()) * 1000)::bigint`,
        comment: 'Thời điểm cập nhật gần nhất (epoch time, tính theo millisecond)'
    })
    updatedAt: number;

    /**
     * Thời điểm xóa mềm (epoch time, nếu có)
     */
    @Column({
        name: 'deleted_at',
        type: 'bigint',
        nullable: true,
        comment: 'Thời điểm xóa mềm (epoch time, nếu có)'
    })
    deletedAt: number;
}
