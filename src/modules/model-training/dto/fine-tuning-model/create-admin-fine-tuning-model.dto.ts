import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';

/**
 * DTO cho việc tạo mô hình fine-tuning bởi admin
 */
export class CreateAdminFineTuningModelDto {
  /**
   * ID định danh duy nhất cho mô hình fine-tuning
   * Phải tồn tại trong nhà cung cấp tương ứng với model base
   */
  @ApiProperty({
    description: 'ID định danh duy nhất cho mô hình fine-tuning',
    example: 'ft:gpt-3.5-turbo:openai:custom-model:7p89qrs',
  })
  @IsNotEmpty()
  @IsString()
  id: string;

  /**
   * Tên hiển thị của mô hình fine-tuning
   * Nếu không cung cấp, sẽ sử dụng ID làm tên
   */
  @ApiPropertyOptional({
    description: 'Tên hiển thị của mô hình fine-tuning',
    example: '<PERSON><PERSON> hình hỗ trợ khách hàng',
  })
  @IsOptional()
  @IsString()
  name?: string;

  /**
   * Mô tả chi tiết về mô hình và mục đích sử dụng
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về mô hình và mục đích sử dụng',
    example: 'Mô hình được huấn luyện để trả lời các câu hỏi về sản phẩm và dịch vụ',
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * ID của mô hình gốc được sử dụng để fine-tune
   * Phải tồn tại trong bảng base_models
   */
  @ApiProperty({
    description: 'ID của mô hình gốc được sử dụng để fine-tune',
    example: 'gpt-3.5-turbo',
  })
  @IsNotEmpty()
  @IsString()
  modelBaseId: string;

  /**
   * Cờ đánh dấu mô hình có ở chế độ riêng tư hay không
   * Mặc định là true (riêng tư)
   */
  @ApiPropertyOptional({
    description: 'Cờ đánh dấu mô hình có ở chế độ riêng tư hay không',
    example: false,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isPrivate?: boolean;
}
