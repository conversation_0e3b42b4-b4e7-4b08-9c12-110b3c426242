import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { TransactionStatus } from '@modules/r-point/enums';

/**
 * Entity đại diện cho bảng point_purchase_transactions trong cơ sở dữ liệu
 * Bảng lưu trữ thông tin về các giao dịch mua point
 */
@Entity('point_purchase_transactions')
export class PointPurchaseTransaction {
  /**
   * ID tự tăng của giao dịch
   */
  @PrimaryGeneratedColumn('increment', { name: 'id' })
  id: number;

  /**
   * ID của người dùng thực hiện giao dịch
   */
  @Column({ name: 'user_id', nullable: true, comment: 'Định danh người dùng' })
  userId: number;

  // Không sử dụng quan hệ với bảng users, chỉ lưu ID

  /**
   * Số tiền giao dịch
   */
  @Column({ name: 'amount', type: 'double precision', nullable: false, comment: 'Số tiền' })
  amount: number;

  /**
   * <PERSON><PERSON> lượng point mua
   */
  @Column({ name: 'points_amount', type: 'integer', nullable: false, comment: 'Số lượng points mua' })
  pointsAmount: number;

  /**
   * Tên loại point (nếu có)
   */
  @Column({ name: 'point_name', length: 255, nullable: true, comment: 'Tên loại point (nếu có)' })
  pointName: string;

  /**
   * ID loại point (nếu có)
   */
  @Column({ name: 'point_id', nullable: true, comment: 'ID loại point (nếu có)' })
  pointId: number;

  // Không sử dụng quan hệ với bảng points, chỉ lưu ID

  /**
   * Loại tiền tệ
   */
  @Column({ name: 'currency', length: 10, default: 'VND', comment: 'Loại tiền tệ' })
  currency: string;

  /**
   * Trạng thái giao dịch
   */
  @Column({ name: 'status', type: 'enum', enum: TransactionStatus, default: TransactionStatus.PENDING, comment: 'Trạng thái giao dịch (PENDING, CONFIRMED, FAILED, REFUNDED)' })
  status: TransactionStatus;

  /**
   * Phương thức thanh toán
   */
  @Column({ name: 'payment_method', length: 50, nullable: false, comment: 'Phương thức thanh toán' })
  paymentMethod: string;

  /**
   * Mã tham chiếu từ cổng thanh toán
   */
  @Column({ name: 'reference_id', length: 100, nullable: true, comment: 'Mã tham chiếu từ cổng thanh toán' })
  referenceId: string;

  /**
   * Mô tả giao dịch
   */
  @Column({ name: 'description', type: 'text', nullable: true, comment: 'Mô tả giao dịch' })
  description: string;

  /**
   * Số lần thử lại thanh toán
   */
  @Column({ name: 'retry_count', default: 0, comment: 'Số lần thử lại thanh toán' })
  retryCount: number;

  /**
   * Thời gian thử lại cuối cùng
   */
  @Column({ name: 'last_retry_at', type: 'bigint', nullable: true, comment: 'Thời gian thử lại cuối cùng (Unix timestamp)' })
  lastRetryAt: number;

  /**
   * Thời gian tạo giao dịch
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false, comment: 'Thời gian tạo giao dịch (Unix timestamp)' })
  createdAt: number;

  /**
   * Thời gian cập nhật giao dịch
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: false, comment: 'Thời gian cập nhật giao dịch (Unix timestamp)' })
  updatedAt: number;

  /**
   * Thời gian hoàn thành giao dịch
   */
  @Column({ name: 'completed_at', type: 'bigint', nullable: true, comment: 'Thời gian hoàn thành giao dịch (Unix timestamp)' })
  completedAt: number;

  /**
   * Số dư trước giao dịch
   */
  @Column({ name: 'balance_before', type: 'integer', nullable: false, comment: 'Số dư trước giao dịch' })
  balanceBefore: number;

  /**
   * Số dư sau giao dịch
   */
  @Column({ name: 'balance_after', type: 'integer', nullable: false, comment: 'Số dư sau giao dịch' })
  balanceAfter: number;

  /**
   * ID của coupon sử dụng (nếu có)
   */
  @Column({ name: 'coupon_id', nullable: true })
  couponId: number;

  /**
   * Số tiền giảm giá từ coupon
   */
  @Column({ name: 'coupon_amount', type: 'double precision', nullable: true, comment: 'Tiền khuyến mãi, giảm giá' })
  couponAmount: number;

  // Không sử dụng quan hệ với bảng invoice, chỉ lưu ID
}
