import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng points trong cơ sở dữ liệu
 * Bảng lưu trữ thông tin về các gói point trong hệ thống
 */
@Entity('points')
export class Point {
  /**
   * ID tự tăng của gói point
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Tên của gói point
   * Ví dụ: "Gói 100k", "Gói 500k"
   */
  @Column({ name: 'name', length: 255, nullable: true, comment: 'tên của gói rpoint' })
  name: string;

  /**
   * Số tiền của gói point (chỉ áp dụng cho gói không phải customize)
   * Đơn vị: VND
   */
  @Column({ name: 'cash', type: 'double precision', nullable: false, comment: 'Số tiền của gói point không phải customize' })
  cash: number;

  /**
   * Tỷ lệ quy đổi giữa tiền và point
   * Ví dụ: rate = 1000 nghĩa là 1000 VND = 1 point
   */
  @Column({ name: 'rate', type: 'double precision', nullable: true, comment: 'Tỷ lệ rate' })
  rate: number;

  /**
   * Số tiền tối thiểu cho gói customize
   * Chỉ áp dụng khi isCustomize = true
   */
  @Column({ name: 'min', type: 'double precision', nullable: true, comment: 'Nếu là customize thì có quy định min' })
  min: number;

  /**
   * Số tiền tối đa cho gói customize
   * Chỉ áp dụng khi isCustomize = true
   */
  @Column({ name: 'max', type: 'double precision', nullable: true, comment: 'Nếu là customize thì có quy định số tiền max' })
  max: number;

  /**
   * Số point tương ứng với gói
   * Được tính dựa trên cash và rate
   */
  @Column({ name: 'point', type: 'bigint', nullable: false, comment: 'Số point' })
  point: number;

  /**
   * Flag xác định đây có phải là gói customize hay không
   * true: Gói customize - người dùng có thể chọn số tiền trong khoảng min-max
   * false: Gói cố định - người dùng chỉ có thể mua với số tiền cố định
   */
  @Column({ name: 'is_customize', type: 'boolean', default: false, nullable: false, comment: 'Có phải gói customize không' })
  isCustomize: boolean;

  /**
   * Mô tả chi tiết về gói point
   * Có thể chứa thông tin về ưu đãi, điều kiện sử dụng,...
   */
  @Column({ name: 'description', length: 255, nullable: true, comment: 'Mô tả' })
  description: string;
}
