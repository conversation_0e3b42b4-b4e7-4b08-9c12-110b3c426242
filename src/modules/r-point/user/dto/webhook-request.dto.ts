import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

/**
 * DTO cho webhook request từ ngân hàng
 * Dựa trên WebhooksRequest từ Java
 */
export class WebhookRequestDto {
  @ApiProperty({
    description: 'ID giao dịch trên Se<PERSON>ay',
    example: 12345
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Brand name của ngân hàng',
    example: 'VIETCOMBANK'
  })
  @IsString()
  @IsNotEmpty()
  gateway: string;

  @ApiProperty({
    description: 'Thời gian xảy ra giao dịch phía ngân hàng',
    example: '2023-08-15T10:15:00Z'
  })
  @IsString()
  @IsNotEmpty()
  transactionDate: string;

  @ApiProperty({
    description: 'Số tài khoản ngân hàng',
    example: '**********'
  })
  @IsString()
  @IsNotEmpty()
  accountNumber: string;

  @ApiProperty({
    description: 'Mã code thanh toán (có thể null)',
    example: 'PAY123456',
    required: false
  })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiProperty({
    description: 'Nội dung chuyển khoản',
    example: 'RPOINT123456'
  })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({
    description: 'Loại giao dịch: in (tiền vào), out (tiền ra)',
    example: 'in',
    enum: ['in', 'out']
  })
  @IsString()
  @IsNotEmpty()
  transferType: string;

  @ApiProperty({
    description: 'Số tiền giao dịch',
    example: 100000
  })
  @IsNumber()
  transferAmount: number;

  @ApiProperty({
    description: 'Số dư tài khoản (lũy kế)',
    example: 5000000
  })
  @IsNumber()
  accumulated: number;

  @ApiProperty({
    description: 'Tài khoản ngân hàng phụ (có thể null)',
    example: '**********',
    required: false
  })
  @IsOptional()
  @IsString()
  subAccount?: string;

  @ApiProperty({
    description: 'Mã tham chiếu của tin nhắn SMS',
    example: 'REF123456'
  })
  @IsString()
  @IsNotEmpty()
  referenceCode: string;

  @ApiProperty({
    description: 'Toàn bộ nội dung tin nhắn SMS',
    example: 'Quý khách đã nhận 100,000 VND từ tài khoản ********** với nội dung RPOINT123456'
  })
  @IsString()
  @IsNotEmpty()
  description: string;
}

/**
 * DTO cho phản hồi webhook
 */
export class WebhookResponseDto {
  @ApiProperty({
    description: 'Trạng thái xử lý webhook',
    example: true
  })
  success: boolean;

  @ApiProperty({
    description: 'Thông báo',
    example: 'Webhook processed successfully'
  })
  message: string;
}
