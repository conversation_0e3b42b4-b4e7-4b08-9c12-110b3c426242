import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsPositive, IsString } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho request áp dụng mã giảm giá cho gói point
 */
export class ApplyCouponRequestDto {
  @ApiProperty({
    description: 'ID của gói point',
    example: 1,
    required: true
  })
  @IsNotEmpty({ message: 'ID gói point không được để trống' })
  @IsNumber({}, { message: 'ID gói point phải là số' })
  @Type(() => Number)
  pointPackageId: number;

  @ApiProperty({
    description: 'Số lượng point muốn mua',
    example: 100,
    required: true
  })
  @IsNotEmpty({ message: 'Số lượng point không được để trống' })
  @IsNumber({}, { message: 'Số lượng point phải là số' })
  @IsPositive({ message: 'Số lượng point phải là số dương' })
  @Type(() => Number)
  pointAmount: number;

  @ApiProperty({
    description: 'Mã coupon cần áp dụng',
    example: 'SUMMER2023',
    required: true
  })
  @IsNotEmpty({ message: 'Mã coupon không được để trống' })
  @IsString({ message: 'Mã coupon phải là chuỗi' })
  couponCode: string;
}
