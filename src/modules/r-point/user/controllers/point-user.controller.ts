import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiQuery, ApiResponse, ApiTags, getSchemaPath } from '@nestjs/swagger';
import { PointUserService } from '../services/point-user.service';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { PointResponseDto, PurchaseHistoryQueryDto, PurchaseHistoryResponseDto } from '../dto';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { JwtUserGuard } from '@modules/auth/guards';
import { TransactionStatus } from '@modules/r-point/enums';

@ApiTags('R-Point - User Points')
@ApiBearerAuth("JWT-auth")
@UseGuards(JwtUserGuard)
@Controller('user/r-point/points')
export class PointUserController {
  constructor(private readonly pointUserService: PointUserService) {}

  /**
   * L<PERSON>y danh sách tất cả các gói point
   * @returns Danh sách các gói point
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tất cả các gói point' })
  @ApiResponse({ status: 200, description: 'Danh sách các gói point', type: [PointResponseDto] })
  async getAllPoints(): Promise<ApiResponseDto<PointResponseDto[]>> {
    const points = await this.pointUserService.getAllPoints();
    return ApiResponseDto.success(points, 'Lấy danh sách gói point thành công');
  }

  /**
   * Lấy lịch sử mua point của người dùng hiện tại với phân trang
   * @param user Thông tin người dùng hiện tại
   * @param queryDto Tham số truy vấn và phân trang
   * @returns Lịch sử mua point và thông tin phân trang
   */
  @Get('purchase-history')
  @ApiOperation({ summary: 'Lấy lịch sử mua point của người dùng' })
  @ApiQuery({ name: 'page', required: false, description: 'Số trang (bắt đầu từ 1)', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Số lượng item trên mỗi trang', type: Number })
  @ApiQuery({ name: 'status', required: false, description: 'Trạng thái giao dịch', enum: TransactionStatus })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sắp xếp theo trường', type: String })
  @ApiQuery({ name: 'sortDirection', required: false, description: 'Hướng sắp xếp (ASC/DESC)', type: String })
  @ApiResponse({
    status: 200,
    description: 'Lịch sử mua point với phân trang',
    schema: {
      properties: {
        code: { type: 'number', example: 200 },
        message: { type: 'string', example: 'Lấy lịch sử mua point thành công' },
        result: {
          type: 'object',
          properties: {
            items: {
              type: 'array',
              items: { $ref: getSchemaPath(PurchaseHistoryResponseDto) }
            },
            meta: {
              type: 'object',
              properties: {
                totalItems: { type: 'number', example: 100 },
                itemCount: { type: 'number', example: 10 },
                itemsPerPage: { type: 'number', example: 10 },
                totalPages: { type: 'number', example: 10 },
                currentPage: { type: 'number', example: 1 }
              }
            }
          }
        }
      }
    }
  })
  async getPurchaseHistory(
    @CurrentUser() user: JwtPayload,
    @Query() queryDto: PurchaseHistoryQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<PurchaseHistoryResponseDto>>> {
    const { items, total, page, limit } = await this.pointUserService.getPurchaseHistory(user.id, queryDto);

    // Tính toán thông tin phân trang
    const totalPages = Math.ceil(total / limit);

    // Tạo đối tượng PaginatedResult
    const paginatedResult: PaginatedResult<PurchaseHistoryResponseDto> = {
      items: items as unknown as PurchaseHistoryResponseDto[],
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: totalPages,
        currentPage: page
      }
    };

    return ApiResponseDto.success(paginatedResult, 'Lấy lịch sử mua point thành công');
  }
}
