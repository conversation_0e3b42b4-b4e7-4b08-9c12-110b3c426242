import { Test, TestingModule } from '@nestjs/testing';
import { PointUserController } from '../controllers/point-user.controller';
import { PointUserService } from '../services';
import { TransactionStatus } from '@modules/r-point/enums';

describe('PointUserController', () => {
  let controller: PointUserController;
  let service: jest.Mocked<PointUserService>;

  const mockPointUserService = {
    getAllPoints: jest.fn(),
    getPurchaseHistory: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PointUserController],
      providers: [
        { provide: PointUserService, useValue: mockPointUserService },
      ],
    }).compile();

    controller = module.get<PointUserController>(PointUserController);
    service = module.get(PointUserService) as jest.Mocked<PointUserService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getAllPoints', () => {
    it('should return all points', async () => {
      // Mock data
      const mockPoints = [
        {
          id: 1,
          name: 'Point 1',
          cash: 100000,
          rate: 1000,
          point: 100,
          isCustomize: false,
          description: 'Regular point package',
        },
        {
          id: 2,
          name: 'Point 2',
          cash: 200000,
          rate: 1000,
          point: 200,
          isCustomize: false,
          description: 'Regular point package',
        },
        {
          id: 3,
          name: 'Customize Point',
          cash: 0,
          rate: 1000,
          min: 20000,
          max: 500000,
          isCustomize: true,
          description: 'Customize point package',
        },
      ];

      // Mock service response
      service.getAllPoints.mockResolvedValue(mockPoints);

      // Call the controller method
      const result = await controller.getAllPoints();

      // Assertions
      expect(service.getAllPoints).toHaveBeenCalled();
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockPoints);
    });
  });

  describe('getPurchaseHistory', () => {
    it('should return purchase history', async () => {
      // Mock data
      const mockTransactions = [
        {
          id: 1,
          userId: 1,
          pointId: 1,
          amount: 100000,
          pointsAmount: 100,
          status: TransactionStatus.CONFIRMED,
          createdAt: 1625097600000,
          point: {
            id: 1,
            name: 'Point 1',
            cash: 100000,
            rate: 1000,
          },
        },
        {
          id: 2,
          userId: 1,
          pointId: 2,
          amount: 200000,
          pointsAmount: 200,
          status: TransactionStatus.CONFIRMED,
          createdAt: 1625097700000,
          point: {
            id: 2,
            name: 'Point 2',
            cash: 200000,
            rate: 1000,
          },
        },
      ];

      // Mock service response
      service.getPurchaseHistory.mockResolvedValue(mockTransactions);

      // Mock user from JWT
      const mockUser = { id: 1 };

      // Call the controller method
      const result = await controller.getPurchaseHistory(mockUser as any);

      // Assertions
      expect(service.getPurchaseHistory).toHaveBeenCalledWith(1);
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockTransactions);
    });
  });
});
