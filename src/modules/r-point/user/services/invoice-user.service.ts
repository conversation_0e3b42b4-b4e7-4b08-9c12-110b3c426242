import { Injectable } from '@nestjs/common';
import { InvoiceRepository } from '@modules/r-point/repositories';
import { PointPurchaseTransactionRepository } from '@modules/r-point/repositories';
import { AppException, ErrorCode } from '@common/exceptions';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils';

/**
 * Service xử lý logic liên quan đến hóa đơn cho người dùng
 */
@Injectable()
export class InvoiceUserService {
  constructor(
    private readonly invoiceRepository: InvoiceRepository,
    private readonly transactionRepository: PointPurchaseTransactionRepository,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Lấy URL tải xuống hóa đơn
   * @param userId ID của người dùng
   * @param invoiceId ID của hóa đơn
   * @returns URL tải xuống hóa đơn
   */
  async getInvoiceDownloadUrl(userId: number, invoiceId: number): Promise<{ downloadUrl: string }> {
    // Kiểm tra xem hóa đơn có tồn tại không
    const invoice = await this.invoiceRepository.findOne({
      where: { id: invoiceId },
    });

    if (!invoice) {
      throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, `Không tìm thấy hóa đơn với ID ${invoiceId}`);
    }

    // Kiểm tra xem giao dịch có thuộc về người dùng không
    const transaction = await this.transactionRepository.findOne({
      where: { id: invoice.orderId },
    });

    if (!transaction || transaction.userId !== userId) {
      throw new AppException(ErrorCode.UNAUTHORIZED_ACCESS, 'Bạn không có quyền truy cập hóa đơn này');
    }

    // Kiểm tra xem hóa đơn có đường dẫn PDF không
    if (!invoice.invoicePathPdf) {
      throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Hóa đơn chưa được tạo hoặc không có file PDF');
    }

    // Tạo URL tải xuống với thời hạn 1 giờ
    const downloadUrl = this.cdnService.generateUrlView(invoice.invoicePathPdf, TimeIntervalEnum.ONE_HOUR);

    if (!downloadUrl) {
      throw new AppException(ErrorCode.CDN_URL_GENERATION_ERROR, 'Không thể tạo URL tải xuống hóa đơn');
    }

    return { downloadUrl };
  }
}
