import { Injectable } from '@nestjs/common';

/**
 * Service cung cấp thông tin về ngân hàng
 */
@Injectable()
export class BankInfoService {
  // Danh sách mã ngân hàng và tên tương ứng
  private readonly bankMap: Record<string, string> = {
    VCB: 'Vietcombank',
    TCB: 'Techcombank',
    VIB: 'VIB Bank',
    VPB: 'VPBank',
    MBB: 'MB Bank',
    ACB: 'ACB Bank',
    TPB: 'TPBank',
    OCB: 'OCB Bank',
    STB: 'Sacombank',
    HDB: 'HDBank',
    VTB: 'VietinBank',
    BIDV: 'BIDV',
    AGRIBANK: 'Agribank',
    SHB: 'SHB Bank',
    SCB: 'SCB Bank',
    ABBANK: 'ABBank',
    VIETABANK: 'VietABank',
    VIETCAPITALBANK: 'Viet Capital Bank',
    PGBANK: 'PGBank',
    OCEANBANK: 'OceanBank',
    NAMABANK: 'Nam A Bank',
    SEABANK: 'SeABank',
    BAOVIETBANK: 'BaoViet Bank',
    GPBANK: 'GPBank',
    KIENLONGBANK: 'Kienlongbank',
    DONGABANK: 'DongA Bank',
    EXIMBANK: 'Eximbank',
    LPBANK: 'LienVietPostBank',
    MSBANK: 'Maritime Bank',
    NCBANK: 'National Citizen Bank',
    PVCOMBANK: 'PVcomBank',
    SAIGONBANK: 'SaigonBank',
    VIETBANK: 'VietBank',
    VRBANK: 'VRB',
    WOORIBANK: 'Woori Bank',
    COOPBANK: 'Co-opBank',
    IBKBANK: 'IBK Bank',
    SHINHANBANK: 'Shinhan Bank',
    UOB: 'UOB Bank',
    CIMB: 'CIMB Bank',
    HSBC: 'HSBC Bank',
    STANDARDCHARTERED: 'Standard Chartered Bank',
    PUBLICBANK: 'Public Bank',
    INDOVINABANK: 'Indovina Bank',
  };

  /**
   * Lấy tên ngân hàng từ mã ngân hàng
   * @param bankCode Mã ngân hàng
   * @returns Tên ngân hàng hoặc mã ngân hàng nếu không tìm thấy
   */
  getBankName(bankCode: string): string {
    return this.bankMap[bankCode] || bankCode;
  }
}
