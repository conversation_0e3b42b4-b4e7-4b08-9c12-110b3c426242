import { Injectable, NotFoundException } from '@nestjs/common';
import { PointRepository, PointPurchaseTransactionRepository } from '@modules/r-point/repositories';
import { Point } from '@modules/r-point/entities';
import { PurchaseHistoryQueryDto } from '../dto';

/**
 * Service xử lý logic liên quan đến point cho người dùng
 */
@Injectable()
export class PointUserService {
  constructor(
    private readonly pointRepository: PointRepository,
    private readonly pointPurchaseTransactionRepository: PointPurchaseTransactionRepository
  ) {}

  /**
   * L<PERSON>y danh sách tất cả các gói point
   * @returns Danh sách các gói point
   */
  async getAllPoints(): Promise<Point[]> {
    return this.pointRepository.find({
      order: {
        isCustomize: 'ASC',
        cash: 'ASC'
      }
    });
  }

  /**
   * L<PERSON>y thông tin gói point theo ID
   * @param id ID của gói point
   * @returns Thông tin gói point hoặc null nếu không tìm thấy
   */
  async getPointById(id: number): Promise<Point | null> {
    return this.pointRepository.findOne({ where: { id } });
  }

  /**
   * Lấy lịch sử mua point của người dùng với phân trang
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn và phân trang
   * @returns Danh sách các giao dịch mua point và thông tin phân trang
   */
  async getPurchaseHistory(
    userId: number,
    queryDto: PurchaseHistoryQueryDto
  ): Promise<{ items: any[]; total: number; page: number; limit: number }> {
    if (!userId) {
      throw new NotFoundException('Không tìm thấy người dùng');
    }

    const {
      page = 1,
      limit = 10,
      status,
      sortBy = 'createdAt',
      sortDirection = 'DESC'
    } = queryDto;

    const skip = (page - 1) * limit;

    // Xây dựng điều kiện truy vấn
    const whereConditions: any = { userId };

    // Lọc theo trạng thái nếu có
    if (status) {
      whereConditions.status = status;
    }

    // Đếm tổng số bản ghi
    const total = await this.pointPurchaseTransactionRepository.count({
      where: whereConditions
    });

    // Lấy danh sách giao dịch với phân trang
    const transactions = await this.pointPurchaseTransactionRepository.find({
      where: whereConditions,
      order: { [sortBy]: sortDirection },
      skip,
      take: limit
    });

    // Chỉ lấy các trường cần thiết từ transaction
    const items = transactions.map(transaction => {
      return {
        id: transaction.id,
        amount: transaction.amount,
        pointsAmount: transaction.pointsAmount,
        pointName: transaction.pointName,
        status: transaction.status,
        couponAmount: transaction.couponAmount,
        completedAt: transaction.completedAt
      };
    });

    return {
      items,
      total,
      page,
      limit
    };
  }
}
