import { Injectable } from '@nestjs/common';
import { CouponRepository, PointRepository } from '@modules/r-point/repositories';
import { Coupon, Point } from '@modules/r-point/entities';
import { CouponStatus, DiscountType } from '@modules/r-point/enums';
import { RandomCouponRequestDto, ValidateCouponRequestDto } from '../dto/coupon-request.dto';
import { CouponResponseDto, ValidateCouponResponseDto } from '../dto/coupon-response.dto';
import { ApplyCouponRequestDto, ApplyCouponResponseDto } from '../dto';
import { LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { AppException, ErrorCode } from '@common/exceptions';
import { RPOINT_ERROR_CODES } from '@modules/r-point/errors';

/**
 * Service xử lý logic liên quan đến coupon cho người dùng
 */
@Injectable()
export class CouponUserService {
  constructor(
    private readonly couponRepository: CouponRepository,
    private readonly pointRepository: PointRepository
  ) {}

  /**
   * Lấy ngẫu nhiên 2-3 coupon đủ điều kiện sử dụng cho đơn hàng
   * @param dto Thông tin về gói point và số lượng point
   * @param userId ID của người dùng
   * @returns Danh sách coupon phù hợp
   */
  async getRandomCoupons(dto: RandomCouponRequestDto): Promise<CouponResponseDto[]> {
    // Kiểm tra gói point có tồn tại không
    const point = await this.pointRepository.findOne({ where: { id: dto.pointId } });
    if (!point) {
      throw new AppException(RPOINT_ERROR_CODES.POINT_PACKAGE_NOT_FOUND, `Không tìm thấy gói point với ID ${dto.pointId}`);
    }

    // Tính toán giá trị đơn hàng
    const orderValue = this.calculateOrderValue(point, dto.pointAmount);

    // Lấy thời gian hiện tại
    const now = Date.now();

    // Tìm các coupon phù hợp
    const coupons = await this.couponRepository.find({
      where: {
        status: CouponStatus.ACTIVE,
        minOrderValue: LessThanOrEqual(orderValue),
        startDate: LessThanOrEqual(now),
        endDate: MoreThanOrEqual(now)
      },
      order: {
        discountValue: 'DESC'
      },
      take: 5 // Lấy 5 coupon để sau đó chọn ngẫu nhiên 2-3 cái
    });

    if (coupons.length === 0) {
      return [];
    }

    // Chọn ngẫu nhiên 2-3 coupon
    const numberOfCoupons = Math.min(coupons.length, Math.floor(Math.random() * 2) + 2); // 2 hoặc 3
    const shuffledCoupons = this.shuffleArray([...coupons]);
    const selectedCoupons = shuffledCoupons.slice(0, numberOfCoupons);

    return selectedCoupons.map(coupon => this.mapCouponToDto(coupon));
  }

  /**
   * Kiểm tra mã coupon có hợp lệ và áp dụng được cho đơn hàng không
   * @param dto Thông tin về mã coupon, gói point và số lượng point
   * @param userId ID của người dùng
   * @returns Kết quả kiểm tra coupon
   */
  async validateCoupon(dto: ValidateCouponRequestDto, userId: number): Promise<ValidateCouponResponseDto> {
    // Kiểm tra gói point có tồn tại không
    const point = await this.pointRepository.findOne({ where: { id: dto.pointId } });
    if (!point) {
      throw new AppException(RPOINT_ERROR_CODES.POINT_PACKAGE_NOT_FOUND, `Không tìm thấy gói point với ID ${dto.pointId}`);
    }

    // Tìm coupon theo mã
    const coupon = await this.couponRepository.findOne({ where: { code: dto.couponCode } });
    if (!coupon) {
      throw new AppException(RPOINT_ERROR_CODES.COUPON_NOT_FOUND, `Không tìm thấy mã giảm giá ${dto.couponCode}`);
    }

    // Tính toán giá trị đơn hàng
    const orderValue = this.calculateOrderValue(point, dto.pointAmount);

    // Kiểm tra tính hợp lệ của coupon
    const validationResult = await this.checkCouponValidity(coupon, orderValue, userId);

    if (!validationResult.isValid) {
      throw new AppException(
        validationResult.errorCode || RPOINT_ERROR_CODES.COUPON_INVALID,
        validationResult.message
      );
    }

    // Tính toán số tiền giảm giá
    const discountAmount = this.calculateDiscountAmount(coupon, orderValue);
    const finalPrice = orderValue - discountAmount;

    return {
      coupon: this.mapCouponToDto(coupon),
      originalPrice: orderValue,
      discountAmount,
      finalPrice,
      isValid: true
    };
  }

  /**
   * Tính toán giá trị đơn hàng dựa trên gói point và số lượng point
   * @param point Thông tin gói point
   * @param pointAmount Số lượng point
   * @returns Giá trị đơn hàng (VND)
   */
  private calculateOrderValue(point: Point, pointAmount: number): number {
    if (point.isCustomize) {
      // Đối với gói customize, sử dụng rate để tính toán
      return pointAmount * point.rate;
    } else {
      // Đối với gói cố định, sử dụng giá cố định
      return point.cash;
    }
  }

  /**
   * Kiểm tra tính hợp lệ của coupon
   * @param coupon Thông tin coupon
   * @param orderValue Giá trị đơn hàng
   * @param userId ID của người dùng
   * @returns Kết quả kiểm tra
   */
  private async checkCouponValidity(coupon: Coupon, orderValue: number, userId: number): Promise<{ isValid: boolean; message?: string; errorCode?: ErrorCode }> {
    // Kiểm tra trạng thái coupon
    if (coupon.status !== CouponStatus.ACTIVE) {
      return {
        isValid: false,
        message: 'Mã giảm giá không hoạt động',
        errorCode: RPOINT_ERROR_CODES.COUPON_INACTIVE
      };
    }

    // Kiểm tra thời hạn
    const now = Date.now();
    if (coupon.startDate > now) {
      return {
        isValid: false,
        message: 'Mã giảm giá chưa đến thời gian sử dụng',
        errorCode: RPOINT_ERROR_CODES.COUPON_NOT_STARTED
      };
    }
    if (coupon.endDate < now) {
      return {
        isValid: false,
        message: 'Mã giảm giá đã hết hạn',
        errorCode: RPOINT_ERROR_CODES.COUPON_EXPIRED
      };
    }

    // Kiểm tra giá trị đơn hàng tối thiểu
    if (orderValue < coupon.minOrderValue) {
      return {
        isValid: false,
        message: `Giá trị đơn hàng tối thiểu để sử dụng mã này là ${coupon.minOrderValue.toLocaleString('vi-VN')} VND`,
        errorCode: RPOINT_ERROR_CODES.ORDER_VALUE_TOO_LOW
      };
    }

    // TODO: Kiểm tra số lần sử dụng của người dùng (cần thêm repository cho bảng coupon_usage)
    // Vì chưa có repository này nên tạm thời bỏ qua

    return { isValid: true };
  }

  /**
   * Tính toán số tiền giảm giá
   * @param coupon Thông tin coupon
   * @param orderValue Giá trị đơn hàng
   * @returns Số tiền giảm giá
   */
  private calculateDiscountAmount(coupon: Coupon, orderValue: number): number {
    let discountAmount = 0;

    if (coupon.discountType === DiscountType.PERCENTAGE) {
      // Giảm giá theo phần trăm
      discountAmount = (orderValue * coupon.discountValue) / 100;

      // Áp dụng giảm giá tối đa nếu có
      if (coupon.maxDiscountAmount && discountAmount > coupon.maxDiscountAmount) {
        discountAmount = coupon.maxDiscountAmount;
      }
    } else {
      // Giảm giá theo số tiền cố định
      discountAmount = coupon.discountValue;

      // Đảm bảo số tiền giảm giá không vượt quá giá trị đơn hàng
      if (discountAmount > orderValue) {
        discountAmount = orderValue;
      }
    }

    return discountAmount;
  }

  /**
   * Chuyển đổi Coupon entity sang DTO
   * @param coupon Coupon entity
   * @returns CouponResponseDto
   */
  private mapCouponToDto(coupon: Coupon): CouponResponseDto {
    // Chuyển đổi null thành undefined cho maxDiscountAmount
    const { maxDiscountAmount, ...rest } = coupon;

    return {
      ...rest,
      maxDiscountAmount: maxDiscountAmount !== null ? maxDiscountAmount : undefined,
    };
  }

  /**
   * Xáo trộn mảng
   * @param array Mảng cần xáo trộn
   * @returns Mảng đã được xáo trộn
   */
  private shuffleArray<T>(array: T[]): T[] {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
  }

  /**
   * Áp dụng mã giảm giá cho đơn hàng mua point
   * @param dto Thông tin về mã coupon, gói point và số lượng point
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết về đơn hàng sau khi áp dụng mã giảm giá
   */
  async applyCoupon(dto: ApplyCouponRequestDto, userId: number): Promise<ApplyCouponResponseDto> {
    // Kiểm tra gói point có tồn tại không
    const point = await this.pointRepository.findOne({ where: { id: dto.pointPackageId } });
    if (!point) {
      throw new AppException(RPOINT_ERROR_CODES.POINT_PACKAGE_NOT_FOUND, `Không tìm thấy gói point với ID ${dto.pointPackageId}`);
    }

    // Tìm coupon theo mã
    const coupon = await this.couponRepository.findOne({ where: { code: dto.couponCode } });
    if (!coupon) {
      throw new AppException(RPOINT_ERROR_CODES.COUPON_NOT_FOUND, `Không tìm thấy mã giảm giá ${dto.couponCode}`);
    }

    // Tính toán giá trị đơn hàng
    const orderValue = this.calculateOrderValue(point, dto.pointAmount);

    // Kiểm tra tính hợp lệ của coupon
    const validationResult = await this.checkCouponValidity(coupon, orderValue, userId);

    if (!validationResult.isValid) {
      throw new AppException(
        validationResult.errorCode || RPOINT_ERROR_CODES.COUPON_INVALID,
        validationResult.message
      );
    }

    // Tính toán số tiền giảm giá
    const discountAmount = this.calculateDiscountAmount(coupon, orderValue);
    const finalPrice = orderValue - discountAmount;

    // Tạo response
    return {
      pointAmount: dto.pointAmount,
      listPrice: orderValue,
      priceBeforeVAT: finalPrice,
      vatAmount: 0, // Không có VAT cho R-Point
      totalPayment: finalPrice,
      appliedCoupon: this.mapCouponToDto(coupon),
      discountAmount
    };
  }
}
