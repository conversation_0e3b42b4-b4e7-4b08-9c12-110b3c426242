import { Injectable, NotFoundException } from '@nestjs/common';
import {
  CouponRepository,
  PointPurchaseTransactionRepository,
  PointRepository,
} from '@modules/r-point/repositories';
import { Between, ILike, In, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { TransactionQueryDto } from '../dto/transaction-query.dto';
import { StatisticsResponseDto, TransactionPaginatedResponseDto, TransactionResponseDto } from '../dto';
import { UserService } from '@/modules/user/user/service/user.service';

/**
 * Service xử lý logic liên quan đến giao dịch mua point cho admin
 */
@Injectable()
export class TransactionAdminService {
  constructor(
    private readonly transactionRepository: PointPurchaseTransactionRepository,
    private readonly pointRepository: PointRepository,
    private readonly couponRepository: CouponRepository,
    private readonly userService: UserService,
  ) {}

  /**
   * Lấy danh sách giao dịch của người dùng với phân trang và lọc
   * @param queryDto Tham số truy vấn
   * @returns Danh sách giao dịch và thông tin phân trang
   */
  async getUserTransactions(
    queryDto: TransactionQueryDto,
  ): Promise<TransactionPaginatedResponseDto> {
    const {
      page = 1,
      limit = 10,
      userId,
      status,
      keyword,
      startTime,
      endTime,
    } = queryDto;
    const skip = (page - 1) * limit;

    // Xây dựng điều kiện truy vấn
    const whereConditions: any = {};

    // Lọc theo userId nếu có
    if (userId) {
      whereConditions.userId = userId;
    }

    // Lọc theo trạng thái nếu có
    if (status) {
      whereConditions.status = status;
    }

    // Lọc theo từ khóa (mã tham chiếu)
    if (keyword) {
      whereConditions.referenceId = ILike(`%${keyword}%`);
    }

    // Lọc theo khoảng thời gian
    if (startTime && endTime) {
      whereConditions.createdAt = Between(startTime, endTime);
    } else if (startTime) {
      whereConditions.createdAt = MoreThanOrEqual(startTime);
    } else if (endTime) {
      whereConditions.createdAt = LessThanOrEqual(endTime);
    }

    // Đếm tổng số giao dịch thỏa mãn điều kiện
    const total = await this.transactionRepository.count({
      where: whereConditions,
    });

    // Lấy danh sách giao dịch
    const transactions = await this.transactionRepository.find({
      where: whereConditions,
      order: { createdAt: 'DESC' },
      skip,
      take: limit,
    });

    // Lấy danh sách userId và pointId để lấy thông tin liên quan
    const userIds = transactions
      .map((t) => t.userId)
      .filter((id) => id !== null) as number[];
    const pointIds = transactions
      .map((t) => t.pointId)
      .filter((id) => id !== null) as number[];

    // Lấy thông tin người dùng và gói point
    const users =
      userIds.length > 0 ? await this.userService.findByIds(userIds) : [];
    const points =
      pointIds.length > 0
        ? await this.pointRepository.find({ where: { id: In(pointIds) } })
        : [];

    // Chuyển đổi dữ liệu và thêm thông tin liên quan
    const items = transactions.map((transaction) => {
      const user = users.find((u) => u.id === transaction.userId);
      const point = points.find((p) => p.id === transaction.pointId);

      return {
        ...transaction,
        user: user
          ? {
              id: user.id,
              fullName: user.fullName,
              email: user.email,
              phone: user.phoneNumber,
            }
          : null,
        point: point
          ? {
              id: point.id,
              name: point.name,
              cash: point.cash,
              rate: point.rate,
            }
          : null,
      };
    });

    // Tính toán thông tin phân trang
    const totalPages = Math.ceil(total / limit);

    return {
      items,
      total,
      page,
      limit,
      totalPages,
    };
  }

  /**
   * Lấy thống kê về r-point và giao dịch
   * @param startTime Thời gian bắt đầu (tùy chọn)
   * @param endTime Thời gian kết thúc (tùy chọn)
   * @returns Thống kê chi tiết
   */
  async getStatistics(
    startTime?: number,
    endTime?: number,
  ): Promise<StatisticsResponseDto> {
    // Xây dựng điều kiện thời gian nếu có
    const timeCondition: any = {};
    if (startTime && endTime) {
      timeCondition.createdAt = Between(startTime, endTime);
    } else if (startTime) {
      timeCondition.createdAt = MoreThanOrEqual(startTime);
    } else if (endTime) {
      timeCondition.createdAt = LessThanOrEqual(endTime);
    }

    // Lấy tất cả giao dịch trong khoảng thời gian
    const transactions = await this.transactionRepository.find({
      where: timeCondition,
    });

    // Thống kê tổng quan
    const totalTransactions = transactions.length;
    const totalAmount = transactions.reduce((sum, t) => sum + t.amount, 0);
    const totalPointsSold = transactions.reduce(
      (sum, t) => sum + t.pointsAmount,
      0,
    );
    const uniqueUsers = new Set(transactions.map((t) => t.userId)).size;
    const averageTransactionValue =
      totalTransactions > 0 ? totalAmount / totalTransactions : 0;

    // Thống kê theo trạng thái
    const byStatus = this.groupByStatus(transactions);

    // Thống kê theo phương thức thanh toán
    const byPaymentMethod = this.groupByPaymentMethod(transactions);

    // Lấy danh sách pointId để lấy thông tin gói point
    const pointIds = [
      ...new Set(
        transactions.map((t) => t.pointId).filter((id) => id !== null),
      ),
    ] as number[];
    const points =
      pointIds.length > 0
        ? await this.pointRepository.find({ where: { id: In(pointIds) } })
        : [];

    // Thống kê theo gói point
    const byPointPackage = this.groupByPointPackage(transactions, points);

    // Lấy danh sách couponId để lấy thông tin coupon
    const couponIds = [
      ...new Set(
        transactions.map((t) => t.couponId).filter((id) => id !== null),
      ),
    ] as number[];
    const coupons =
      couponIds.length > 0
        ? await this.couponRepository.find({ where: { id: In(couponIds) } })
        : [];

    // Thống kê theo coupon
    const byCoupon = this.groupByCoupon(transactions, coupons);

    return {
      overview: {
        totalTransactions,
        totalAmount,
        totalPointsSold,
        uniqueUsers,
        averageTransactionValue,
      },
      details: {
        byStatus,
        byPaymentMethod,
        byPointPackage,
        byCoupon,
      },
    };
  }

  /**
   * Lấy thông tin chi tiết của một giao dịch
   * @param id ID của giao dịch
   * @returns Thông tin chi tiết giao dịch
   */
  async getTransactionById(id: number): Promise<TransactionResponseDto> {
    const transaction = await this.transactionRepository.findOne({
      where: { id },
    });
    if (!transaction) {
      throw new NotFoundException(`Không tìm thấy giao dịch với ID ${id}`);
    }

    // Lấy thông tin người dùng và gói point
    const user = transaction.userId
      ? await this.userService.findOne(transaction.userId)
      : null;
    const point = transaction.pointId
      ? await this.pointRepository.findOne({
          where: { id: transaction.pointId },
        })
      : null;

    return {
      ...transaction,
      user: user
        ? {
            id: user.id,
            fullName: user.fullName,
            email: user.email,
            phone: user.phoneNumber,
          }
        : null,
      point: point
        ? {
            id: point.id,
            name: point.name,
            cash: point.cash,
            rate: point.rate,
          }
        : null,
    };
  }

  /**
   * Nhóm các giao dịch theo trạng thái
   * @param transactions Danh sách giao dịch
   * @returns Thống kê theo trạng thái
   */
  private groupByStatus(transactions: any[]): any[] {
    const statusGroups = transactions.reduce((groups, transaction) => {
      const status = transaction.status;
      if (!groups[status]) {
        groups[status] = {
          status,
          count: 0,
          totalAmount: 0,
        };
      }
      groups[status].count++;
      groups[status].totalAmount += transaction.amount;
      return groups;
    }, {});

    return Object.values(statusGroups);
  }

  /**
   * Nhóm các giao dịch theo phương thức thanh toán
   * @param transactions Danh sách giao dịch
   * @returns Thống kê theo phương thức thanh toán
   */
  private groupByPaymentMethod(transactions: any[]): any[] {
    const paymentMethodGroups = transactions.reduce((groups, transaction) => {
      const paymentMethod = transaction.paymentMethod;
      if (!groups[paymentMethod]) {
        groups[paymentMethod] = {
          paymentMethod,
          count: 0,
          totalAmount: 0,
        };
      }
      groups[paymentMethod].count++;
      groups[paymentMethod].totalAmount += transaction.amount;
      return groups;
    }, {});

    return Object.values(paymentMethodGroups);
  }

  /**
   * Nhóm các giao dịch theo gói point
   * @param transactions Danh sách giao dịch
   * @param points Danh sách gói point
   * @returns Thống kê theo gói point
   */
  private groupByPointPackage(transactions: any[], points: any[]): any[] {
    const pointGroups = transactions.reduce((groups, transaction) => {
      if (!transaction.pointId) return groups;

      const pointId = transaction.pointId;
      const point = points.find((p) => p.id === pointId);
      const pointName = point ? point.name : `Point ID ${pointId}`;

      if (!groups[pointId]) {
        groups[pointId] = {
          pointId,
          pointName,
          count: 0,
          totalAmount: 0,
          totalPoints: 0,
        };
      }
      groups[pointId].count++;
      groups[pointId].totalAmount += transaction.amount;
      groups[pointId].totalPoints += transaction.pointsAmount;
      return groups;
    }, {});

    return Object.values(pointGroups);
  }

  /**
   * Nhóm các giao dịch theo coupon
   * @param transactions Danh sách giao dịch
   * @param coupons Danh sách coupon
   * @returns Thống kê theo coupon
   */
  private groupByCoupon(transactions: any[], coupons: any[]): any[] {
    const couponGroups = transactions.reduce((groups, transaction) => {
      if (!transaction.couponId || !transaction.couponAmount) return groups;

      const couponId = transaction.couponId;
      const coupon = coupons.find((c) => c.id === couponId);
      const couponCode = coupon ? coupon.code : `Coupon ID ${couponId}`;

      if (!groups[couponId]) {
        groups[couponId] = {
          couponId,
          couponCode,
          usageCount: 0,
          totalDiscountAmount: 0,
        };
      }
      groups[couponId].usageCount++;
      groups[couponId].totalDiscountAmount += transaction.couponAmount;
      return groups;
    }, {});

    return Object.values(couponGroups);
  }
}
