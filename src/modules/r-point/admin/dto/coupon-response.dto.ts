import { ApiProperty } from '@nestjs/swagger';
import { DiscountType, CouponStatus } from '@modules/r-point/enums';

/**
 * DTO cho response trả về thông tin coupon
 */
export class CouponResponseDto {
  @ApiProperty({
    description: 'ID của coupon',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Mã giảm giá',
    example: 'SUMMER2023',
  })
  code: string;

  @ApiProperty({
    description: 'Mô tả chi tiết về mã giảm giá',
    example: 'Giảm giá 10% cho tất cả các gói point trong mùa hè 2023',
  })
  description: string;

  @ApiProperty({
    description: 'Loại giảm giá: phần trăm hoặc số tiền cố định',
    enum: DiscountType,
    example: DiscountType.PERCENTAGE,
  })
  discountType: DiscountType;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> trị giảm giá tương ứng với loại (%, số tiền)',
    example: 10, // 10% hoặc 10,000 VND
  })
  discountValue: number;

  @ApiProperty({
    description: 'Giá trị đơn hàng tối thiểu để áp dụng mã giảm giá',
    example: 100000,
  })
  minOrderValue: number;

  @ApiProperty({
    description: 'Giảm giá tối đa cho mã giảm giá loại phần trăm',
    example: 50000,
    nullable: true,
  })
  maxDiscountAmount: number | null;

  @ApiProperty({
    description: 'Thời điểm bắt đầu áp dụng mã (Unix timestamp)',
    example: 1625097600000, // 2021-07-01
  })
  startDate: number;

  @ApiProperty({
    description: 'Thời điểm kết thúc áp dụng mã (Unix timestamp)',
    example: 1627689600000, // 2021-07-31
  })
  endDate: number;

  @ApiProperty({
    description: 'Tổng số lần sử dụng tối đa cho toàn bộ hệ thống',
    example: 100,
    nullable: true,
  })
  usageLimit: number | null;

  @ApiProperty({
    description: 'Số lần một người dùng được sử dụng mã này',
    example: 1,
  })
  perUserLimit: number;

  @ApiProperty({
    description: 'Trạng thái mã giảm giá',
    enum: CouponStatus,
    example: CouponStatus.ACTIVE,
  })
  status: CouponStatus;

  @ApiProperty({
    description: 'Thời gian tạo mã (Unix timestamp)',
    example: 1625011200000, // 2021-06-30
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật mã gần nhất (Unix timestamp)',
    example: 1625011200000, // 2021-06-30
  })
  updatedAt: number;
}
