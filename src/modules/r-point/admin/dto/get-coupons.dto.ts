import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum } from 'class-validator';
import { QueryDto } from '@/common/dto/query.dto';

export enum CouponStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  EXPIRED = 'EXPIRED'
}

export class GetCouponsDto extends QueryDto {
  @ApiProperty({
    description: 'Trạng thái coupon',
    required: false,
    enum: CouponStatus
  })
  @IsEnum(CouponStatus)
  @IsOptional()
  status?: CouponStatus;
} 