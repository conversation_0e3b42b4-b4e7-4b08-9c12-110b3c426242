import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsNumber, IsOptional, IsPositive, IsString, MaxLength, Min } from 'class-validator';
import { DiscountType } from '@modules/r-point/enums';

/**
 * DTO cho việc tạo mới coupon
 */
export class CreateCouponDto {
  @ApiProperty({
    description: 'Mã giảm giá',
    example: 'SUMMER2023',
    maxLength: 50,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(50)
  code: string;

  @ApiProperty({
    description: 'Mô tả chi tiết về mã giảm giá',
    example: 'Giảm giá 10% cho tất cả các gói point trong mùa hè 2023',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Loại giảm giá: phần trăm hoặc số tiền cố định',
    enum: DiscountType,
    example: DiscountType.PERCENTAGE,
  })
  @IsNotEmpty()
  @IsEnum(DiscountType)
  discountType: DiscountType;

  @ApiProperty({
    description: 'Giá trị giảm giá tương ứng với loại (%, số tiền)',
    example: 10, // 10% hoặc 10,000 VND
  })
  @IsNotEmpty()
  @IsNumber()
  @IsPositive()
  discountValue: number;

  @ApiProperty({
    description: 'Giá trị đơn hàng tối thiểu để áp dụng mã giảm giá',
    example: 100000,
    default: 0,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  minOrderValue?: number;

  @ApiProperty({
    description: 'Giảm giá tối đa cho mã giảm giá loại phần trăm',
    example: 50000,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  maxDiscountAmount?: number;

  @ApiProperty({
    description: 'Thời điểm bắt đầu áp dụng mã (Unix timestamp)',
    example: 1625097600000, // 2021-07-01
  })
  @IsNotEmpty()
  @IsNumber()
  startDate: number;

  @ApiProperty({
    description: 'Thời điểm kết thúc áp dụng mã (Unix timestamp)',
    example: 1627689600000, // 2021-07-31
  })
  @IsNotEmpty()
  @IsNumber()
  endDate: number;

  @ApiProperty({
    description: 'Tổng số lần sử dụng tối đa cho toàn bộ hệ thống',
    example: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  usageLimit?: number;

  @ApiProperty({
    description: 'Số lần một người dùng được sử dụng mã này',
    example: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  perUserLimit?: number;
}
