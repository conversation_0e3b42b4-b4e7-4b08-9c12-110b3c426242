import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng audience_admin_custom_fields trong cơ sở dữ liệu
 * Lưu thông tin các trường tùy chỉnh mà admin có thể định nghĩa động
 */
@Entity('audience_admin_custom_fields')
export class AdminAudienceCustomFieldDefinition {
  /**
   * Định danh duy nhất cho trường tùy chỉnh (chữ thường, không dấu cách)
   */
  @PrimaryColumn({ name: 'field_key', length: 100 })
  fieldKey: string;

  /**
   * ID của admin mà trường tùy chỉnh này thuộc về
   */
  @Column({ name: 'created_by' })
  createdBy: number;

  /**
   * Tên hiển thị thân thiện với admin
   */
  @Column({ name: 'display_name', length: 255 })
  displayName: string;

  /**
   * <PERSON><PERSON><PERSON> dữ liệu: string, integer, date, boolean, v.v.
   */
  @Column({ name: 'data_type', length: 50 })
  dataType: string;

  /**
   * <PERSON><PERSON> tả chi tiết hoặc ghi chú về trường tùy chỉnh
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;
}
