import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng admin_campaign_history trong cơ sở dữ liệu
 * Bảng lịch sử chăm sóc khách hàng của admin
 */
@Entity('admin_campaign_history')
export class AdminCampaignHistory {
  /**
   * ID của lịch sử
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của chiến dịch
   */
  @Column({ name: 'campaign_id', type: 'bigint', nullable: true, comment: 'Mã chiến dịch' })
  campaignId: number;

  /**
   * ID của khách hàng
   */
  @Column({ name: 'audience_id', type: 'bigint', nullable: true, comment: 'Mã khách hàng' })
  audienceId: number;

  /**
   * Trạng thái gửi
   */
  @Column({ name: 'status', length: 20, nullable: true, comment: 'Trạng thái' })
  status: string;

  /**
   * Thời gian gửi
   */
  @Column({ name: 'sent_at', type: 'bigint', nullable: true, comment: 'Thời gian gửi' })
  sentAt: number;

  /**
   * Thời gian tạo
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true, comment: 'Thời gian tạo' })
  createdAt: number;

  // Không sử dụng quan hệ với các bảng khác, chỉ lưu ID
}
