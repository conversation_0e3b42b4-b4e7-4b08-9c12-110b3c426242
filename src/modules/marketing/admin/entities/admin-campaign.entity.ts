import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng admin_campaigns trong cơ sở dữ liệu
 * Bảng chiến dịch của admin
 */
@Entity('admin_campaigns')
export class AdminCampaign {
  /**
   * ID của campaign
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * Tiêu đề chiến dịch
   */
  @Column({ name: 'title', length: 255, nullable: true, comment: 'Tiêu đề' })
  title: string;

  /**
   * <PERSON>ô tả chiến dịch
   */
  @Column({ name: 'description', type: 'text', nullable: true, comment: '<PERSON><PERSON> tả' })
  description: string;

  /**
   * Nền tảng gửi (email, sms, zalo, ...)
   */
  @Column({ name: 'platform', length: 255, nullable: true, comment: 'Nền tảng' })
  platform: string;

  /**
   * Nội dung chiến dịch
   */
  @Column({ name: 'content', type: 'text', nullable: true, comment: 'Nội dung' })
  content: string;

  /**
   * Thông tin máy chủ gửi
   */
  @Column({ name: 'server', type: 'jsonb', nullable: true, comment: 'Thông tin máy chủ gửi' })
  server: any;

  /**
   * Thời gian dự kiến gửi chiến dịch
   */
  @Column({ name: 'scheduled_at', type: 'bigint', nullable: true, comment: 'Thời gian dự kiến gửi chiến dịch' })
  scheduledAt: number;

  /**
   * Tiêu đề email (nếu platform là email)
   */
  @Column({ name: 'subject', length: 255, nullable: true, comment: 'Nội dung tiêu đề với chiến dịch là email' })
  subject: string;

  /**
   * Trạng thái chiến dịch
   */
  @Column({ name: 'status', length: 20, nullable: true, comment: 'Trạng thái' })
  status: string;

  /**
   * ID của nhân viên tạo chiến dịch
   */
  @Column({ name: 'employee_id', nullable: true, comment: 'Mã nhân viên' })
  employeeId: number;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true, comment: 'Ngày tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Ngày cập nhật' })
  updatedAt: number;

  // Không sử dụng quan hệ với các bảng khác, chỉ lưu ID
}
