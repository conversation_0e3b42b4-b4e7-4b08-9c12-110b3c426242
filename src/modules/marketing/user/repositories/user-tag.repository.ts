import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, FindOptionsWhere, Repository } from 'typeorm';
import { UserTag } from '../entities/user-tag.entity';

/**
 * Repository cho UserTag
 */
@Injectable()
export class UserTagRepository {
  constructor(
    @InjectRepository(UserTag)
    private readonly repository: Repository<UserTag>,
  ) {}

  /**
   * Tìm kiếm nhiều tag
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách tag
   */
  async find(options?: FindManyOptions<UserTag>): Promise<UserTag[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một tag
   * @param options Tùy chọn tìm kiếm
   * @returns Tag hoặc null
   */
  async findOne(options?: FindOneOptions<UserTag>): Promise<UserTag | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Đếm số lượng tag
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng tag
   */
  async count(options?: FindManyOptions<UserTag>): Promise<number> {
    return this.repository.countBy(options?.where || {});
  }

  /**
   * Lưu tag
   * @param tag Tag cần lưu
   * @returns Tag đã lưu
   */
  async save(tag: UserTag): Promise<UserTag>;
  async save(tag: UserTag[]): Promise<UserTag[]>;
  async save(tag: UserTag | UserTag[]): Promise<UserTag | UserTag[]> {
    return this.repository.save(tag as any);
  }

  /**
   * Xóa tag
   * @param tag Tag cần xóa
   * @returns Tag đã xóa
   */
  async remove(tag: UserTag): Promise<UserTag>;
  async remove(tag: UserTag[]): Promise<UserTag[]>;
  async remove(tag: UserTag | UserTag[]): Promise<UserTag | UserTag[]> {
    return this.repository.remove(tag as any);
  }
}
