import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { CustomFieldDataType } from './create-audience-custom-field-definition.dto';

/**
 * DTO cho việc cập nhật trường tùy chỉnh
 */
export class UpdateAudienceCustomFieldDefinitionDto {
  /**
   * Tên hiển thị thân thiện với người dùng
   * @example "Địa chỉ khách hàng"
   */
  @ApiProperty({
    description: 'Tên hiển thị thân thiện với người dùng',
    example: 'Địa chỉ khách hàng',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên hiển thị phải là chuỗi' })
  displayName?: string;

  /**
   * Kiểu dữ liệu: string, integer, date, boolean, json
   * @example "string"
   */
  @ApiProperty({
    description: 'Kiểu dữ liệu',
    enum: CustomFieldDataType,
    example: CustomFieldDataType.STRING,
    required: false,
  })
  @IsOptional()
  @IsEnum(CustomFieldDataType, {
    message: `Kiểu dữ liệu phải là một trong các giá trị: ${Object.values(CustomFieldDataType).join(', ')}`,
  })
  dataType?: CustomFieldDataType;

  /**
   * Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh
   * @example "Địa chỉ liên hệ của khách hàng"
   */
  @ApiProperty({
    description: 'Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh',
    example: 'Địa chỉ liên hệ của khách hàng',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Mô tả phải là chuỗi' })
  description?: string;
}
