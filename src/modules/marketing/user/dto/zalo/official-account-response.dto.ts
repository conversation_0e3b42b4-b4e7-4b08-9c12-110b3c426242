import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi thông tin Official Account
 */
export class OfficialAccountResponseDto {
  @ApiProperty({
    description: 'ID của Official Account trong hệ thống',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của Official Account trên <PERSON>',
    example: '*********',
  })
  oaId: string;

  @ApiProperty({
    description: 'Tên của Official Account',
    example: 'RedAI Official',
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả của Official Account',
    example: '<PERSON><PERSON><PERSON> chính thức của RedAI',
    nullable: true,
  })
  description?: string;

  @ApiProperty({
    description: 'URL avatar của Official Account',
    example: 'https://zalo.me/avatar/*********.jpg',
    nullable: true,
  })
  avatarUrl?: string;

  @ApiProperty({
    description: 'Tr<PERSON>ng thái kết nối (active, inactive)',
    example: 'active',
  })
  status: string;

  @ApiProperty({
    description: 'Thời điểm tạo (Unix timestamp)',
    example: *************,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật (Unix timestamp)',
    example: *************,
  })
  updatedAt: number;
}
