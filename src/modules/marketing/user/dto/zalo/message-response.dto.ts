import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho phản hồi thông tin tin nhắn
 */
export class MessageResponseDto {
  @ApiProperty({
    description: 'ID của tin nhắn trong hệ thống',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID của tin nhắn trên Zalo',
    example: 'msg123456789',
    nullable: true,
  })
  messageId?: string;

  @ApiProperty({
    description: 'Loại tin nhắn (text, image, file, template)',
    example: 'text',
  })
  messageType: string;

  @ApiProperty({
    description: 'Nội dung tin nhắn',
    example: 'Xin chào! Cảm ơn bạn đã quan tâm đến sản phẩm của chúng tôi.',
    nullable: true,
  })
  content?: string;

  @ApiProperty({
    description: 'Dữ liệu bổ sung của tin nhắn',
    example: { url: 'https://example.com/image.jpg' },
    nullable: true,
  })
  data?: any;

  @ApiProperty({
    description: 'H<PERSON>ớng tin nhắn (incoming, outgoing)',
    example: 'outgoing',
  })
  direction: string;

  @ApiProperty({
    description: 'Thời điểm gửi/nhận (Unix timestamp)',
    example: 1625097600000,
  })
  timestamp: number;
}
