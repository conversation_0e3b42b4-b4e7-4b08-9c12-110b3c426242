import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng zalo_followers trong cơ sở dữ liệu
 * Lưu trữ thông tin về người dùng Zalo đã theo dõi Official Account
 */
@Entity('zalo_followers')
export class ZaloFollower {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của Official Account
   */
  @Column({ name: 'oa_id', length: 50 })
  oaId: string;

  /**
   * ID của người dùng Zalo
   */
  @Column({ name: 'user_id', length: 50 })
  userId: string;

  /**
   * Tên hiển thị của người dùng
   */
  @Column({ name: 'display_name', length: 255, nullable: true })
  displayName: string;

  /**
   * URL avatar của người dùng
   */
  @Column({ name: 'avatar_url', length: 500, nullable: true })
  avatarUrl: string;

  /**
   * <PERSON><PERSON> điện thoại của người dùng (nếu đư<PERSON> cấp quyền)
   */
  @Column({ name: 'phone', length: 20, nullable: true })
  phone: string;

  /**
   * Giới tính của người dùng (1: Nam, 2: Nữ)
   */
  @Column({ name: 'gender', type: 'smallint', nullable: true })
  gender: number;

  /**
   * Ngày sinh của người dùng (định dạng dd/mm/yyyy)
   */
  @Column({ name: 'birth_date', length: 20, nullable: true })
  birthDate: string;

  /**
   * Các tag gán cho người dùng (JSON)
   */
  @Column({ name: 'tags', type: 'jsonb', default: '[]' })
  tags: string[];

  /**
   * Thời điểm theo dõi (Unix timestamp)
   */
  @Column({ name: 'followed_at', type: 'bigint' })
  followedAt: number;

  /**
   * Thời điểm hủy theo dõi (Unix timestamp)
   */
  @Column({ name: 'unfollowed_at', type: 'bigint', nullable: true })
  unfollowedAt: number;

  /**
   * Trạng thái (active, unfollowed)
   */
  @Column({ name: 'status', length: 20, default: 'active' })
  status: string;

  /**
   * Thời điểm tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời điểm cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
