import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng user_template_email trong cơ sở dữ liệu
 * Bảng template email của người dùng
 */
@Entity('user_template_email')
export class UserTemplateEmail {
  /**
   * ID của template
   */
  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
  id: number;

  /**
   * ID của người dùng
   */
  @Column({ name: 'user_id', nullable: true })
  userId: number;

  // Không sử dụng quan hệ với bảng User, chỉ lưu ID

  /**
   * Tên template
   */
  @Column({ name: 'name', length: 255, nullable: true, comment: 'Tên email' })
  name: string;

  /**
   * Tiêu đề email
   */
  @Column({ name: 'subject', length: 255, nullable: true, comment: 'Tiêu đề email' })
  subject: string;

  /**
   * Nội dung email
   */
  @Column({ name: 'content', type: 'text', nullable: true, comment: 'Nội dung email' })
  content: string;

  /**
   * <PERSON><PERSON>c tag của template
   */
  @Column({ name: 'tags', type: 'jsonb', nullable: true, comment: 'Nhãn cho email' })
  tags: any;

  /**
   * Các placeholder trong template
   */
  @Column({ name: 'placeholders', type: 'json', nullable: true, comment: 'Biến truyền vào' })
  placeholders: any;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true, comment: 'Thời gian tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true, comment: 'Thời gian cập nhật' })
  updatedAt: number;
}
