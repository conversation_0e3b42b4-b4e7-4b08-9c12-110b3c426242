import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ZaloSegmentConditionDto } from '../dto/zalo';

/**
 * Entity cho phân đoạn Zalo
 */
@Entity('zalo_segments')
export class ZaloSegment {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'oa_id' })
  oaId: string;

  @Column()
  name: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ type: 'json' })
  conditions: ZaloSegmentConditionDto[];

  @Column({ name: 'follower_count', default: 0 })
  followerCount: number;

  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;
}
