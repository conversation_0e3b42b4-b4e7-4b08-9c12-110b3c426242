import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { UserTaskExecution } from '../entities/user-task-execution.entity';

/**
 * Repository cho entity UserTaskExecution
 * Xử lý các thao tác truy vấn dữ liệu liên quan đến bảng user_task_executions
 */
@Injectable()
export class UserTaskExecutionRepository extends Repository<UserTaskExecution> {
  /**
   * Constructor
   * @param dataSource Nguồn dữ liệu TypeORM
   */
  constructor(private dataSource: DataSource) {
    super(UserTaskExecution, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho UserTaskExecution
   * @returns SelectQueryBuilder cho UserTaskExecution
   */
  private createBaseQuery(): SelectQueryBuilder<UserTaskExecution> {
    return this.createQueryBuilder('execution');
  }
}
