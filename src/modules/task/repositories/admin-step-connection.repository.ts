import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AdminStepConnection } from '../entities/admin-step-connection.entity';

/**
 * Repository cho entity AdminStepConnection
 * Xử lý các thao tác truy vấn dữ liệu liên quan đến bảng admin_step_connections
 */
@Injectable()
export class AdminStepConnectionRepository extends Repository<AdminStepConnection> {
  /**
   * Constructor
   * @param dataSource Nguồn dữ liệu TypeORM
   */
  constructor(private dataSource: DataSource) {
    super(AdminStepConnection, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AdminStepConnection
   * @returns SelectQueryBuilder cho AdminStepConnection
   */
  private createBaseQuery(): SelectQueryBuilder<AdminStepConnection> {
    return this.createQueryBuilder('connection');
  }
}
