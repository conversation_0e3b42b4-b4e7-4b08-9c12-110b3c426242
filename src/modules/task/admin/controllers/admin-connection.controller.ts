import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { Controller, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AdminConnectionService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các endpoint liên quan đến kết nối giữa các bước trong task của admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_TASK)
@Controller('admin/step-connections')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class AdminConnectionController {
  /**
   * Constructor
   * @param adminConnectionService Service xử lý logic liên quan đến kết nối giữa các bước trong task của admin
   */
  constructor(private readonly adminConnectionService: AdminConnectionService) {}
}
