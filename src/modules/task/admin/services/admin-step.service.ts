import { Injectable, Logger } from '@nestjs/common';
import { AdminStepRepository } from '../../repositories';

/**
 * Service xử lý logic liên quan đến các bước trong task của admin
 */
@Injectable()
export class AdminStepService {
  /**
   * Logger cho AdminStepService
   */
  private readonly logger = new Logger(AdminStepService.name);

  /**
   * Constructor
   * @param adminStepRepository Repository xử lý dữ liệu các bước trong task của admin
   */
  constructor(
    private readonly adminStepRepository: AdminStepRepository,
  ) {}
}
