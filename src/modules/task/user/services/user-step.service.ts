import { Injectable, Logger } from '@nestjs/common';
import { UserStepRepository } from '../../repositories';

/**
 * Service xử lý logic liên quan đến các bước trong task của người dùng
 */
@Injectable()
export class UserStepService {
  /**
   * Logger cho UserStepService
   */
  private readonly logger = new Logger(UserStepService.name);

  /**
   * Constructor
   * @param userStepRepository Repository xử lý dữ liệu các bước trong task của người dùng
   */
  constructor(
    private readonly userStepRepository: UserStepRepository,
  ) {}
}
