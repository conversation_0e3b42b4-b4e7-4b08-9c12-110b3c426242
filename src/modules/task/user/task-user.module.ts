import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  UserTask,
  UserStep,
  UserTaskExecution,
  UserStepConnection
} from '../entities';
import {
  UserTaskRepository,
  UserStepRepository,
  UserTaskExecutionRepository,
  UserStepConnectionRepository
} from '../repositories';
import {
  UserTaskController,
  UserStepController,
  UserConnectionController
} from './controllers';
import {
  UserTaskService,
  UserStepService,
  UserConnectionService
} from './services';

/**
 * Module quản lý task cho người dùng
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserTask,
      UserStep,
      UserTaskExecution,
      UserStepConnection
    ])
  ],
  controllers: [
    UserTaskController,
    UserStepController,
    UserConnectionController
  ],
  providers: [
    UserTaskRepository,
    UserStepRepository,
    UserTaskExecutionRepository,
    UserStepConnectionRepository,
    UserTaskService,
    UserStepService,
    UserConnectionService
  ],
  exports: [
    UserTaskService,
    UserStepService,
    UserConnectionService
  ],
})
export class TaskUserModule {}
