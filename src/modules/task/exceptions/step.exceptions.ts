import { ErrorCode } from '@/common';
import { HttpStatus } from '@nestjs/common';

/**
 * Mã lỗi cho các thao tác liên quan đến step
 * Phạm vi mã lỗi: 10100 - 10199
 */
export const STEP_ERROR_CODES = {
  // Lỗi chung
  STEP_NOT_FOUND: new ErrorCode(10100, 'Không tìm thấy bước', HttpStatus.NOT_FOUND),
  STEP_CREATION_FAILED: new ErrorCode(10101, 'Tạo bước thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  STEP_UPDATE_FAILED: new ErrorCode(10102, 'Cập nhật bước thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  STEP_DELETE_FAILED: new ErrorCode(10103, '<PERSON><PERSON><PERSON> bước thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  STEP_FETCH_FAILED: new ErrorCode(10104, '<PERSON><PERSON><PERSON> thông tin bước thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  
  // Lỗi xác thực và phân quyền
  STEP_UNAUTHORIZED: new ErrorCode(10110, 'Không có quyền truy cập bước này', HttpStatus.FORBIDDEN),
  
  // Lỗi trạng thái
  STEP_INVALID_TYPE: new ErrorCode(10120, 'Loại bước không hợp lệ', HttpStatus.BAD_REQUEST),
  STEP_TASK_COMPLETED: new ErrorCode(10121, 'Không thể thay đổi bước khi nhiệm vụ đã hoàn thành', HttpStatus.BAD_REQUEST),
  STEP_TASK_CANCELLED: new ErrorCode(10122, 'Không thể thay đổi bước khi nhiệm vụ đã bị hủy', HttpStatus.BAD_REQUEST),
  
  // Lỗi dữ liệu
  STEP_INVALID_DATA: new ErrorCode(10130, 'Dữ liệu bước không hợp lệ', HttpStatus.BAD_REQUEST),
  STEP_NAME_REQUIRED: new ErrorCode(10131, 'Tên bước là bắt buộc', HttpStatus.BAD_REQUEST),
  STEP_NAME_TOO_LONG: new ErrorCode(10132, 'Tên bước quá dài (tối đa 255 ký tự)', HttpStatus.BAD_REQUEST),
  STEP_CONFIG_INVALID: new ErrorCode(10133, 'Cấu hình bước không hợp lệ', HttpStatus.BAD_REQUEST),
  STEP_ORDER_INVALID: new ErrorCode(10134, 'Thứ tự bước không hợp lệ', HttpStatus.BAD_REQUEST),
  STEP_ORDER_DUPLICATE: new ErrorCode(10135, 'Thứ tự bước đã tồn tại', HttpStatus.BAD_REQUEST),
  
  // Lỗi giới hạn
  STEP_LIMIT_EXCEEDED: new ErrorCode(10140, 'Đã vượt quá giới hạn số lượng bước trong nhiệm vụ', HttpStatus.BAD_REQUEST),
  
  // Lỗi liên quan đến loại bước
  STEP_PROMPT_INVALID: new ErrorCode(10150, 'Cấu hình bước nhập liệu không hợp lệ', HttpStatus.BAD_REQUEST),
  STEP_TRIGGER_INVALID: new ErrorCode(10151, 'Cấu hình bước kích hoạt không hợp lệ', HttpStatus.BAD_REQUEST),
  STEP_ACTION_INVALID: new ErrorCode(10152, 'Cấu hình bước hành động không hợp lệ', HttpStatus.BAD_REQUEST),
  STEP_MEDIA_INVALID: new ErrorCode(10153, 'Cấu hình bước xử lý media không hợp lệ', HttpStatus.BAD_REQUEST),
  
  // Lỗi liên quan đến xác thực
  STEP_GOOGLE_AUTH_REQUIRED: new ErrorCode(10160, 'Cần xác thực Google để thực hiện bước này', HttpStatus.BAD_REQUEST),
  STEP_FACEBOOK_AUTH_REQUIRED: new ErrorCode(10161, 'Cần xác thực Facebook để thực hiện bước này', HttpStatus.BAD_REQUEST),
};
