/**
 * <PERSON><PERSON><PERSON> hằng số cho module Task
 */
export const TASK_CONSTANTS = {
  /**
   * Giới hạn số lượng task cho mỗi người dùng
   */
  MAX_TASKS_PER_USER: 100,
  
  /**
   * Giới hạn số lượng bước trong một task
   */
  MAX_STEPS_PER_TASK: 50,
  
  /**
   * Giới hạn số lượng kết nối trong một task
   */
  MAX_CONNECTIONS_PER_TASK: 100,
  
  /**
   * Giới hạn số lượng phiên thực thi cho mỗi task
   */
  MAX_EXECUTIONS_PER_TASK: 1000,
  
  /**
   * Thời gian timeout cho một bước thực thi (ms)
   */
  STEP_EXECUTION_TIMEOUT: 60000, // 1 phút
  
  /**
   * Thời gian tối đa cho một phiên thực thi (ms)
   */
  MAX_EXECUTION_TIME: 3600000, // 1 giờ
  
  /**
   * <PERSON><PERSON><PERSON><PERSON> hạn tần suất thực thi task (số lần/phút)
   */
  EXECUTION_RATE_LIMIT: 10,
};
