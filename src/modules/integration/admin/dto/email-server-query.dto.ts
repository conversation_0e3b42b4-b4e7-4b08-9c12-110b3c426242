import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@common/dto';

/**
 * DTO cho việc truy vấn danh sách cấu hình máy chủ email
 */
export class EmailServerQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Tìm kiếm theo tên máy chủ hoặc host',
    required: false,
    example: 'gmail',
  })
  @IsOptional()
  @IsString()
  search: string = '';

  @ApiProperty({
    description: 'Lọc theo ID người dùng',
    required: false,
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  userId?: number;
}
