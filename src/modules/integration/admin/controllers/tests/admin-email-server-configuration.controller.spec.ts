import { Test, TestingModule } from '@nestjs/testing';
import { AdminEmailServerConfigurationController } from '../admin-email-server-configuration.controller';
import { AdminEmailServerConfigurationService } from '../../services';
import { AdminEmailServerConfigurationEntity } from '@modules/integration/entities/admin_email_server_configurations.entity';
import { CreateEmailServerDto, TestEmailServerDto, UpdateEmailServerDto } from '../../../user/dto';
import { EmailServerQueryDto } from '../../dto';
import { PaginatedResult } from '@/common/response';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { AppException } from '@common/exceptions';
import { INTEGRATION_ERROR_CODES } from '../../../exceptions/integration-error.code';
import { HttpStatus } from '@nestjs/common';

describe('AdminEmailServerConfigurationController', () => {
  let controller: AdminEmailServerConfigurationController;
  let service: AdminEmailServerConfigurationService;

  // Mock data
  const mockEmailServer: AdminEmailServerConfigurationEntity = {
    id: 1,
    serverName: 'Test SMTP Server',
    host: 'smtp.test.com',
    port: 587,
    username: '<EMAIL>',
    password: '********',
    useSsl: true,
    additionalSettings: { auth: 'login' },
    createdBy: 1,
    createdAt: Date.now(),
    updatedAt: Date.now(),
  };

  const mockPaginatedResult: PaginatedResult<AdminEmailServerConfigurationEntity> = {
    items: [
      mockEmailServer,
      {
        ...mockEmailServer,
        id: 2,
        serverName: 'Another SMTP Server',
      },
    ],
    meta: {
      totalItems: 2,
      itemCount: 2,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  const mockCreateDto: CreateEmailServerDto = {
    serverName: 'New SMTP Server',
    host: 'smtp.new.com',
    port: 587,
    username: '<EMAIL>',
    password: 'new-password',
    useSsl: true,
    additionalSettings: { auth: 'login' },
  };

  const mockUpdateDto: UpdateEmailServerDto = {
    serverName: 'Updated SMTP Server',
    port: 465,
  };

  const mockTestDto: TestEmailServerDto = {
    recipientEmail: '<EMAIL>',
    subject: 'Test Email',
  };

  const mockEmployee: JwtPayload = {
    id: 1,
    sub: 1,
    isAdmin: true,
  };

  // Mock service
  const mockService = {
    findAll: jest.fn().mockResolvedValue(mockPaginatedResult),
    findOne: jest.fn().mockResolvedValue(mockEmailServer),
    create: jest.fn().mockResolvedValue(mockEmailServer),
    update: jest.fn().mockResolvedValue(mockEmailServer),
    remove: jest.fn().mockResolvedValue({ message: 'Cấu hình máy chủ email đã được xóa' }),
    testConnection: jest.fn().mockResolvedValue({
      success: true,
      message: 'Kết nối thành công! Email kiểm tra đã được gửi.',
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminEmailServerConfigurationController],
      providers: [
        {
          provide: AdminEmailServerConfigurationService,
          useValue: mockService,
        },
      ],
    }).compile();

    controller = module.get<AdminEmailServerConfigurationController>(AdminEmailServerConfigurationController);
    service = module.get<AdminEmailServerConfigurationService>(AdminEmailServerConfigurationService);

    // Reset mocks
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    it('should return a paginated list of email servers', async () => {
      const queryDto: EmailServerQueryDto = {
        page: 1,
        limit: 10,
      };

      const result = await controller.findAll(queryDto, mockEmployee);

      expect(result).toBeDefined();
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Lấy danh sách cấu hình máy chủ email thành công');
      expect(result.result).toEqual(mockPaginatedResult);
      expect(service.findAll).toHaveBeenCalledWith(queryDto);
    });

    it('should handle service errors', async () => {
      const queryDto: EmailServerQueryDto = {
        page: 1,
        limit: 10,
      };

      const error = new AppException(INTEGRATION_ERROR_CODES.EMAIL_SERVER_LIST_FAILED);
      mockService.findAll.mockRejectedValueOnce(error);

      await expect(controller.findAll(queryDto, mockEmployee)).rejects.toThrow(AppException);
    });
  });

  describe('findOne', () => {
    it('should return an email server by id', async () => {
      const result = await controller.findOne(1, mockEmployee);

      expect(result).toBeDefined();
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Lấy thông tin chi tiết cấu hình máy chủ email thành công');
      expect(result.result).toEqual(mockEmailServer);
      expect(service.findOne).toHaveBeenCalledWith(1);
    });

    it('should handle service errors', async () => {
      const error = new AppException(INTEGRATION_ERROR_CODES.EMAIL_SERVER_NOT_FOUND);
      mockService.findOne.mockRejectedValueOnce(error);

      await expect(controller.findOne(999, mockEmployee)).rejects.toThrow(AppException);
    });
  });

  describe('create', () => {
    it('should create a new email server', async () => {
      const result = await controller.create(mockCreateDto, mockEmployee);

      expect(result).toBeDefined();
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Tạo mới cấu hình máy chủ email thành công');
      expect(result.result).toEqual(mockEmailServer);
      expect(service.create).toHaveBeenCalledWith(mockCreateDto, mockEmployee.id);
    });

    it('should handle service errors', async () => {
      const error = new AppException(INTEGRATION_ERROR_CODES.EMAIL_SERVER_CREATE_FAILED);
      mockService.create.mockRejectedValueOnce(error);

      await expect(controller.create(mockCreateDto, mockEmployee)).rejects.toThrow(AppException);
    });
  });

  describe('update', () => {
    it('should update an existing email server', async () => {
      const result = await controller.update(1, mockUpdateDto, mockEmployee);

      expect(result).toBeDefined();
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Cập nhật cấu hình máy chủ email thành công');
      expect(result.result).toEqual(mockEmailServer);
      expect(service.update).toHaveBeenCalledWith(1, mockUpdateDto, mockEmployee.id);
    });

    it('should handle service errors', async () => {
      const error = new AppException(INTEGRATION_ERROR_CODES.EMAIL_SERVER_UPDATE_FAILED);
      mockService.update.mockRejectedValueOnce(error);

      await expect(controller.update(1, mockUpdateDto, mockEmployee)).rejects.toThrow(AppException);
    });
  });

  describe('remove', () => {
    it('should remove an email server', async () => {
      const result = await controller.remove(1, mockEmployee);

      expect(result).toBeDefined();
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Xóa cấu hình máy chủ email thành công');
      expect(result.result).toEqual({ message: 'Cấu hình máy chủ email đã được xóa' });
      expect(service.remove).toHaveBeenCalledWith(1, mockEmployee.id);
    });

    it('should handle service errors', async () => {
      const error = new AppException(INTEGRATION_ERROR_CODES.EMAIL_SERVER_DELETE_FAILED);
      mockService.remove.mockRejectedValueOnce(error);

      await expect(controller.remove(1, mockEmployee)).rejects.toThrow(AppException);
    });
  });

  describe('testConnection', () => {
    it('should test email server connection', async () => {
      const result = await controller.testConnection(1, mockTestDto, mockEmployee);

      expect(result).toBeDefined();
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Kiểm tra kết nối máy chủ email thành công');
      expect(result.result).toEqual({
        success: true,
        message: 'Kết nối thành công! Email kiểm tra đã được gửi.',
      });
      expect(service.testConnection).toHaveBeenCalledWith(1, mockTestDto);
    });

    it('should handle service errors', async () => {
      const error = new AppException(INTEGRATION_ERROR_CODES.EMAIL_SERVER_TEST_FAILED);
      mockService.testConnection.mockRejectedValueOnce(error);

      await expect(controller.testConnection(1, mockTestDto, mockEmployee)).rejects.toThrow(AppException);
    });
  });
});
