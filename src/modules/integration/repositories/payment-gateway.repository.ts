import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { PaymentGateway } from '@modules/integration/entities';

@Injectable()
export class PaymentGatewayRepository extends Repository<PaymentGateway> {
  constructor(private dataSource: DataSource) {
    super(PaymentGateway, dataSource.createEntityManager());
  }

  /**
   * Lấy danh sách tài khoản ngân hàng đủ điều kiện tạo tài khoản VA
   * @param userId ID của người dùng
   * @returns Danh sách tài khoản ngân hàng đủ điều kiện
   */
  async getEligibleVAAccounts(userId: number): Promise<PaymentGateway[]> {
    return this.createQueryBuilder('payment_gateway')
      .where('payment_gateway.companyId IN (SELECT companyId FROM user_company_in_sepay WHERE userId = :userId)', { userId })
      .andWhere('payment_gateway.bankCode = :bankCode', { bankCode: 'OCB' })
      .andWhere('payment_gateway.status IN (:...statuses)', { statuses: ['ACTIVE', 'DA_XAC_THUC'] })
      .andWhere('payment_gateway.isVa = :isVa', { isVa: false })
      .getMany();
  }

  /**
   * Lấy danh sách tài khoản ngân hàng đủ điều kiện liên kết với chatbot
   * @param userId ID của người dùng
   * @param status Trạng thái tài khoản ngân hàng
   * @returns Danh sách tài khoản ngân hàng đủ điều kiện
   */
  async findActiveByUserId(userId: number, status: string): Promise<PaymentGateway[]> {
    return this.createQueryBuilder('payment_gateway')
      .where('payment_gateway.companyId IN (SELECT companyId FROM user_company_in_sepay WHERE userId = :userId)', { userId })
      .andWhere('payment_gateway.status = :status', { status })
      .getMany();
  }
}
