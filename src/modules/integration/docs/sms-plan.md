# Kế Hoạch Tí<PERSON> SMS và SMS Brandname

## 1. Tổng Quan

Dự án này nhằm xây dựng một công cụ tích hợp các nhà cung cấp dịch vụ SMS và SMS brandname, cho phép người dùng cấu hình, quản lý và sử dụng các dịch vụ SMS từ nhiều nhà cung cấp khác nhau thông qua một giao diện thống nhất.

### 1.1. Hiện Trạng Hệ Thống

Hệ thống hiện tại đã có:
- Entity `SmsServerConfiguration` để lưu trữ cấu hình nhà cung cấp SMS
- Controller và service cơ bản cho cấu hình máy chủ SMS (cả user và admin)
- Các interface SMS trong `src/modules/sms/interfaces/sms.interface.ts` định nghĩa các cấu trúc request/response liên quan đến SMS
- <PERSON><PERSON> thống template cho email có thể được điều chỉnh cho SMS

### 1.2. <PERSON><PERSON><PERSON> Tiêu

- Tích hợp với nhiều nhà cung cấp dịch vụ SMS phổ biến tại Việt Nam và quốc tế
- Hỗ trợ đăng ký và quản lý SMS brandname
- Xây dựng hệ thống template SMS linh hoạt
- Cung cấp tính năng quản lý chiến dịch SMS
- Đảm bảo tuân thủ các quy định về gửi SMS tại Việt Nam

## 2. Kế Hoạch Triển Khai Chi Tiết

### 2.1. Tích Hợp Nhà Cung Cấp SMS

#### 2.1.1. Định Nghĩa Interface và DTO

- Tạo các DTO cho việc tạo, cập nhật và kiểm tra cấu hình máy chủ SMS
  - `CreateSmsServerDto`
  - `UpdateSmsServerDto`
  - `TestSmsServerDto`
  - `SmsServerResponseDto`

- Định nghĩa interface cho các phản hồi từ nhà cung cấp SMS
  - `SmsProviderResponse`
  - `SmsDeliveryStatus`

- Tạo enum cho các loại nhà cung cấp SMS
  - `SmsProviderEnum` (Twilio, Vonage, Viettel, FPT, SpeedSMS, ...)

#### 2.1.2. Triển Khai Service Cấu Hình Máy Chủ SMS

- Hoàn thiện `SmsServerConfigurationUserService` để xử lý các thao tác CRUD
  - `create(createSmsServerDto, userId)`
  - `findAll(userId)`
  - `findOne(id, userId)`
  - `update(id, updateSmsServerDto, userId)`
  - `remove(id, userId)`

- Triển khai phương thức kiểm tra kết nối đến nhà cung cấp SMS
  - `testConnection(id, testSmsServerDto, userId)`

- Thêm mã hóa cho dữ liệu nhạy cảm (API key, token)
  - Sử dụng service mã hóa hiện có trong hệ thống

#### 2.1.3. Triển Khai Controller Cấu Hình Máy Chủ SMS

- Thêm endpoint cho việc tạo, cập nhật, xóa và liệt kê cấu hình SMS
  - `POST /integration/sms-server`
  - `GET /integration/sms-server`
  - `GET /integration/sms-server/:id`
  - `PATCH /integration/sms-server/:id`
  - `DELETE /integration/sms-server/:id`

- Thêm endpoint kiểm tra kết nối nhà cung cấp SMS
  - `POST /integration/sms-server/:id/test`

- Triển khai xác thực và validation phù hợp
  - Sử dụng `JwtUserGuard` cho người dùng
  - Sử dụng `JwtEmployeeGuard` và `RolesGuard` cho admin

#### 2.1.4. Tạo SMS Service

- Triển khai service để gửi tin nhắn SMS qua các nhà cung cấp khác nhau
  - `sendSms(phoneNumber, message, configId)`
  - `sendBulkSms(phoneNumbers, message, configId)`
  - `checkDeliveryStatus(messageId, configId)`

- Tạo các adapter cho từng nhà cung cấp SMS
  - `TwilioProviderService`
  - `VonageProviderService`
  - `ViettelProviderService`
  - `FptProviderService`
  - `SpeedSmsProviderService`

- Triển khai xử lý lỗi và ghi log
  - Sử dụng `Logger` của NestJS
  - Định nghĩa các loại lỗi cụ thể cho SMS

### 2.2. Tích Hợp SMS Brandname

#### 2.2.1. Định Nghĩa Entity và DTO

- Tạo entity để lưu trữ cấu hình SMS brandname
  - `SmsBrandname`

- Tạo các DTO cho các thao tác với brandname
  - `CreateSmsBrandnameDto`
  - `UpdateSmsBrandnameDto`
  - `SmsBrandnameResponseDto`

- Định nghĩa interface cho các phản hồi từ brandname
  - `BrandnameRegistrationResponse`
  - `BrandnameStatusResponse`

#### 2.2.2. Triển Khai Service SMS Brandname

- Tạo service để quản lý SMS brandname
  - `SmsBrandnameUserService`
  - `SmsBrandnameAdminService`

- Triển khai các phương thức đăng ký, cập nhật và xác minh brandname
  - `register(createSmsBrandnameDto, userId)`
  - `update(id, updateSmsBrandnameDto, userId)`
  - `verify(id, verificationData, userId)`
  - `checkStatus(id, userId)`

- Thêm hỗ trợ cho các yêu cầu brandname cụ thể của từng nhà cung cấp
  - Xử lý tài liệu xác minh
  - Quản lý quy trình phê duyệt

#### 2.2.3. Triển Khai Controller SMS Brandname

- Thêm endpoint cho quản lý brandname
  - `POST /integration/sms-brandname`
  - `GET /integration/sms-brandname`
  - `GET /integration/sms-brandname/:id`
  - `PATCH /integration/sms-brandname/:id`
  - `DELETE /integration/sms-brandname/:id`
  - `POST /integration/sms-brandname/:id/verify`
  - `GET /integration/sms-brandname/:id/status`

- Triển khai xác thực và validation phù hợp

### 2.3. Hệ Thống Template SMS

#### 2.3.1. Nâng Cao Hệ Thống Template SMS

- Hoàn thiện triển khai entity `AdminTemplateSms`
  - Thêm các trường cần thiết
  - Cập nhật repository

- Tạo service để quản lý template SMS
  - `AdminTemplateSmsService`
  - `TemplateSmsService`

- Triển khai hệ thống placeholder tương tự như template email
  - Định nghĩa các placeholder phổ biến cho SMS
  - Tạo service để chèn giá trị vào placeholder

#### 2.3.2. Tạo Service Gửi SMS

- Triển khai service để gửi SMS với template
  - `SendWithTemplateSmsService`

- Thêm hỗ trợ cho các loại tin nhắn khác nhau
  - OTP/Xác minh
  - Marketing
  - Thông báo

- Triển khai giới hạn tốc độ và quản lý hạn ngạch
  - Theo dõi số lượng SMS đã gửi
  - Áp dụng giới hạn theo cấu hình

### 2.4. Quản Lý Chiến Dịch SMS

#### 2.4.1. Triển Khai Entity và DTO Chiến Dịch

- Tạo entity cho chiến dịch SMS
  - `SmsCampaign`
  - `SmsCampaignRecipient`
  - `SmsCampaignResult`

- Định nghĩa DTO cho các thao tác chiến dịch
  - `CreateSmsCampaignDto`
  - `UpdateSmsCampaignDto`
  - `SmsCampaignResponseDto`
  - `SmsCampaignResultDto`

- Tạo interface cho các phản hồi chiến dịch
  - `CampaignStatusResponse`
  - `CampaignAnalyticsResponse`

#### 2.4.2. Triển Khai Service Chiến Dịch

- Tạo service để quản lý chiến dịch SMS
  - `SmsCampaignService`

- Thêm hỗ trợ cho lập lịch, nhắm mục tiêu và báo cáo
  - `scheduleCampaign(campaignId, scheduleData)`
  - `executeCampaign(campaignId)`
  - `pauseCampaign(campaignId)`
  - `resumeCampaign(campaignId)`
  - `cancelCampaign(campaignId)`

- Triển khai phân tích chiến dịch
  - `getCampaignStats(campaignId)`
  - `getCampaignDeliveryReport(campaignId)`

#### 2.4.3. Triển Khai Controller Chiến Dịch

- Thêm endpoint cho quản lý chiến dịch
  - `POST /sms/campaign`
  - `GET /sms/campaign`
  - `GET /sms/campaign/:id`
  - `PATCH /sms/campaign/:id`
  - `DELETE /sms/campaign/:id`
  - `POST /sms/campaign/:id/schedule`
  - `POST /sms/campaign/:id/execute`
  - `POST /sms/campaign/:id/pause`
  - `POST /sms/campaign/:id/resume`
  - `POST /sms/campaign/:id/cancel`
  - `GET /sms/campaign/:id/stats`
  - `GET /sms/campaign/:id/delivery-report`

- Triển khai xác thực và validation phù hợp

## 3. Cấu Trúc Thư Mục

```
src/modules/integration/
├── entities/
│   ├── sms-server-configuration.entity.ts (đã có)
│   └── sms-brandname.entity.ts (mới)
├── user/
│   ├── controllers/
│   │   ├── sms-server-configuration-user.controller.ts (cập nhật)
│   │   └── sms-brandname-user.controller.ts (mới)
│   ├── services/
│   │   ├── sms-server-configuration-user.service.ts (cập nhật)
│   │   └── sms-brandname-user.service.ts (mới)
│   └── dto/
│       ├── create-sms-server.dto.ts (mới)
│       ├── update-sms-server.dto.ts (mới)
│       ├── test-sms-server.dto.ts (mới)
│       ├── sms-server-response.dto.ts (mới)
│       ├── create-sms-brandname.dto.ts (mới)
│       └── update-sms-brandname.dto.ts (mới)
├── admin/
│   ├── controllers/
│   │   ├── sms-server-configuration-admin.controller.ts (cập nhật)
│   │   └── sms-brandname-admin.controller.ts (mới)
│   ├── services/
│   │   ├── sms-server-configuration-admin.service.ts (cập nhật)
│   │   └── sms-brandname-admin.service.ts (mới)
│   └── dto/ (tương tự như user DTOs)
└── repositories/
    ├── sms-server-configuration.repository.ts (đã có)
    └── sms-brandname.repository.ts (mới)

src/modules/sms/ (module mới)
├── sms.module.ts
├── controllers/
│   ├── sms.controller.ts
│   └── sms-campaign.controller.ts
├── services/
│   ├── sms.service.ts
│   ├── sms-template.service.ts
│   ├── sms-campaign.service.ts
│   └── providers/ (adapter cho các nhà cung cấp SMS khác nhau)
│       ├── sms-provider.interface.ts
│       ├── twilio-provider.service.ts
│       ├── vonage-provider.service.ts
│       ├── viettel-provider.service.ts
│       ├── fpt-provider.service.ts
│       └── speedsms-provider.service.ts
├── dto/
│   ├── send-sms.dto.ts
│   ├── send-bulk-sms.dto.ts
│   ├── create-campaign.dto.ts
│   └── campaign-response.dto.ts
└── interfaces/
    ├── sms.interface.ts (đã có)
    └── sms-campaign.interface.ts (mới)
```

## 4. Chi Tiết Triển Khai

### 4.1. Tích Hợp Nhà Cung Cấp SMS

#### 4.1.1. Các Loại Nhà Cung Cấp SMS

- **Quốc tế:**
  - Twilio
  - Vonage (Nexmo)
  - MessageBird
  - Infobip

- **Việt Nam:**
  - Viettel SMS
  - FPT SMS
  - SpeedSMS
  - VMG
  - Vinaphone SMS
  - Mobifone SMS

#### 4.1.2. Phương Thức Xác Thực

- API Key/Secret
- OAuth 2.0
- Username/Password
- Token-based

#### 4.1.3. Tính Năng Cần Triển Khai

- Gửi SMS đơn lẻ
- Gửi SMS hàng loạt
- SMS theo lịch
- Theo dõi trạng thái gửi SMS
- Xử lý lỗi và cơ chế thử lại

### 4.2. Tích Hợp SMS Brandname

#### 4.2.1. Quy Trình Đăng Ký Brandname

- Nộp đơn đăng ký
- Xác minh tài liệu
- Quy trình phê duyệt
- Kích hoạt

#### 4.2.2. Các Loại Brandname

- Quảng cáo
- Dịch vụ khách hàng
- OTP/Xác minh

#### 4.2.3. Tính Năng Tuân Thủ

- Lọc nội dung
- Hạn chế lịch trình
- Giới hạn tốc độ
- Quản lý danh sách đen

### 4.3. Hệ Thống Template SMS

#### 4.3.1. Các Loại Template

- OTP/Xác minh
- Marketing
- Giao dịch
- Thông báo

#### 4.3.2. Tính Năng Template

- Hệ thống placeholder
- Đếm ký tự
- Chức năng xem trước
- Kiểm soát phiên bản

### 4.4. Quản Lý Chiến Dịch SMS

#### 4.4.1. Tính Năng Chiến Dịch

- Lựa chọn đối tượng mục tiêu
- Lập lịch
- Kiểm tra A/B
- Phân tích hiệu suất
- Theo dõi chi phí

#### 4.4.2. Tính Năng Báo Cáo

- Tỷ lệ gửi thành công
- Tỷ lệ mở (đối với các nhà cung cấp hỗ trợ)
- Theo dõi chuyển đổi
- Phân tích chi phí

## 5. Lịch Trình Triển Khai

### 5.1. Giai Đoạn 1: Tích Hợp Cơ Bản Nhà Cung Cấp SMS (Tuần 1-2)

- Triển khai tích hợp nhà cung cấp SMS cơ bản
- Tạo DTO và service
- Triển khai các thao tác CRUD cho cấu hình SMS

### 5.2. Giai Đoạn 2: Chức Năng Gửi SMS (Tuần 2-3)

- Triển khai service gửi SMS
- Tạo adapter cho nhà cung cấp
- Thêm hỗ trợ cho template

### 5.3. Giai Đoạn 3: Tích Hợp SMS Brandname (Tuần 3-4)

- Triển khai entity và service brandname
- Tạo luồng đăng ký và xác minh
- Thêm tính năng tuân thủ

### 5.4. Giai Đoạn 4: Quản Lý Chiến Dịch (Tuần 4-5)

- Triển khai entity và service chiến dịch
- Tạo tính năng lập lịch và nhắm mục tiêu
- Thêm báo cáo và phân tích

### 5.5. Giai Đoạn 5: Kiểm Thử và Tối Ưu Hóa (Tuần 5-6)

- Kiểm thử toàn diện
- Tối ưu hóa hiệu suất
- Tài liệu hóa

## 6. Chiến Lược Kiểm Thử

### 6.1. Kiểm Thử Đơn Vị

- Kiểm thử các service và component riêng lẻ
- Mock API của nhà cung cấp SMS bên ngoài

### 6.2. Kiểm Thử Tích Hợp

- Kiểm thử tương tác giữa các component
- Xác minh các thao tác cơ sở dữ liệu

### 6.3. Kiểm Thử End-to-End

- Kiểm thử luồng công việc hoàn chỉnh
- Xác minh việc gửi SMS (sử dụng tài khoản test)

### 6.4. Kiểm Thử Hiệu Suất

- Kiểm thử gửi SMS hàng loạt
- Xác minh giới hạn tốc độ
- Kiểm thử các thao tác đồng thời

## 7. Các Vấn Đề Cần Lưu Ý

### 7.1. Bảo Mật

- Mã hóa thông tin xác thực nhà cung cấp SMS
- Triển khai kiểm soát truy cập dựa trên vai trò
- Bảo vệ thông tin cá nhân của người nhận SMS

### 7.2. Tuân Thủ Quy Định

- Tuân thủ quy định về SMS marketing tại Việt Nam
- Triển khai cơ chế opt-out
- Quản lý thời gian gửi SMS theo quy định

### 7.3. Khả Năng Mở Rộng

- Thiết kế hệ thống có thể mở rộng để hỗ trợ nhiều nhà cung cấp SMS
- Chuẩn bị cho việc tăng khối lượng SMS
- Tối ưu hóa hiệu suất cho các chiến dịch lớn

### 7.4. Xử Lý Lỗi

- Triển khai cơ chế thử lại thông minh
- Ghi log chi tiết cho các lỗi
- Thông báo cho người dùng về các vấn đề gửi SMS

## 8. Kết Luận

Kế hoạch tích hợp SMS và SMS brandname này cung cấp một lộ trình toàn diện để xây dựng một hệ thống mạnh mẽ, linh hoạt và tuân thủ quy định. Bằng cách tuân theo các giai đoạn triển khai được nêu, chúng ta có thể tạo ra một giải pháp SMS tích hợp đáp ứng nhu cầu của người dùng và doanh nghiệp tại Việt Nam.