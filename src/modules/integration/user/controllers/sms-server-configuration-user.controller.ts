import { Controller, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { SmsServerConfigurationUserService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags';

@ApiTags(SWAGGER_API_TAGS.INTEGRATION)
@Controller('integration/sms-server')
@UseGuards(JwtUserGuard)
@ApiBearerAuth("JWT-auth")
export class SmsServerConfigurationUserController {
  constructor(
    private readonly smsServerConfigurationUserService: SmsServerConfigurationUserService,
  ) {}
}
