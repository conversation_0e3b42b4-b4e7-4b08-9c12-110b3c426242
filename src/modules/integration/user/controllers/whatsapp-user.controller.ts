import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { WhatsAppUserService } from '../services/whatsapp-user.service';
import {
  WhatsAppAccountQueryDto,
  CreateWhatsAppAccountDto,
  UpdateWhatsAppAccountDto,
  WhatsAppAccountResponseDto,
  ConnectWhatsAppAgentDto,
  CreateWhatsAppTemplateDto,
  WhatsAppTemplateResponseDto,
  SendWhatsAppMessageDto,
  WhatsAppMessageResponseDto,
} from '../dto/whatsapp';

/**
 * Controller quản lý tích hợp WhatsApp cho người dùng
 */
@ApiTags(SWAGGER_API_TAGS.INTEGRATION)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@Controller('integration/whatsapp')
export class WhatsAppUserController {
  constructor(private readonly whatsappUserService: WhatsAppUserService) {}

  /**
   * Lấy danh sách tài khoản WhatsApp
   */
  @Get('accounts')
  @ApiOperation({ summary: 'Lấy danh sách tài khoản WhatsApp' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách tài khoản WhatsApp',
    type: ApiResponseDto,
  })
  async findWhatsAppAccounts(
    @CurrentUser() user: JwtPayload,
    @Query() query: WhatsAppAccountQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<WhatsAppAccountResponseDto>>> {
    const accounts = await this.whatsappUserService.findWhatsAppAccounts(user.id, query);
    return ApiResponseDto.success(accounts, 'Lấy danh sách tài khoản WhatsApp thành công');
  }

  /**
   * Lấy thông tin tài khoản WhatsApp theo ID
   */
  @Get('accounts/:id')
  @ApiOperation({ summary: 'Lấy thông tin tài khoản WhatsApp theo ID' })
  @ApiParam({ name: 'id', description: 'ID của tài khoản WhatsApp' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thông tin tài khoản WhatsApp',
    type: ApiResponseDto,
  })
  async findWhatsAppAccountById(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<WhatsAppAccountResponseDto>> {
    const account = await this.whatsappUserService.findWhatsAppAccountById(user.id, id);
    return ApiResponseDto.success(account, 'Lấy thông tin tài khoản WhatsApp thành công');
  }

  /**
   * Tạo mới tài khoản WhatsApp
   */
  @Post('accounts')
  @ApiOperation({ summary: 'Tạo mới tài khoản WhatsApp' })
  @ApiBody({ type: CreateWhatsAppAccountDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tài khoản WhatsApp đã được tạo',
    type: ApiResponseDto,
  })
  async createWhatsAppAccount(
    @CurrentUser() user: JwtPayload,
    @Body() createDto: CreateWhatsAppAccountDto,
  ): Promise<ApiResponseDto<WhatsAppAccountResponseDto>> {
    const account = await this.whatsappUserService.createWhatsAppAccount(user.id, createDto);
    return ApiResponseDto.created(account, 'Tạo tài khoản WhatsApp thành công');
  }

  /**
   * Cập nhật tài khoản WhatsApp
   */
  @Put('accounts/:id')
  @ApiOperation({ summary: 'Cập nhật tài khoản WhatsApp' })
  @ApiParam({ name: 'id', description: 'ID của tài khoản WhatsApp' })
  @ApiBody({ type: UpdateWhatsAppAccountDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tài khoản WhatsApp đã được cập nhật',
    type: ApiResponseDto,
  })
  async updateWhatsAppAccount(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateWhatsAppAccountDto,
  ): Promise<ApiResponseDto<WhatsAppAccountResponseDto | null>> {
    const account = await this.whatsappUserService.updateWhatsAppAccount(user.id, id, updateDto);
    return ApiResponseDto.success(account, 'Cập nhật tài khoản WhatsApp thành công');
  }

  /**
   * Xóa tài khoản WhatsApp
   */
  @Delete('accounts/:id')
  @ApiOperation({ summary: 'Xóa tài khoản WhatsApp' })
  @ApiParam({ name: 'id', description: 'ID của tài khoản WhatsApp' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tài khoản WhatsApp đã được xóa',
    type: ApiResponseDto,
  })
  async deleteWhatsAppAccount(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<null>> {
    await this.whatsappUserService.deleteWhatsAppAccount(user.id, id);
    return ApiResponseDto.success(null, 'Xóa tài khoản WhatsApp thành công');
  }

  /**
   * Kết nối tài khoản WhatsApp với agent
   */
  @Post('accounts/:id/connect-agent')
  @ApiOperation({ summary: 'Kết nối tài khoản WhatsApp với agent' })
  @ApiParam({ name: 'id', description: 'ID của tài khoản WhatsApp' })
  @ApiBody({ type: ConnectWhatsAppAgentDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tài khoản WhatsApp đã được kết nối với agent',
    type: ApiResponseDto,
  })
  async connectAgent(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() connectDto: ConnectWhatsAppAgentDto,
  ): Promise<ApiResponseDto<WhatsAppAccountResponseDto | null>> {
    const account = await this.whatsappUserService.connectAgent(user.id, id, connectDto);
    return ApiResponseDto.success(account, 'Kết nối tài khoản WhatsApp với agent thành công');
  }

  /**
   * Ngắt kết nối tài khoản WhatsApp với agent
   */
  @Delete('accounts/:id/disconnect-agent')
  @ApiOperation({ summary: 'Ngắt kết nối tài khoản WhatsApp với agent' })
  @ApiParam({ name: 'id', description: 'ID của tài khoản WhatsApp' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tài khoản WhatsApp đã được ngắt kết nối với agent',
    type: ApiResponseDto,
  })
  async disconnectAgent(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<WhatsAppAccountResponseDto | null>> {
    const account = await this.whatsappUserService.disconnectAgent(user.id, id);
    return ApiResponseDto.success(account, 'Ngắt kết nối tài khoản WhatsApp với agent thành công');
  }

  /**
   * Gửi tin nhắn WhatsApp
   */
  @Post('accounts/:id/send')
  @ApiOperation({ summary: 'Gửi tin nhắn WhatsApp' })
  @ApiParam({ name: 'id', description: 'ID của tài khoản WhatsApp' })
  @ApiBody({ type: SendWhatsAppMessageDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Tin nhắn WhatsApp đã được gửi',
    type: ApiResponseDto,
  })
  async sendMessage(
    @CurrentUser() user: JwtPayload,
    @Param('id', ParseIntPipe) id: number,
    @Body() sendDto: SendWhatsAppMessageDto,
  ): Promise<ApiResponseDto<WhatsAppMessageResponseDto>> {
    const message = await this.whatsappUserService.sendMessage(user.id, id, sendDto);
    return ApiResponseDto.success(message, 'Gửi tin nhắn WhatsApp thành công');
  }
}
