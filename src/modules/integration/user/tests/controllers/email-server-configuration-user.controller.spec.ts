import { Test, TestingModule } from '@nestjs/testing';
import { EmailServerConfigurationUserController } from '../../controllers/email-server-configuration-user.controller';
import { EmailServerConfigurationUserService } from '../../services/email-server-configuration-user.service';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { CreateEmailServerDto, TestEmailServerDto, TestEmailServerWithConfigDto, UpdateEmailServerDto } from '../../dto';
import { EmailServerConfiguration } from '@modules/integration/entities';
import { HttpStatus } from '@nestjs/common';

describe('EmailServerConfigurationUserController', () => {
  let controller: EmailServerConfigurationUserController;
  let service: EmailServerConfigurationUserService;

  // Mock data
  const mockUser: JwtPayload = {
    id: 1,
    email: '<EMAIL>',
    username: 'testuser',
    iat: Date.now(),
    exp: Date.now() + 3600,
  };

  const mockEmailServer: EmailServerConfiguration = {
    id: 1,
    userId: 1,
    serverName: 'Test SMTP Server',
    host: 'smtp.test.com',
    port: 587,
    username: '<EMAIL>',
    password: '********',
    useSsl: true,
    additionalSettings: { auth: 'login' },
    createdAt: Date.now(),
    updatedAt: Date.now(),
  };

  const mockCreateDto: CreateEmailServerDto = {
    serverName: 'Test SMTP Server',
    host: 'smtp.test.com',
    port: 587,
    username: '<EMAIL>',
    password: 'test-password',
    useSsl: true,
    additionalSettings: { auth: 'login' },
  };

  const mockUpdateDto: UpdateEmailServerDto = {
    serverName: 'Updated Test SMTP Server',
    port: 465,
  };

  const mockTestDto: TestEmailServerDto = {
    recipientEmail: '<EMAIL>',
    subject: 'Test Email',
  };

  const mockTestWithConfigDto: TestEmailServerWithConfigDto = {
    emailServerConfig: {
      serverName: 'Test SMTP Server',
      host: 'smtp.test.com',
      port: 587,
      username: '<EMAIL>',
      password: 'test-password',
      useSsl: true,
      additionalSettings: { auth: 'login' },
    },
    testInfo: {
      recipientEmail: '<EMAIL>',
      subject: 'Test Email with Config',
    },
  };

  const mockTestResult = {
    success: true,
    message: 'Kết nối thành công! Email kiểm tra đã được gửi.',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EmailServerConfigurationUserController],
      providers: [
        {
          provide: EmailServerConfigurationUserService,
          useValue: {
            create: jest.fn().mockResolvedValue(mockEmailServer),
            findAll: jest.fn().mockResolvedValue([mockEmailServer]),
            findOne: jest.fn().mockResolvedValue(mockEmailServer),
            update: jest.fn().mockResolvedValue(mockEmailServer),
            remove: jest.fn().mockResolvedValue(undefined),
            testConnection: jest.fn().mockResolvedValue(mockTestResult),
            testConnectionWithConfig: jest.fn().mockResolvedValue(mockTestResult),
          },
        },
      ],
    }).compile();

    controller = module.get<EmailServerConfigurationUserController>(EmailServerConfigurationUserController);
    service = module.get<EmailServerConfigurationUserService>(EmailServerConfigurationUserService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new email server configuration', async () => {
      const result = await controller.create(mockCreateDto, mockUser);

      expect(result).toBeDefined();
      expect(result.code).toBe(HttpStatus.CREATED);
      expect(result.message).toBe('Tạo cấu hình máy chủ email thành công');
      expect(result.result).toEqual(mockEmailServer);
      expect(service.create).toHaveBeenCalledWith(mockCreateDto, mockUser.id);
    });
  });

  describe('findAll', () => {
    it('should return all email server configurations', async () => {
      const result = await controller.findAll(mockUser);

      expect(result).toBeDefined();
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Lấy danh sách cấu hình máy chủ email thành công');
      expect(result.result).toEqual([mockEmailServer]);
      expect(service.findAll).toHaveBeenCalledWith(mockUser.id);
    });
  });

  describe('findOne', () => {
    it('should return a specific email server configuration', async () => {
      const result = await controller.findOne(1, mockUser);

      expect(result).toBeDefined();
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Lấy thông tin cấu hình máy chủ email thành công');
      expect(result.result).toEqual(mockEmailServer);
      expect(service.findOne).toHaveBeenCalledWith(1, mockUser.id);
    });
  });

  describe('update', () => {
    it('should update an email server configuration', async () => {
      const result = await controller.update(1, mockUpdateDto, mockUser);

      expect(result).toBeDefined();
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Cập nhật cấu hình máy chủ email thành công');
      expect(result.result).toEqual(mockEmailServer);
      expect(service.update).toHaveBeenCalledWith(1, mockUpdateDto, mockUser.id);
    });
  });

  describe('remove', () => {
    it('should delete an email server configuration', async () => {
      const result = await controller.remove(1, mockUser);

      expect(result).toBeDefined();
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Xóa cấu hình máy chủ email thành công');
      expect(service.remove).toHaveBeenCalledWith(1, mockUser.id);
    });
  });

  describe('testConnection', () => {
    it('should test email server connection with existing configuration', async () => {
      const result = await controller.testConnection(1, mockTestDto, mockUser);

      expect(result).toBeDefined();
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Kiểm tra kết nối máy chủ email thành công');
      expect(result.result).toEqual(mockTestResult);
      expect(service.testConnection).toHaveBeenCalledWith(1, mockTestDto, mockUser.id);
    });
  });

  describe('testConnectionWithConfig', () => {
    it('should test email server connection with direct configuration', async () => {
      const result = await controller.testConnectionWithConfig(mockTestWithConfigDto, mockUser);

      expect(result).toBeDefined();
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Kiểm tra kết nối máy chủ email thành công');
      expect(result.result).toEqual(mockTestResult);
      expect(service.testConnectionWithConfig).toHaveBeenCalledWith(mockTestWithConfigDto);
    });

    it('should handle test connection failure with direct configuration', async () => {
      const failureResult = {
        success: false,
        message: 'Kết nối thất bại!',
        details: 'Connection timeout',
      };

      jest.spyOn(service, 'testConnectionWithConfig').mockResolvedValueOnce(failureResult);

      const result = await controller.testConnectionWithConfig(mockTestWithConfigDto, mockUser);

      expect(result).toBeDefined();
      expect(result.code).toBe(HttpStatus.OK);
      expect(result.message).toBe('Kiểm tra kết nối máy chủ email thành công');
      expect(result.result).toEqual(failureResult);
      expect(service.testConnectionWithConfig).toHaveBeenCalledWith(mockTestWithConfigDto);
    });
  });
});
