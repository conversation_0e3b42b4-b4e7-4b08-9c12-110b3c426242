# Integration User Module Tests

Th<PERSON> mục này chứa các test cho Integration User Module, bao gồm test cho controllers và services.

## C<PERSON>u trúc

```
tests/
├── controllers/                # Test cho controllers
│   └── email-server-configuration-user.controller.spec.ts
├── services/                   # Test cho services
│   └── email-server-configuration-user.service.spec.ts
├── jest.config.js             # C<PERSON>u hình Jest
└── README.md                  # Hướng dẫn này
```

## Chạy test

### Ch<PERSON>y tất cả tests trong module

```bash
npx jest src/modules/integration/user/tests --config=src/modules/integration/user/tests/jest.config.js
```

### Chạy test với coverage

```bash
npx jest src/modules/integration/user/tests --config=src/modules/integration/user/tests/jest.config.js --coverage
```

### Chạy test cho từng file

#### Test cho Controller:

```bash
# Test cho EmailServerConfigurationUserController
npx jest src/modules/integration/user/tests/controllers/email-server-configuration-user.controller.spec.ts --config=src/modules/integration/user/tests/jest.config.js
```

#### Test cho Service:

```bash
# Test cho EmailServerConfigurationUserService
npx jest src/modules/integration/user/tests/services/email-server-configuration-user.service.spec.ts --config=src/modules/integration/user/tests/jest.config.js
```

### Chạy test trong watch mode

```bash
npx jest src/modules/integration/user/tests --config=src/modules/integration/user/tests/jest.config.js --watch
```

## Test Coverage

Để xem chi tiết coverage report:

```bash
npx jest src/modules/integration/user/tests --config=src/modules/integration/user/tests/jest.config.js --coverage --coverageReporters=html
```

Coverage report sẽ được tạo trong thư mục `./coverage/integration-user/`.

## Các test cases

### EmailServerConfigurationUserController

- ✅ Tạo cấu hình email server mới
- ✅ Lấy danh sách cấu hình email server
- ✅ Lấy thông tin cấu hình email server cụ thể
- ✅ Cập nhật cấu hình email server
- ✅ Xóa cấu hình email server
- ✅ Test kết nối với cấu hình có sẵn
- ✅ **Test kết nối với cấu hình trực tiếp (API mới)**

### EmailServerConfigurationUserService

- ✅ **Test kết nối với cấu hình trực tiếp**
- ✅ **Xử lý lỗi khi verify connection thất bại**
- ✅ **Xử lý lỗi khi gửi email thất bại**
- ✅ **Xử lý lỗi khi tạo transporter thất bại**
- ✅ **Sử dụng subject mặc định khi không được cung cấp**
- ✅ **Xử lý additional settings**
- ✅ **Hoạt động khi không có additional settings**

## API mới được test

### POST /user/integration/email-server/test-with-config

API này cho phép test kết nối email server với cấu hình trực tiếp mà không cần lưu vào database trước.

**Request Body:**
```json
{
  "emailServerConfig": {
    "serverName": "Gmail SMTP",
    "host": "smtp.gmail.com",
    "port": 587,
    "username": "<EMAIL>",
    "password": "your-app-password",
    "useSsl": true,
    "additionalSettings": {
      "auth": "login",
      "tls": { "rejectUnauthorized": false }
    }
  },
  "testInfo": {
    "recipientEmail": "<EMAIL>",
    "subject": "Test Email Connection"
  }
}
```

**Response:**
```json
{
  "code": 200,
  "message": "Kiểm tra kết nối máy chủ email thành công",
  "result": {
    "success": true,
    "message": "Kết nối thành công! Email kiểm tra đã được gửi."
  }
}
```

## Lưu ý

- Tests sử dụng mock cho nodemailer để tránh gửi email thật trong quá trình test
- Tests cover cả trường hợp thành công và thất bại
- Encryption service được mock để test không phụ thuộc vào implementation thực tế
- Repository được mock để test logic service mà không cần database thật
