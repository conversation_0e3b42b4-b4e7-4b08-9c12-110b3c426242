module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '../../../../../',
  testRegex: 'src/modules/integration/user/tests/.*\\.spec\\.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  collectCoverageFrom: ['src/modules/integration/user/**/*.(t|j)s'],
  coverageDirectory: './coverage/integration-user',
  testEnvironment: 'node',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@modules/(.*)$': '<rootDir>/src/modules/$1',
    '^@shared/(.*)$': '<rootDir>/src/shared/$1',
    '^@common/(.*)$': '<rootDir>/src/common/$1',
    '^@utils/(.*)$': '<rootDir>/src/shared/utils/$1',
    '^@database/(.*)$': '<rootDir>/src/database/$1',
    '^@config$': '<rootDir>/src/config',
  },
  passWithNoTests: true,
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,
  maxWorkers: 1,
  forceExit: true,
  detectOpenHandles: true,
  haste: {
    enableSymlinks: false,
  },
  watchPathIgnorePatterns: [
    '<rootDir>/dist/',
    '<rootDir>/node_modules/',
  ],
  testPathIgnorePatterns: [
    '<rootDir>/dist/',
    '<rootDir>/node_modules/',
  ],
};
