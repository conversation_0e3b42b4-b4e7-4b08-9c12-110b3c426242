import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho việc trả về thông tin tài khoản ngân hàng
 */
export class BankAccountResponseDto {
  @ApiProperty({
    description: 'ID tài khoản ngân hàng',
    example: '123456'
  })
  id: string;

  @ApiProperty({
    description: 'ID công ty',
    example: '789012'
  })
  companyId: string;

  @ApiProperty({
    description: 'ID ngân hàng',
    example: '1'
  })
  bankId: string;

  @ApiProperty({
    description: 'Tên chủ tài khoản',
    example: 'NGUYEN VAN A'
  })
  accountHolderName: string;

  @ApiProperty({
    description: 'Số tài khoản',
    example: '**********'
  })
  accountNumber: string;

  @ApiProperty({
    description: 'Số dư',
    example: '1000000'
  })
  accumulated: string;

  @ApiProperty({
    description: 'Tên gợi nhớ',
    example: 'T<PERSON><PERSON> khoản chính'
  })
  label: string;

  @ApiProperty({
    description: 'Trạng thái đã liên kết API ngân hàng hay chưa',
    example: true
  })
  bankApiConnected: boolean;

  @ApiProperty({
    description: 'Thời gian phát sinh giao dịch lần cuối',
    example: '2023-01-01 00:00:00'
  })
  lastTransaction: string;

  @ApiProperty({
    description: 'Ngày tạo',
    example: '2023-01-01 00:00:00'
  })
  createdAt: string;

  @ApiProperty({
    description: 'Ngày cập nhật',
    example: '2023-01-01 00:00:00'
  })
  updatedAt: string;
}
