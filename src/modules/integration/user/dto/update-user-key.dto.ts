import { ApiProperty } from '@nestjs/swagger';
import { IsObject, IsOptional } from 'class-validator';

/**
 * DTO cho việc cập nhật API key của người dùng
 */
export class UpdateUserKeyDto {
  @ApiProperty({
    description: 'Thông tin xác thực API',
    example: {
      api_key: 'sk-abcdefghijklmnopqrstuvwxyz',
      organization_id: 'org-123456789'
    },
    required: false
  })
  @IsOptional()
  @IsObject()
  credentials?: Record<string, any>;

  @ApiProperty({
    description: 'Cài đặt người dùng',
    example: {
      is_active: true,
      is_default: true
    },
    required: false
  })
  @IsOptional()
  @IsObject()
  settings?: Record<string, any>;
}
