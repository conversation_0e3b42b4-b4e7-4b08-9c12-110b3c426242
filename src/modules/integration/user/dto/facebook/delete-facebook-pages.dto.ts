import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho việc xóa nhiều trang Facebook cùng lúc
 */
export class DeleteFacebookPagesDto {
  @ApiProperty({
    description: 'Danh sách ID của các trang Facebook cần xóa',
    example: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890', 'b2c3d4e5-f6a7-8901-bcde-f01234567890'],
    type: [String],
  })
  @IsArray({ message: 'pageIds phải là một mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất một page ID' })
  @IsString({ each: true, message: 'Mỗi page ID phải là chuỗi' })
  @IsNotEmpty({ each: true, message: 'Page ID không được để trống' })
  pageIds: string[];
}
