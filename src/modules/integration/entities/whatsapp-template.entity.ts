import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { WhatsAppAccount } from './whatsapp-account.entity';

/**
 * Entity đại diện cho bảng whatsapp_templates trong cơ sở dữ liệu
 * Lưu trữ thông tin về các mẫu tin nhắn WhatsApp đã được phê duyệt
 */
@Entity('whatsapp_templates')
export class WhatsAppTemplate {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID tài khoản WhatsApp
   */
  @Column({ name: 'whatsapp_account_id' })
  whatsappAccountId: number;

  /**
   * ID mẫu tin nhắn trên WhatsApp
   */
  @Column({ name: 'template_id', length: 255 })
  templateId: string;

  /**
   * Tên mẫu tin nhắn
   */
  @Column({ name: 'name', length: 255 })
  name: string;

  /**
   * <PERSON><PERSON><PERSON> ngữ của mẫu tin nhắn
   */
  @Column({ name: 'language', length: 10 })
  language: string;

  /**
   * Loại mẫu tin nhắn (text, media, interactive)
   */
  @Column({ name: 'category', length: 50 })
  category: string;

  /**
   * Trạng thái của mẫu tin nhắn (APPROVED, PENDING, REJECTED)
   */
  @Column({ name: 'status', length: 20 })
  status: string;

  /**
   * Nội dung của mẫu tin nhắn (JSON)
   */
  @Column({ name: 'components', type: 'jsonb' })
  components: Record<string, any>;

  /**
   * Thời điểm tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  createdAt: number;

  /**
   * Thời điểm cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  updatedAt: number;

  /**
   * Quan hệ với bảng whatsapp_accounts
   */
  @ManyToOne(() => WhatsAppAccount)
  @JoinColumn({ name: 'whatsapp_account_id' })
  whatsappAccount: WhatsAppAccount;
}
