import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { WhatsAppAccount } from './whatsapp-account.entity';

/**
 * Entity đại diện cho bảng whatsapp_webhook_logs trong cơ sở dữ liệu
 * Lưu trữ log các webhook từ WhatsApp để debug và theo dõi
 */
@Entity('whatsapp_webhook_logs')
export class WhatsAppWebhookLog {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID tài khoản WhatsApp
   */
  @Column({ name: 'whatsapp_account_id', nullable: true })
  whatsappAccountId: number;

  /**
   * Dữ liệu webhook nhận được (JSON)
   */
  @Column({ name: 'payload', type: 'jsonb' })
  payload: Record<string, any>;

  /**
   * Trạng thái xử lý (success/error)
   */
  @Column({ name: 'status', length: 20 })
  status: string;

  /**
   * Thông báo lỗi (nếu có)
   */
  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage: string;

  /**
   * Thời điểm nhận webhook (Unix timestamp)
   */
  @Column({ name: 'received_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  receivedAt: number;

  /**
   * Quan hệ với bảng whatsapp_accounts
   */
  @ManyToOne(() => WhatsAppAccount)
  @JoinColumn({ name: 'whatsapp_account_id' })
  whatsappAccount: WhatsAppAccount;
}
