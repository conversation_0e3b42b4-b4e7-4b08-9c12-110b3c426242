import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng email_server_configurations trong cơ sở dữ liệu
 * Lưu trữ thông tin cấu hình cụ thể cho máy chủ Email (SMTP server)
 */
@Entity('email_server_configurations')
export class EmailServerConfiguration {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Mã người dùng
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * Tên hiển thị của cấu hình, ví dụ: "Mailgun Server #1" hoặc "AWS SES"
   */
  @Column({ name: 'server_name', length: 100 })
  serverName: string;

  /**
   * Địa chỉ máy chủ SMTP, ví dụ: smtp.gmail.com, smtp.mailgun.org…
   */
  @Column({ name: 'host', length: 255 })
  host: string;

  /**
   * Cổng SMTP, ví dụ: 465, 587, …
   */
  @Column({ name: 'port' })
  port: number;

  /**
   * Tên đăng nhập hoặc Email account (nếu sử dụng tài khoản riêng)
   */
  @Column({ name: 'username', length: 255 })
  username: string;

  /**
   * Mật khẩu hoặc token xác thực cho SMTP
   */
  @Column({ name: 'password', length: 255 })
  password: string;

  /**
   * Xác định có sử dụng SSL/TLS hay không
   */
  @Column({ name: 'use_ssl' })
  useSsl: boolean;

  /**
   * Cho phép lưu các cấu hình nâng cao, ví dụ: certificate path, cơ chế xác thực, v.v.
   */
  @Column({ name: 'additional_settings', type: 'json', nullable: true })
  additionalSettings: any;

  /**
   * Thời gian tạo
   */
  @Column({ name: 'created_at', type: 'bigint' })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @Column({ name: 'updated_at', type: 'bigint' })
  updatedAt: number;

  // Không sử dụng quan hệ với bảng users, chỉ lưu ID
}