import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, PrimaryGeneratedColumn } from 'typeorm';
import { TelegramBot } from './telegram-bot.entity';

/**
 * Entity đại diện cho bảng telegram_webhook_logs trong cơ sở dữ liệu
 * Lưu trữ log các webhook từ Telegram để debug và theo dõi
 */
@Entity('telegram_webhook_logs')
export class TelegramWebhookLog {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID của bot Telegram
   */
  @Column({ name: 'telegram_bot_id', nullable: true })
  telegramBotId: number;

  /**
   * Dữ liệu webhook nhận được (JSON)
   */
  @Column({ name: 'payload', type: 'jsonb' })
  payload: Record<string, any>;

  /**
   * Trạng thái xử lý (success/error)
   */
  @Column({ name: 'status', length: 20 })
  status: string;

  /**
   * Thông báo lỗi (nếu có)
   */
  @Column({ name: 'error_message', type: 'text', nullable: true })
  errorMessage: string;

  /**
   * Thời điểm nhận webhook (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  createdAt: number;
}
