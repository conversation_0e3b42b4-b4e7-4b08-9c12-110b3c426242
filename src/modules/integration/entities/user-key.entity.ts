import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn, Unique } from 'typeorm';
import { User } from '@modules/user/entities';

/**
 * Entity đại diện cho bảng user_keys trong cơ sở dữ liệu
 * Cấu hình của người dùng cho nhà cung cấp vận chuyển
 */
@Entity('user_keys')
@Unique(['userId', 'providerId'])
export class UserKey {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID người dùng
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * ID nhà cung cấp vận chuyển
   */
  @Column({ name: 'provider_id' })
  providerId: number;

  /**
   * Thông tin xác thực
   */
  @Column({ name: 'credentials', type: 'jsonb', default: '{"api_key": null, "api_secret": null}' })
  credentials: any;

  /**
   * Cài đặt người dùng
   */
  @Column({ name: 'settings', type: 'jsonb', default: '{"is_active": true, "is_default": false}' })
  settings: any;

  /**
   * Thời gian tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', default: '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint' })
  createdAt: number;

  /**
   * Thời gian cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', default: '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint' })
  updatedAt: number;
}
