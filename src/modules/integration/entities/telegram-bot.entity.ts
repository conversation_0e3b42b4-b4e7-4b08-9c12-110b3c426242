import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { Agent } from '@modules/agent/entities';

/**
 * Entity đại diện cho bảng telegram_bots trong cơ sở dữ liệu
 * Lưu trữ thông tin về các bot Telegram được tạo bởi người dùng
 */
@Entity('telegram_bots')
export class TelegramBot {
  /**
   * ID tự động tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * ID người dùng sở hữu bot
   */
  @Column({ name: 'user_id' })
  userId: number;

  /**
   * Tên bot do người dùng đặt
   */
  @Column({ name: 'bot_name', length: 255 })
  botName: string;

  /**
   * Username của bot trên Telegram (không bao gồm @)
   */
  @Column({ name: 'bot_username', length: 255 })
  botUsername: string;

  /**
   * Token API của bot từ BotFather
   */
  @Column({ name: 'bot_token', length: 255 })
  botToken: string;

  /**
   * ID agent được kết nối với bot Telegram
   */
  @Column({ name: 'agent_id', type: 'uuid', nullable: true })
  agentId: string;

  /**
   * Trạng thái hoạt động của bot
   */
  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  /**
   * Webhook URL đã đăng ký với Telegram
   */
  @Column({ name: 'webhook_url', length: 255, nullable: true })
  webhookUrl: string;

  /**
   * Thời điểm tạo (Unix timestamp)
   */
  @Column({ name: 'created_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  createdAt: number;

  /**
   * Thời điểm cập nhật (Unix timestamp)
   */
  @Column({ name: 'updated_at', type: 'bigint', default: () => 'EXTRACT(EPOCH FROM NOW())::BIGINT' })
  updatedAt: number;
}
