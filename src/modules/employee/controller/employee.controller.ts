import { SwaggerApiResponse } from '@/common/decorators/swagger-api-response.decorator';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import {
  AssignEmployeeRoleDto, AvatarUploadResponseDto, ChangeEmployeePasswordDto, ChangePasswordResponseDto, CreateEmployeeDto, CreateEmployeeResponseDto, EmployeeAvatarUploadDto, EmployeeLoginDto,
  EmployeeLoginResponseDto, EmployeeQueryDto, EmployeeResponseDto, EmployeeRoleResponseDto, UpdateEmployeeAvatarDto, EmployeeOverviewDto, RoleCountDto
} from '@modules/employee/dto';
import { Employee } from '@modules/employee/entities';
import { EmployeeAuthService, EmployeeService } from '@modules/employee/services';
import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Res,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
  getSchemaPath,
  ApiResponse as NestApiResponse
} from '@nestjs/swagger';
import { Response } from 'express';

/**
 * Controller xử lý các API liên quan đến nhân viên
 */
@ApiTags(SWAGGER_API_TAGS.EMPLOYEES)
@ApiExtraModels(
  ApiResponseDto,
  EmployeeLoginResponseDto,
  EmployeeResponseDto,
  AvatarUploadResponseDto,
  ChangePasswordResponseDto,
  EmployeeRoleResponseDto,
  CreateEmployeeResponseDto,
  PaginatedResult,
  EmployeeOverviewDto,
  RoleCountDto,
)
@Controller('employees')
export class EmployeeController {
  constructor(
    private readonly employeeService: EmployeeService,
    private readonly employeeAuthService: EmployeeAuthService,
  ) { }

  /**
   * Đăng nhập nhân viên
   * @param loginDto Thông tin đăng nhập
   * @returns Token JWT và thông tin nhân viên
   */
  @Post('login')
  @ApiOperation({
    summary: 'Đăng nhập nhân viên',
    description:
      'API này cho phép nhân viên đăng nhập vào hệ thống và nhận JWT token để xác thực các yêu cầu tiếp theo. Refresh token được lưu trong cookie HTTP-only.',
  })
  @ApiBody({
    type: EmployeeLoginDto,
    description: 'Thông tin đăng nhập của nhân viên',
    examples: {
      example1: {
        value: {
          email: '<EMAIL>',
          password: 'Haianh123@123',
        },
        summary: 'Thông tin đăng nhập mẫu',
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.OK,
    description: 'Đăng nhập thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Đăng nhập thành công' },
            result: { $ref: getSchemaPath(EmployeeLoginResponseDto) },
          },
        },
      ],
    },
    headers: {
      'Set-Cookie': {
        description: 'Cookie chứa refresh token',
        schema: {
          type: 'string',
          example:
            'refresh_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=604800',
        },
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description: 'Đăng nhập thất bại',
    schema: {
      type: 'object',
      properties: {
        code: { type: 'number', example: 401 },
        message: {
          type: 'string',
          example: 'Email hoặc mật khẩu không chính xác',
        },
        error: { type: 'string', example: 'Unauthorized' },
      },
    },
  })
  @SwaggerApiResponse({ description: 'Thông tin đăng nhập thành công' })
  async login(
    @Body() loginDto: EmployeeLoginDto,
    @Res({ passthrough: true }) response: Response,
  ): Promise<ApiResponseDto<EmployeeLoginResponseDto>> {
    const result = await this.employeeAuthService.login(loginDto);

    // Thiết lập cookie với refresh token
    response.cookie('refresh_token', result.refreshToken, {
      httpOnly: true, // Không cho phép JavaScript truy cập cookie
      secure: true, // Chỉ gửi cookie qua HTTPS
      sameSite: 'strict', // Chỉ gửi cookie cho cùng site
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 ngày
      path: '/', // Cookie có hiệu lực trên toàn bộ domain
    });

    // Tạo đối tượng phản hồi theo DTO
    const responseData: EmployeeLoginResponseDto = {
      accessToken: result.accessToken,
      expiresIn: result.expiresIn, // Giữ lại để tương thích ngược
      expiresAt: result.expiresAt,
      employee: {
        id: result.employee.id,
        email: result.employee.email,
        fullName: result.employee.fullName,
        avatar: result.employee.avatar,
        roles: result.employee.roles,
        permissions: result.employee.permissions,
      },
    };

    return {
      code: 200,
      message: 'Đăng nhập thành công',
      result: responseData,
    };
  }

  /**
   * Tạo nhân viên mới
   * @param createEmployeeDto Thông tin nhân viên mới
   * @returns Nhân viên đã được tạo
   */
  // @ApiBearerAuth('JWT-auth')
  // @UseGuards(JwtEmployeeGuard)
  @Post()
  @ApiOperation({
    summary: 'Tạo nhân viên mới',
    description:
      'API này cho phép tạo một nhân viên mới trong hệ thống. Yêu cầu quyền quản trị.',
  })
  @ApiBody({
    type: CreateEmployeeDto,
    description: 'Thông tin nhân viên mới',
    examples: {
      example1: {
        value: {
          fullName: 'Nguyễn Văn A',
          email: '<EMAIL>',
          phoneNumber: '0987654321',
          password: 'Password123!',
          address: 'Hà Nội',
          roleIds: [1, 2],
        },
        summary: 'Thông tin nhân viên mới mẫu',
      },
      example2: {
        value: {
          fullName: 'Nguyễn Văn A',
          email: '<EMAIL>',
          phoneNumber: '0987654321',
          password: 'Password123!',
          address: 'Hà Nội',
          roleIds: [1, 2],
          avatarImageType: 'image/jpeg',
          avatarMaxSize: 2097152,
        },
        summary: 'Thông tin nhân viên mới với yêu cầu tạo URL upload avatar',
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.CREATED,
    description: 'Tạo nhân viên thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Tạo nhân viên thành công' },
            result: { $ref: getSchemaPath(CreateEmployeeResponseDto) },
          },
        },
      ],
    },
  })
  @NestApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: {
          type: 'array',
          items: { type: 'string' },
          example: ['email must be an email'],
        },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Email đã tồn tại',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 409 },
        message: {
          type: 'string',
          example: 'Email <EMAIL> đã được sử dụng',
        },
        error: { type: 'string', example: 'Conflict' },
      },
    },
  })
  @SwaggerApiResponse({ description: 'Nhân viên đã được tạo' })
  async createEmployee(
    @Body() createEmployeeDto: CreateEmployeeDto,
  ): Promise<ApiResponseDto<CreateEmployeeResponseDto>> {
    const result = await this.employeeService.createEmployee(createEmployeeDto);
    return ApiResponseDto.success(result, 'Tạo nhân viên thành công');
  }

  /**
   * Tạo URL tạm thời để tải lên avatar nhân viên
   * @param employeeId ID của nhân viên
   * @param avatarUploadDto Thông tin về loại và kích thước avatar
   * @returns URL tạm thời và thông tin khóa S3
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Post(':id/avatar/upload-url')
  @ApiOperation({
    summary: 'Tạo URL tạm thời để tải lên avatar nhân viên',
    description:
      'API này tạo một URL tạm thời để tải lên avatar cho nhân viên. URL này có thời hạn 5 phút.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của nhân viên',
    type: 'number',
    example: 1,
  })
  @ApiBody({
    type: EmployeeAvatarUploadDto,
    description: 'Thông tin về loại và kích thước avatar',
    examples: {
      example1: {
        value: {
          imageType: 'image/jpeg',
          maxSize: 2097152, // 2MB
        },
        summary: 'Thông tin avatar mẫu',
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.OK,
    description: 'Tạo URL tải lên avatar thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            uploadUrl: {
              type: 'string',
              example: 'https://storage.example.com/upload?token=abc123...',
            },
            avatarKey: {
              type: 'string',
              example: 'employee-avatars/images/avatar-1-1682506092000-uuid',
            },
            expiresIn: {
              type: 'number',
              example: 300000,
              description: 'Thời gian hết hạn (giây)',
            },
            expiresAt: {
              type: 'number',
              example: 1746968772000,
              description: 'Thời điểm hết hạn (timestamp)',
            },
          },
        },
        message: {
          type: 'string',
          example: 'Tạo URL tải lên avatar thành công',
        },
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy nhân viên',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: {
          type: 'string',
          example: 'Nhân viên với ID "1" không tồn tại',
        },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @SwaggerApiResponse({ description: 'URL tạm thời để tải lên avatar' })
  async createAvatarUploadUrl(
    @Param('id', ParseIntPipe) employeeId: number,
    @Body() avatarUploadDto: EmployeeAvatarUploadDto,
  ): Promise<ApiResponseDto<AvatarUploadResponseDto>> {
    const result = await this.employeeService.createAvatarUploadUrl(
      employeeId,
      avatarUploadDto,
    );
    return ApiResponseDto.success(result, 'Tạo URL tải lên avatar thành công');
  }

  /**
   * Cập nhật avatar cho nhân viên
   * @param employeeId ID của nhân viên
   * @param updateAvatarDto Thông tin avatar mới
   * @returns Nhân viên đã được cập nhật
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Put(':id/avatar')
  @ApiOperation({
    summary: 'Cập nhật avatar cho nhân viên',
    description:
      'API này cập nhật avatar cho nhân viên sau khi đã tải lên thành công.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của nhân viên',
    type: 'number',
    example: 1,
  })
  @ApiBody({
    type: UpdateEmployeeAvatarDto,
    description: 'Thông tin avatar mới',
    examples: {
      example1: {
        value: {
          avatarKey: 'employee-avatars/images/avatar-1-1682506092000-uuid',
        },
        summary: 'Thông tin avatar mới mẫu',
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.OK,
    description: 'Cập nhật avatar thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            id: { type: 'number', example: 1 },
            fullName: { type: 'string', example: 'Nguyễn Văn A' },
            email: { type: 'string', example: '<EMAIL>' },
            phoneNumber: { type: 'string', example: '0987654321' },
            address: { type: 'string', example: 'Hà Nội' },
            avatar: {
              type: 'string',
              example: 'employee-avatars/images/avatar-1-1682506092000-uuid',
            },
            createdAt: { type: 'number', example: 1682506092000 },
            updatedAt: { type: 'number', example: 1682506092000 },
            enable: { type: 'boolean', example: true },
          },
        },
        message: { type: 'string', example: 'Cập nhật avatar thành công' },
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy nhân viên',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: {
          type: 'string',
          example: 'Nhân viên với ID "1" không tồn tại',
        },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @SwaggerApiResponse({ description: 'Avatar đã được cập nhật' })
  async updateAvatar(
    @Param('id', ParseIntPipe) employeeId: number,
    @Body() updateAvatarDto: UpdateEmployeeAvatarDto,
  ): Promise<ApiResponseDto<EmployeeResponseDto>> {
    const employee = await this.employeeService.updateAvatar(
      employeeId,
      updateAvatarDto,
    );
    return ApiResponseDto.success(employee, 'Cập nhật avatar thành công');
  }

  /**
   * Đổi mật khẩu cho nhân viên
   * @param employeeId ID của nhân viên
   * @param changePasswordDto Thông tin mật khẩu mới
   * @returns Thông báo kết quả
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Put(':id/password')
  @ApiOperation({
    summary: 'Đổi mật khẩu cho nhân viên',
    description:
      'API này cho phép đổi mật khẩu cho nhân viên. Yêu cầu quyền quản trị hoặc chính nhân viên đó.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của nhân viên',
    type: 'number',
    example: 1,
  })
  @ApiBody({
    type: ChangeEmployeePasswordDto,
    description: 'Thông tin mật khẩu mới',
    examples: {
      example1: {
        value: {
          newPassword: 'NewPassword123!',
        },
        summary: 'Thông tin mật khẩu mới mẫu',
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.OK,
    description: 'Đổi mật khẩu thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            message: { type: 'string', example: 'Đổi mật khẩu thành công' },
          },
        },
        message: { type: 'string', example: 'Đổi mật khẩu thành công' },
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Mật khẩu không đủ mạnh',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: {
          type: 'string',
          example:
            'Mật khẩu không đủ mạnh. Mật khẩu trung bình. Mật khẩu nên chứa ít nhất 1 chữ hoa. Mật khẩu nên chứa ít nhất 1 ký tự đặc biệt.',
        },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy nhân viên',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: {
          type: 'string',
          example: 'Nhân viên với ID "1" không tồn tại',
        },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @SwaggerApiResponse({ description: 'Mật khẩu đã được đổi' })
  async changePassword(
    @Param('id', ParseIntPipe) employeeId: number,
    @Body() changePasswordDto: ChangeEmployeePasswordDto,
  ): Promise<ApiResponseDto<ChangePasswordResponseDto>> {
    const result = await this.employeeService.changePassword(
      employeeId,
      changePasswordDto,
    );
    return ApiResponseDto.success(result, 'Đổi mật khẩu thành công');
  }

  /**
   * Gán vai trò cho nhân viên
   * @param employeeId ID của nhân viên
   * @param assignRoleDto Thông tin vai trò cần gán
   * @returns Nhân viên đã được cập nhật
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Put(':id/roles')
  @ApiOperation({
    summary: 'Gán vai trò cho nhân viên',
    description:
      'API này cho phép gán các vai trò cho nhân viên. Yêu cầu quyền quản trị.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của nhân viên',
    type: 'number',
    example: 1,
  })
  @ApiBody({
    type: AssignEmployeeRoleDto,
    description: 'Danh sách ID của các vai trò cần gán',
    examples: {
      example1: {
        value: {
          roleIds: [1, 2, 3],
        },
        summary: 'Danh sách vai trò mẫu',
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.OK,
    description: 'Gán vai trò thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            id: { type: 'number', example: 1 },
            fullName: { type: 'string', example: 'Nguyễn Văn A' },
            email: { type: 'string', example: '<EMAIL>' },
            roles: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'number', example: 1 },
                  name: { type: 'string', example: 'Admin' },
                  description: {
                    type: 'string',
                    example: 'Quyền quản trị viên',
                  },
                },
              },
            },
          },
        },
        message: { type: 'string', example: 'Gán vai trò thành công' },
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy nhân viên',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: {
          type: 'string',
          example: 'Nhân viên với ID "1" không tồn tại',
        },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @SwaggerApiResponse({ description: 'Vai trò đã được gán' })
  async assignRoles(
    @Param('id', ParseIntPipe) employeeId: number,
    @Body() assignRoleDto: AssignEmployeeRoleDto,
  ): Promise<ApiResponseDto<EmployeeResponseDto>> {
    const employee = await this.employeeService.assignRoles(
      employeeId,
      assignRoleDto,
    );
    return ApiResponseDto.success(employee, 'Gán vai trò thành công');
  }

  /**
   * Lấy danh sách vai trò của nhân viên
   * @param employeeId ID của nhân viên
   * @returns Danh sách vai trò
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Get(':id/roles')
  @ApiOperation({
    summary: 'Lấy danh sách vai trò của nhân viên',
    description: 'API này trả về danh sách các vai trò được gán cho nhân viên.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của nhân viên',
    type: 'number',
    example: 1,
  })
  @NestApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách vai trò thành công',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'number', example: 1 },
              name: { type: 'string', example: 'Admin' },
              description: { type: 'string', example: 'Quyền quản trị viên' },
              permissions: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    id: { type: 'number', example: 1 },
                    module: { type: 'string', example: 'users' },
                    action: { type: 'string', example: 'read' },
                    description: {
                      type: 'string',
                      example: 'Xem danh sách người dùng',
                    },
                  },
                },
              },
            },
          },
        },
        message: {
          type: 'string',
          example: 'Lấy danh sách vai trò thành công',
        },
      },
    },
  })
  @NestApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Không tìm thấy nhân viên',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: {
          type: 'string',
          example: 'Nhân viên với ID "1" không tồn tại',
        },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  @SwaggerApiResponse({ description: 'Danh sách vai trò' })
  async getEmployeeRoles(
    @Param('id', ParseIntPipe) employeeId: number,
  ): Promise<ApiResponseDto<any[]>> {
    const roles = await this.employeeService.getEmployeeRoles(employeeId);
    return ApiResponseDto.success(roles, 'Lấy danh sách vai trò thành công');
  }

  /**
   * Lấy thông tin tổng quan về nhân viên, vai trò và quyền
   * @returns Thông tin tổng quan nhân viên
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Get('overview')
  @ApiOperation({
    summary: 'Lấy thông tin tổng quan về nhân viên, vai trò và quyền',
    description:
      'API này trả về thông tin tổng quan về nhân viên, bao gồm số lượng nhân viên, danh sách vai trò, và tổng số quyền.',
  })
  @NestApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy thông tin tổng quan thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Lấy thông tin tổng quan thành công' },
            result: { $ref: getSchemaPath(EmployeeOverviewDto) },
          },
        },
      ],
    },
  })
  @SwaggerApiResponse({ description: 'Thông tin tổng quan nhân viên' })
  async getEmployeeOverview(): Promise<ApiResponseDto<EmployeeOverviewDto>> {
    const overview = await this.employeeService.getEmployeeOverview();
    return ApiResponseDto.success(overview, 'Lấy thông tin tổng quan thành công');
  }

  /**
   * Lấy danh sách nhân viên có phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách nhân viên có phân trang
   */
  @ApiBearerAuth('JWT-auth')
  @UseGuards(JwtEmployeeGuard)
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách nhân viên có phân trang',
    description:
      'API này trả về danh sách nhân viên có phân trang và hỗ trợ tìm kiếm, lọc.',
  })
  @ApiQuery({
    name: 'page',
    description: 'Trang hiện tại (bắt đầu từ 1)',
    required: false,
    type: Number,
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Số lượng bản ghi trên mỗi trang',
    required: false,
    type: Number,
    example: 10,
  })
  @ApiQuery({
    name: 'search',
    description: 'Từ khóa tìm kiếm (tìm theo tên)',
    required: false,
    type: String,
    example: 'Nguyễn',
  })
  @ApiQuery({
    name: 'sortBy',
    description: 'Trường sắp xếp',
    required: false,
    type: String,
    example: 'createdAt',
  })
  @ApiQuery({
    name: 'sortDirection',
    description: 'Hướng sắp xếp',
    required: false,
    enum: ['ASC', 'DESC'],
    example: 'DESC',
  })
  @ApiQuery({
    name: 'enable',
    description: 'Lọc theo trạng thái hoạt động',
    required: false,
    type: Boolean,
    example: true,
  })
  @ApiQuery({
    name: 'email',
    description: 'Lọc theo email',
    required: false,
    type: String,
    example: '<EMAIL>',
  })
  @ApiQuery({
    name: 'phoneNumber',
    description: 'Lọc theo số điện thoại',
    required: false,
    type: String,
    example: '0987654321',
  })
  @NestApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách nhân viên thành công',
    schema: ApiResponseDto.getPaginatedSchema(EmployeeResponseDto),
  })
  @SwaggerApiResponse({ description: 'Danh sách nhân viên có phân trang' })
  async getEmployees(
    @Query() queryDto: EmployeeQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<Employee>>> {
    const employees = await this.employeeService.findAll(queryDto);
    return ApiResponseDto.paginated(
      employees,
      'Lấy danh sách nhân viên thành công',
    );
  }
}
