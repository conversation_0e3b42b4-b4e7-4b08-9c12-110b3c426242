import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO cho việc tạo permission mới
 */
export class CreatePermissionDto {
  /**
   * Hành động
   */
  @ApiProperty({
    description: 'Hành động',
    example: 'create',
  })
  @IsNotEmpty({ message: 'Hành động không được để trống' })
  @IsString({ message: 'Hành động phải là chuỗi' })
  action: string;

  /**
   * Module
   */
  @ApiProperty({
    description: 'Module',
    example: 'user',
  })
  @IsNotEmpty({ message: 'Module không được để trống' })
  @IsString({ message: 'Module phải là chuỗi' })
  module: string;

  /**
   * Mô tả quyền
   */
  @ApiProperty({
    description: 'Mô tả quyền',
    example: 'Tạo người dùng mới',
  })
  @IsNotEmpty({ message: '<PERSON>ô tả không được để trống' })
  @IsString({ message: '<PERSON>ô tả phải là chuỗi' })
  description: string;
}
