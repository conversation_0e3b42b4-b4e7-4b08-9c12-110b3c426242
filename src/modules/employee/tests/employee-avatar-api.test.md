# API Test cho Employee Avatar Upload

## <PERSON><PERSON><PERSON> đích
Tài liệu này mô tả các test case để kiểm tra API lấy URL tạm thời cho việc upload avatar nhân viên.

## Chuẩn bị
1. <PERSON><PERSON><PERSON> bảo server đang chạy
2. <PERSON><PERSON><PERSON> bị token JWT hợp lệ (đăng nhập vào hệ thống để lấy token)
3. <PERSON><PERSON><PERSON> bị ID của một nhân viên đã tồn tại trong hệ thống

## Test Case 1: Lấy URL tạm thời để upload avatar

### Y<PERSON><PERSON> cầu
- Phương thức: POST
- Endpoint: `/employees/{employeeId}/avatar/upload-url`
- Headers:
  - `Authorization: Bearer {token}`
  - `Content-Type: application/json`
- Body:
```json
{
  "imageType": "image/jpeg",
  "maxSize": 2097152
}
```

### Thực hiện với cURL
```bash
curl -X POST \
  http://localhost:3000/employees/1/avatar/upload-url \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "imageType": "image/jpeg",
    "maxSize": 2097152
}'
```

### Thực hiện với Postman
1. Tạo request POST mới
2. Nhập URL: `http://localhost:3000/employees/1/avatar/upload-url`
3. Tab Headers: Thêm `Authorization: Bearer {token}`
4. Tab Body: Chọn raw, JSON và nhập:
```json
{
  "imageType": "image/jpeg",
  "maxSize": 2097152
}
```
5. Gửi request

### Kết quả mong đợi
- Status code: 200
- Response body:
```json
{
  "code": 200,
  "message": "Tạo URL tải lên avatar thành công",
  "result": {
    "uploadUrl": "https://storage.example.com/upload/signed-url?token=abc123...",
    "avatarKey": "employee-avatars/1/avatar-123456789.jpg",
    "expiresIn": 300,
    "expiresAt": 1682506392000
  }
}
```

## Test Case 2: Tạo nhân viên mới với URL tạm thời cho avatar

### Yêu cầu
- Phương thức: POST
- Endpoint: `/employees`
- Headers:
  - `Authorization: Bearer {token}`
  - `Content-Type: application/json`
- Body:
```json
{
  "fullName": "Nguyễn Văn A",
  "email": "<EMAIL>",
  "phoneNumber": "0987654321",
  "password": "Password123!",
  "address": "Hà Nội",
  "avatarImageType": "image/jpeg",
  "avatarMaxSize": 2097152
}
```

### Thực hiện với cURL
```bash
curl -X POST \
  http://localhost:3000/employees \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "fullName": "Nguyễn Văn A",
    "email": "<EMAIL>",
    "phoneNumber": "0987654321",
    "password": "Password123!",
    "address": "Hà Nội",
    "avatarImageType": "image/jpeg",
    "avatarMaxSize": 2097152
}'
```

### Thực hiện với Postman
1. Tạo request POST mới
2. Nhập URL: `http://localhost:3000/employees`
3. Tab Headers: Thêm `Authorization: Bearer {token}`
4. Tab Body: Chọn raw, JSON và nhập:
```json
{
  "fullName": "Nguyễn Văn A",
  "email": "<EMAIL>",
  "phoneNumber": "0987654321",
  "password": "Password123!",
  "address": "Hà Nội",
  "avatarImageType": "image/jpeg",
  "avatarMaxSize": 2097152
}
```
5. Gửi request

### Kết quả mong đợi
- Status code: 200
- Response body:
```json
{
  "code": 200,
  "message": "Tạo nhân viên thành công",
  "result": {
    "id": 123,
    "fullName": "Nguyễn Văn A",
    "email": "<EMAIL>",
    "phoneNumber": "0987654321",
    "address": "Hà Nội",
    "createdAt": 1682506092000,
    "updatedAt": 1682506092000,
    "enable": true,
    "avatarUploadUrl": "https://storage.example.com/upload/signed-url?token=abc123...",
    "avatarKey": "employee-avatars/123/avatar-123456789.jpg",
    "avatarUrlExpiresAt": 1682506392000
  }
}
```

## Test Case 3: Upload avatar sử dụng URL tạm thời

### Yêu cầu
- Phương thức: PUT
- Endpoint: URL tạm thời từ response của Test Case 1 hoặc 2
- Headers:
  - `Content-Type: image/jpeg`
- Body: File hình ảnh (binary)

### Thực hiện với cURL
```bash
curl -X PUT \
  "https://storage.example.com/upload/signed-url?token=abc123..." \
  -H 'Content-Type: image/jpeg' \
  --data-binary '@/path/to/avatar.jpg'
```

### Thực hiện với Postman
1. Tạo request PUT mới
2. Nhập URL: URL tạm thời từ response của Test Case 1 hoặc 2
3. Tab Headers: Thêm `Content-Type: image/jpeg`
4. Tab Body: Chọn binary và chọn file hình ảnh
5. Gửi request

### Kết quả mong đợi
- Status code: 200

## Test Case 4: Cập nhật avatar cho nhân viên

### Yêu cầu
- Phương thức: PUT
- Endpoint: `/employees/{employeeId}/avatar`
- Headers:
  - `Authorization: Bearer {token}`
  - `Content-Type: application/json`
- Body:
```json
{
  "avatarKey": "employee-avatars/123/avatar-123456789.jpg"
}
```

### Thực hiện với cURL
```bash
curl -X PUT \
  http://localhost:3000/employees/123/avatar \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "avatarKey": "employee-avatars/123/avatar-123456789.jpg"
}'
```

### Thực hiện với Postman
1. Tạo request PUT mới
2. Nhập URL: `http://localhost:3000/employees/123/avatar`
3. Tab Headers: Thêm `Authorization: Bearer {token}`
4. Tab Body: Chọn raw, JSON và nhập:
```json
{
  "avatarKey": "employee-avatars/123/avatar-123456789.jpg"
}
```
5. Gửi request

### Kết quả mong đợi
- Status code: 200
- Response body:
```json
{
  "code": 200,
  "message": "Cập nhật avatar thành công",
  "result": {
    "id": 123,
    "fullName": "Nguyễn Văn A",
    "email": "<EMAIL>",
    "phoneNumber": "0987654321",
    "address": "Hà Nội",
    "createdAt": 1682506092000,
    "updatedAt": 1682506092000,
    "enable": true,
    "avatar": "employee-avatars/123/avatar-123456789.jpg"
  }
}
```
