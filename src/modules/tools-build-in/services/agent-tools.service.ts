import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { AppException } from '@common/exceptions';
import { TOOLS_BUILD_IN_ERROR_CODES } from '../exceptions';
import { AgentToolsResponseDto, GroupToolResponseDto, ToolResponseDto, AgentToolsNewResponseDto, NewToolResponseDto, ToolTypeEnum } from '../dto';

/**
 * Service xử lý các thao tác liên quan đến tool của agent
 */
@Injectable()
export class AgentToolsService {
  private readonly logger = new Logger(AgentToolsService.name);

  constructor(private readonly dataSource: DataSource) { }

  /**
   * Lấy danh sách tool của agent theo agent_id
   * @param agentId ID của agent
   * @returns Danh sách tool của agent
   */
  async getAgentTools(agentId: string): Promise<AgentToolsResponseDto | AgentToolsNewResponseDto> {
    try {
      // Ki<PERSON>m tra agent có tồn tại không
      const agent = await this.dataSource
        .createQueryBuilder()
        .select(['id', 'name'])
        .from('agents', 'agent')
        .where('agent.id = :agentId', { agentId })
        .getRawOne();

      if (!agent) {
        throw new AppException(
          TOOLS_BUILD_IN_ERROR_CODES.AGENT_NOT_FOUND,
          `Không tìm thấy agent với ID ${agentId}`,
        );
      }

      // Lấy thông tin type_id của agent từ bảng agents_user
      const agentUser = await this.dataSource
        .createQueryBuilder()
        .select(['type_id'])
        .from('agents_user', 'agent_user')
        .where('agent_user.id = :agentId', { agentId })
        .getRawOne();

      if (!agentUser || !agentUser.type_id) {
        throw new AppException(
          TOOLS_BUILD_IN_ERROR_CODES.AGENT_TYPE_NOT_FOUND,
          `Agent không có loại agent`,
        );
      }

      const typeAgentId = agentUser.type_id;

      // Lấy danh sách nhóm tool của loại agent
      const groupTools = await this.getGroupToolsByTypeAgentId(typeAgentId);

      // Tính tổng số tool
      const totalTools = groupTools.reduce(
        (total, group) => total + group.tools.length,
        0,
      );

      // Tạo response theo định dạng mới
      const newFormatTools = await this.convertToNewFormat(groupTools);

      // Kiểm tra xem có yêu cầu định dạng mới không
      const useNewFormat = true; // Có thể thay đổi logic này để xác định khi nào sử dụng định dạng mới

      if (useNewFormat) {
        return {
          tools: newFormatTools
        };
      }

      // Tạo response theo định dạng cũ
      return {
        agentId: agent.id,
        agentName: agent.name,
        groupTools,
        totalTools,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi lấy danh sách tool của agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        TOOLS_BUILD_IN_ERROR_CODES.AGENT_TOOLS_FETCH_FAILED,
        `Lỗi khi lấy danh sách tool của agent: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách nhóm tool của loại agent
   * @param typeAgentId ID của loại agent
   * @returns Danh sách nhóm tool
   */
  private async getGroupToolsByTypeAgentId(
    typeAgentId: number,
  ): Promise<GroupToolResponseDto[]> {
    try {
      // Lấy danh sách nhóm tool của loại agent
      const groupToolsData = await this.dataSource
        .createQueryBuilder()
        .select(['agt.group_id as id'])
        .from('admin_group_tools_type_agents', 'agt')
        .where('agt.type_agent_id = :typeAgentId', { typeAgentId })
        .getRawMany();

      if (!groupToolsData || groupToolsData.length === 0) {
        return [];
      }

      // Lấy thông tin chi tiết của từng nhóm tool
      const groupTools: GroupToolResponseDto[] = [];
      for (const groupToolData of groupToolsData) {
        const groupId = groupToolData.id;

        // Lấy thông tin nhóm tool
        const groupInfo = await this.dataSource
          .createQueryBuilder()
          .select(['id', 'name', 'description'])
          .from('admin_group_tools', 'agt')
          .where('agt.id = :groupId', { groupId })
          .getRawOne();

        if (!groupInfo) {
          continue;
        }

        // Lấy danh sách tool trong nhóm
        const tools = await this.getToolsByGroupId(groupId);

        // Thêm nhóm tool vào danh sách
        groupTools.push({
          id: groupInfo.id,
          name: groupInfo.name,
          description: groupInfo.description,
          tools,
        });
      }

      return groupTools;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách nhóm tool của loại agent: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy danh sách tool trong nhóm
   * @param groupId ID của nhóm tool
   * @returns Danh sách tool
   */
  private async getToolsByGroupId(groupId: number): Promise<ToolResponseDto[]> {
    try {

      // Lấy danh sách tool mapping từ user_group_tool_mappings
      const userToolMappings = await this.dataSource
        .createQueryBuilder()
        .select(['tool_id'])
        .from('user_group_tool_mappings', 'ugtm')
        .where('ugtm.group_id = :groupId', { groupId })
        .getRawMany();

      // Lấy thông tin chi tiết của từng user tool
      const userTools = await this.getUserToolsByMappings(userToolMappings);

      // Lấy danh sách tool tích hợp (custom tools) trong nhóm
      const customTools = await this.getCustomToolsByGroupId(groupId);

      // Kết hợp danh sách tool thông thường, user tool và tool tích hợp
      return [...userTools, ...customTools];
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách tool trong nhóm: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy danh sách tool của người dùng từ danh sách mapping
   * @param userToolMappings Danh sách mapping của user tool
   * @returns Danh sách tool của người dùng
   */
  private async getUserToolsByMappings(userToolMappings: any[]): Promise<ToolResponseDto[]> {
    if (!userToolMappings || userToolMappings.length === 0) {
      return [];
    }

    const userTools: ToolResponseDto[] = [];

    for (const mapping of userToolMappings) {
      try {
        const toolId = mapping.tool_id;

        // Lấy thông tin tool
        const toolInfo = await this.dataSource
          .createQueryBuilder()
          .select(['id', 'name', 'description', 'status'])
          .from('user_tools', 'ut')
          .where('ut.id = :toolId', { toolId })
          .getRawOne();

        if (!toolInfo) {
          this.logger.warn(`Không tìm thấy user tool với ID ${toolId}`);
          continue;
        }

        // Lấy phiên bản mới nhất của tool
        const latestVersion = await this.dataSource
          .createQueryBuilder()
          .select([
            'id',
            'version_number',
            'tool_name',
            'tool_description',
            'parameters',
            'endpoint',
            'method'
          ])
          .from('user_tool_versions', 'utv')
          .where('utv.original_function_id = :toolId', { toolId })
          .andWhere('utv.status != :status', { status: 'DEPRECATED' })
          .orderBy('utv.version_number', 'DESC')
          .limit(1)
          .getRawOne();

        if (!latestVersion) {
          this.logger.warn(`Không tìm thấy phiên bản cho user tool với ID ${toolId}`);
          continue;
        }

        // Tạo đối tượng tool response
        const toolResponse: ToolResponseDto = {
          id: toolInfo.id,
          name: toolInfo.name,
          description: toolInfo.description,
          type: 'USER',
          versionId: latestVersion.id,
          versionNumber: `V.${latestVersion.version_number}.0.0`,
          toolName: latestVersion.tool_name,
          parameters: latestVersion.parameters,
          endpoint: latestVersion.endpoint,
          method: latestVersion.method
        };

        // Thêm tool vào danh sách
        userTools.push(toolResponse);
      } catch (error) {
        this.logger.error(`Lỗi khi xử lý user tool: ${error.message}`);
        continue;
      }
    }

    return userTools;
  }

  /**
   * Lấy phiên bản mới nhất của tool
   * @param toolId ID của tool
   * @returns Thông tin phiên bản mới nhất
   */
  private async getLatestToolVersion(toolId: string): Promise<any> {
    try {
      return await this.dataSource
        .createQueryBuilder()
        .select([
          'id',
          'tool_id',
          'version_number',
          'tool_name',
          'tool_description',
          'parameters',
          'status'
        ])
        .from('admin_tool_versions', 'atv')
        .where('atv.tool_id = :toolId', { toolId })
        .andWhere('atv.status != :status', { status: 'DEPRECATED' })
        .orderBy('atv.version_number', 'DESC')
        .limit(1)
        .getRawOne();
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy phiên bản mới nhất của tool: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * Lấy danh sách tool tích hợp trong nhóm
   * @param groupId ID của nhóm tool
   * @returns Danh sách tool tích hợp
   */
  private async getCustomToolsByGroupId(groupId: number): Promise<ToolResponseDto[]> {
    try {
      // Lấy danh sách mapping của tool tích hợp
      const customToolMappings = await this.dataSource
        .createQueryBuilder()
        .select(['tool_id'])
        .from('user_group_tool_custom_mappings', 'ugtcm')
        .where('ugtcm.group_id = :groupId', { groupId })
        .getRawMany();

      if (!customToolMappings || customToolMappings.length === 0) {
        return [];
      }

      // Lấy thông tin chi tiết của từng tool tích hợp
      const customTools: ToolResponseDto[] = [];
      for (const mapping of customToolMappings) {
        const toolId = mapping.tool_id;

        try {
          // Lấy thông tin tool tích hợp và join với bảng api_key nếu có api_key_id
          // Sử dụng raw query để có thể xem chính xác dữ liệu trả về
          const customToolInfo = await this.dataSource.query(`
            SELECT
              utc.id,
              utc.tool_name,
              utc.tool_description,
              utc.query_param,
              utc.path_param,
              utc.body,
              utc.endpoint,
              utc.method,
              utc.base_url,
              utc.api_key_id,
              utc.oauth_id,
              ak.param_name,
              ak.api_key,
              ak.scheme_name
            FROM user_tools_custom utc
            LEFT JOIN api_key ak ON utc.api_key_id = ak.id
            WHERE utc.id = $1
            AND utc.status = $2
          `, [toolId, 'APPROVED'])
            .then(results => results && results.length > 0 ? results[0] : null);

          if (!customToolInfo) {
            this.logger.warn(`Không tìm thấy tool tích hợp với ID ${toolId}`);
            continue;
          }

          // Log để kiểm tra dữ liệu trả về
          this.logger.debug(`Dữ liệu tool tích hợp: ${JSON.stringify(customToolInfo)}`);

          // Kiểm tra thông tin API key
          if (customToolInfo.ak_param_name && customToolInfo.ak_api_key) {
            this.logger.debug(`Thông tin API key: paramName=${customToolInfo.ak_param_name}, apiKey=${customToolInfo.ak_api_key}`);
          } else {
            this.logger.debug(`Không có thông tin API key cho tool ${toolId}`);
          }

          // Tổng hợp parameters từ query_param, path_param và body
          const parameters = {
            type: 'object',
            properties: {
              query_param: {},
              path_param: {},
              body: {}
            },
            required: [],
          };

          // Thêm các thuộc tính từ query_param, path_param và body
          try {
            // Xử lý query_param
            if (customToolInfo.query_param) {
              const queryParam = typeof customToolInfo.query_param === 'string'
                ? JSON.parse(customToolInfo.query_param)
                : customToolInfo.query_param;

              parameters.properties.query_param = queryParam;
            }

            // Xử lý path_param
            if (customToolInfo.path_param) {
              const pathParam = typeof customToolInfo.path_param === 'string'
                ? JSON.parse(customToolInfo.path_param)
                : customToolInfo.path_param;

              parameters.properties.path_param = pathParam;
            }

            // Xử lý body
            if (customToolInfo.body) {
              const body = typeof customToolInfo.body === 'string'
                ? JSON.parse(customToolInfo.body)
                : customToolInfo.body;

              parameters.properties.body = body;
            }
          } catch (parseError) {
            this.logger.error(`Lỗi khi phân tích tham số của tool ${toolId}: ${parseError.message}`);
            // Tiếp tục với parameters mặc định nếu có lỗi phân tích
          }

          // Phân tích URL để tìm các tham số path
          const url = `${customToolInfo.base_url}${customToolInfo.endpoint}`;
          const pathParams = this.extractPathParamsFromUrl(url);

          // Nếu có path params trong URL nhưng không có trong parameters, thêm vào
          if (pathParams.length > 0 &&
            (!parameters.properties.path_param ||
              Object.keys(parameters.properties.path_param).length === 0)) {

            // Tạo đối tượng path_param mới
            const pathParamObj: Record<string, any> = {};
            pathParams.forEach((param: string) => {
              pathParamObj[param] = {
                type: 'string',
                description: `Tham số đường dẫn ${param}`
              };
            });

            // Gán vào parameters
            parameters.properties.path_param = pathParamObj;
          }

          // Chuẩn bị thông tin API key nếu có
          let apiKeyInfo: { paramName: string; apiKey: string; schemeName?: string } | undefined = undefined;
          if (customToolInfo.param_name && customToolInfo.api_key) {
            apiKeyInfo = {
              paramName: customToolInfo.param_name,
              apiKey: customToolInfo.api_key,
              schemeName: customToolInfo.scheme_name || 'default'
            };
            this.logger.debug(`Đã tìm thấy thông tin API key: ${JSON.stringify(apiKeyInfo)}`);
          }

          // Thêm tool tích hợp vào danh sách
          customTools.push({
            id: customToolInfo.id,
            name: customToolInfo.tool_name,
            description: customToolInfo.tool_description,
            type: 'CUSTOM',
            toolName: customToolInfo.tool_name,
            parameters: parameters,
            integration: {
              baseUrl: customToolInfo.base_url,
              endpoint: customToolInfo.endpoint,
              method: customToolInfo.method,
              hasAuth: !!(customToolInfo.api_key_id || customToolInfo.oauth_id)
            },
            // Lưu thông tin api key nếu có
            apiKeyInfo: apiKeyInfo
          });
        } catch (toolError) {
          this.logger.error(`Lỗi khi xử lý tool tích hợp ${toolId}: ${toolError.message}`);
          // Tiếp tục với tool tiếp theo nếu có lỗi
          continue;
        }
      }

      return customTools;
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách tool tích hợp trong nhóm: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }

  /**
   * Chuyển đổi danh sách nhóm tool sang định dạng mới
   * @param groupTools Danh sách nhóm tool
   * @returns Danh sách tool theo định dạng mới
   */
  private async convertToNewFormat(groupTools: GroupToolResponseDto[]): Promise<NewToolResponseDto[]> {
    const newFormatTools: NewToolResponseDto[] = [];

    // Lặp qua từng nhóm tool
    for (const group of groupTools) {
      // Lặp qua từng tool trong nhóm
      for (const tool of group.tools) {
        try {
          // Kiểm tra và đảm bảo tool.parameters tồn tại
          const parameters = tool.parameters || {
            type: 'object',
            properties: {},
            required: [],
          };

          // Tạo đối tượng tool mới theo định dạng mới
          let newTool: NewToolResponseDto;

          // Xử lý khác nhau dựa trên loại tool
          if (tool.type === 'USER') {
            // Đối với user tool, sử dụng endpoint và method trực tiếp, không cần headers
            newTool = {
              parameter: {
                name: tool.name,
                description: tool.description,
                inputSchema: this.convertParametersToInputSchema(parameters, tool.type),
              },
              extra: {
                url: tool.endpoint || '',
                method: tool.method || 'GET',
              },
              typeTool: ToolTypeEnum.IN_SYSTEM, // User tool thuộc hệ thống
            };
          } else if (tool.type === 'CUSTOM') {
            // Đối với custom tool, lấy api-key từ dữ liệu nếu có
            const extra: any = {
              url: this.getToolUrl(tool),
              method: this.getToolMethod(tool),
            };

            // Nếu tool có thông tin API key
            if (tool.apiKeyInfo && tool.apiKeyInfo.paramName && tool.apiKeyInfo.apiKey) {
              // Sử dụng thông tin API key từ tool
              const { paramName, apiKey, schemeName } = tool.apiKeyInfo;

              // Nếu có scheme name, sử dụng định dạng "Scheme-Name param-value"
              if (schemeName && schemeName !== 'default') {
                extra.headers = {
                  ...(extra.headers || {}),
                  [paramName]: `${apiKey}`
                };
                this.logger.debug(`Sử dụng API key với scheme: ${schemeName} ${apiKey}`);
              } else {
                // Nếu không có scheme name hoặc là 'default', chỉ sử dụng giá trị
                extra.headers = {
                  ...(extra.headers || {}),
                  [paramName]: apiKey
                };
                this.logger.debug(`Sử dụng API key không có scheme: ${apiKey}`);
              }
            }

            newTool = {
              parameter: {
                name: tool.name,
                description: tool.description,
                inputSchema: this.convertParametersToInputSchema(parameters, tool.type),
              },
              extra: extra,
              typeTool: ToolTypeEnum.OUT_SYSTEM, // Custom tool ngoài hệ thống
            };
          } else {
            // Đối với admin tool, không cần headers
            newTool = {
              parameter: {
                name: tool.name,
                description: tool.description,
                inputSchema: this.convertParametersToInputSchema(parameters, tool.type),
              },
              extra: {
                url: this.getToolUrl(tool),
                method: this.getToolMethod(tool),
              },
              typeTool: ToolTypeEnum.IN_SYSTEM, // Admin tool thuộc hệ thống
            };
          }

          newFormatTools.push(newTool);
        } catch (error) {
          this.logger.error(`Lỗi khi chuyển đổi tool ${tool.id} sang định dạng mới: ${error.message}`);
          // Bỏ qua tool này và tiếp tục với tool tiếp theo
          continue;
        }
      }
    }

    return newFormatTools;
  }

  /**
   * Chuyển đổi parameters của tool sang inputSchema
   * @param parameters Parameters của tool
   * @param toolType Loại tool (không sử dụng trong phiên bản hiện tại)
   * @returns InputSchema
   */
  private convertParametersToInputSchema(parameters: any, _toolType: string): any {
    if (!parameters) {
      return {
        type: 'object',
        properties: {},
        required: [],
      };
    }

    // Tạo cấu trúc inputSchema mới
    const inputSchema = {
      type: 'object',
      required: parameters.required || [],
      properties: {
        query_param: {
          type: 'object',
          description: 'Tham số truy vấn',
          properties: {},
        },
        path_param: {
          type: 'object',
          description: 'Tham số đường dẫn',
          properties: {},
        },
        body: {
          type: 'object',
          description: 'Tham số body',
          properties: {},
        },
      },
      description: parameters.description || 'Tham số đầu vào cho công cụ',
    };

    // Xử lý các tham số theo cấu trúc mới
    if (parameters.properties) {
      // Xử lý query_param
      if (parameters.properties.query_param) {
        inputSchema.properties.query_param.properties = this.processParameterProperties(parameters.properties.query_param);
      }

      // Xử lý path_param
      if (parameters.properties.path_param) {
        inputSchema.properties.path_param.properties = this.processParameterProperties(parameters.properties.path_param);
      }

      // Xử lý body
      if (parameters.properties.body) {
        inputSchema.properties.body.properties = this.processParameterProperties(parameters.properties.body);
      }
    }

    return inputSchema;
  }

  /**
   * Xử lý các thuộc tính của tham số
   * @param paramProperties Thuộc tính của tham số
   * @returns Thuộc tính đã xử lý
   */
  private processParameterProperties(paramProperties: any): any {
    const result = {};

    if (!paramProperties) return result;

    // Nếu paramProperties là một đối tượng có thuộc tính properties
    if (paramProperties.properties) {
      return paramProperties.properties;
    }

    // Nếu paramProperties là một đối tượng thuộc tính trực tiếp
    Object.keys(paramProperties).forEach(key => {
      const property = paramProperties[key];

      result[key] = {
        type: property.type || 'string',
        description: property.description || '',
      };

      // Nếu là mảng, thêm thuộc tính items
      if (property.type === 'array' && property.items) {
        result[key].items = property.items;
      }

      // Thêm các thuộc tính khác nếu có
      if (property.example) {
        result[key].example = property.example;
      }
    });

    return result;
  }

  /**
   * Lấy URL đầy đủ của tool
   * @param tool Thông tin tool
   * @returns URL đầy đủ
   */
  private getToolUrl(tool: ToolResponseDto): string {
    // Nếu là tool tích hợp, sử dụng thông tin tích hợp
    if (tool.integration) {
      return `${tool.integration.baseUrl}${tool.integration.endpoint}`;
    }

    // Nếu là user tool với endpoint
    if (tool.type === 'USER' && tool.endpoint) {
      return tool.endpoint;
    }

    // Mặc định sử dụng URL cố định
    return `http://localhost:3003/api/v1/agent`;
  }

  /**
   * Lấy phương thức HTTP của tool
   * @param tool Thông tin tool
   * @returns Phương thức HTTP
   */
  private getToolMethod(tool: ToolResponseDto): string {
    // Nếu là tool tích hợp, sử dụng thông tin tích hợp
    if (tool.integration) {
      return tool.integration.method;
    }

    // Nếu là user tool với method
    if (tool.type === 'USER' && tool.method) {
      return tool.method;
    }

    // Mặc định sử dụng GET
    return 'GET';
  }

  /**
   * Trích xuất các tham số path từ URL
   * @param url URL cần phân tích
   * @returns Danh sách các tham số path
   */
  private extractPathParamsFromUrl(url: string): string[] {
    const pathParams: string[] = [];
    const regex = /{([^}]+)}/g;
    let match: RegExpExecArray | null;

    while ((match = regex.exec(url)) !== null) {
      pathParams.push(match[1]);
    }

    return pathParams;
  }
}
