import { ApiProperty } from '@nestjs/swagger';

export enum ToolTypeEnum {
  IN_SYSTEM = 'IN_SYSTEM',
  OUT_SYSTEM = 'OUT_SYSTEM',
}

/**
 * DTO cho thông tin về tham số của tool
 */
export class ToolParameterDto {
  /**
   * Tên tool
   */
  @ApiProperty({
    description: 'Tên tool',
    example: 'tool_search',
  })
  name: string;

  /**
   * Mô tả tool
   */
  @ApiProperty({
    description: 'Mô tả tool',
    example: 'Công cụ giúp tìm kiếm thông tin từ nhiều nguồn',
  })
  description: string;

  /**
   * Schema đầu vào của tool
   */
  @ApiProperty({
    description: 'Schema đầu vào của tool',
    example: {
      type: 'object',
      required: ['query'],
      properties: {
        query_param: {
          type: 'object',
          description: '',
          properties: {
            search: {
              type: 'string',
              description: 'search của cart item; example: product 1',
            },
            require: ['search'],
          },
        },
        path_param: {
          type: 'object',
          description: '',
          properties: {
            id: {
              type: 'number',
              description: 'search của cart item; example: product 1',
            },
            require: ['search'],
          },
        },
        body: {
          type: 'object',
          description: '',
          properties: {
            toolId: {
              type: 'array',
              description: 'search của cart item; example: product 1',
              items: {
                type: 'string',
              },
            },
            require: ['search'],
          },
        },
      },
      description: 'Tham số đầu vào cho công cụ tìm kiếm',
    },
  })
  inputSchema: any;
}

/**
 * DTO cho thông tin bổ sung của tool
 */
export class ToolExtraDto {
  /**
   * URL đầy đủ của API
   */
  @ApiProperty({
    description: 'URL đầy đủ của API',
    example: 'http://localhost:3003/api/v1/agent',
  })
  url: string;

  /**
   * Headers cần thiết (tùy chọn cho user tool)
   */
  @ApiProperty({
    description: 'Headers cần thiết',
    example: {
      'x-api-key': 'redai_uuXkRZH0AIjWeqWUTv+5GA==:e9jt8wvIRDDVJkCBSkIWFZrhtJc4oA2TdAHhrUhJBJF4ZM8H3Yx9oYLP1zQQszhT1SrPn6312rMdK+uT/Zkhsg==',
    },
    required: false,
  })
  headers?: Record<string, string>;

  /**
   * Phương thức HTTP
   */
  @ApiProperty({
    description: 'Phương thức HTTP',
    example: 'GET',
  })
  method: string;
}

/**
 * DTO cho thông tin về tool trong định dạng mới
 */
export class NewToolResponseDto {
  /**
   * Thông tin về tham số của tool
   */
  @ApiProperty({
    description: 'Thông tin về tham số của tool',
    type: ToolParameterDto,
  })
  parameter: ToolParameterDto;

  /**
   * Thông tin bổ sung của tool
   */
  @ApiProperty({
    description: 'Thông tin bổ sung của tool',
    type: ToolExtraDto,
  })
  extra: ToolExtraDto;

  @ApiProperty({
    description: 'ID của tool',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  typeTool: ToolTypeEnum;
}

/**
 * DTO cho response lấy danh sách tool của agent trong định dạng mới
 */
export class AgentToolsNewResponseDto {
  /**
   * Danh sách tool
   */
  @ApiProperty({
    description: 'Danh sách tool',
    type: [NewToolResponseDto],
  })
  tools: NewToolResponseDto[];
}
