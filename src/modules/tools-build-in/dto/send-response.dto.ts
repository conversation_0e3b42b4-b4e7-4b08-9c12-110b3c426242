import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho việc trả về thông tin người nhận
 */
export class RecipientResponseDto {
  @ApiProperty({
    description: 'Email người nhận',
    example: '<EMAIL>',
    nullable: true
  })
  email?: string;

  @ApiProperty({
    description: 'Số điện thoại người nhận',
    example: '0987654321',
    nullable: true
  })
  phone?: string;

  @ApiProperty({
    description: 'Tên người nhận',
    example: 'Nguyễn Văn A',
    nullable: true
  })
  name?: string;

  @ApiProperty({
    description: 'ID người nhận (nếu là người dùng trong hệ thống)',
    example: 1,
    nullable: true
  })
  userId?: number;

  @ApiProperty({
    description: 'ID khách hàng chuyển đổi',
    example: 2,
    nullable: true
  })
  convertCustomerId?: number;
}

/**
 * DTO cho việc trả về kết quả gửi
 */
export class SendResultResponseDto {
  @ApiProperty({
    description: 'Trạng thái thành công',
    example: true
  })
  success: boolean;

  @ApiProperty({
    description: 'Thông báo',
    example: 'Gửi email thành công'
  })
  message: string;

  @ApiProperty({
    description: 'ID của tin nhắn đã gửi',
    example: 'msg_123456789',
    nullable: true
  })
  messageId?: string;

  @ApiProperty({
    description: 'Thời gian gửi',
    example: 1672531200000,
    nullable: true
  })
  sentAt?: number;

  @ApiProperty({
    description: 'Danh sách người nhận thành công',
    type: [RecipientResponseDto],
    nullable: true
  })
  successfulRecipients?: RecipientResponseDto[];

  @ApiProperty({
    description: 'Danh sách người nhận thất bại',
    type: [RecipientResponseDto],
    nullable: true
  })
  failedRecipients?: RecipientResponseDto[];
}

/**
 * DTO cho việc trả về thông tin email đã gửi
 */
export class EmailResponseDto {
  @ApiProperty({
    description: 'ID của email',
    example: 'email_123456789'
  })
  id: string;

  @ApiProperty({
    description: 'Người nhận',
    type: [RecipientResponseDto]
  })
  recipients: RecipientResponseDto[];

  @ApiProperty({
    description: 'Tiêu đề email',
    example: 'Thông báo khuyến mãi tháng 12'
  })
  subject: string;

  @ApiProperty({
    description: 'Trạng thái gửi',
    example: 'delivered'
  })
  status: string;

  @ApiProperty({
    description: 'Thời gian gửi',
    example: 1672531200000
  })
  sentAt: number;

  @ApiProperty({
    description: 'Thời gian mở email',
    example: 1672531800000,
    nullable: true
  })
  openedAt?: number;

  @ApiProperty({
    description: 'Số lần mở',
    example: 2,
    nullable: true
  })
  openCount?: number;
}

/**
 * DTO cho việc trả về thông tin SMS đã gửi
 */
export class SmsResponseDto {
  @ApiProperty({
    description: 'ID của SMS',
    example: 'sms_123456789'
  })
  id: string;

  @ApiProperty({
    description: 'Người nhận',
    type: [RecipientResponseDto]
  })
  recipients: RecipientResponseDto[];

  @ApiProperty({
    description: 'Nội dung SMS',
    example: 'Mã xác nhận của bạn là 123456'
  })
  content: string;

  @ApiProperty({
    description: 'Trạng thái gửi',
    example: 'delivered'
  })
  status: string;

  @ApiProperty({
    description: 'Thời gian gửi',
    example: 1672531200000
  })
  sentAt: number;

  @ApiProperty({
    description: 'Loại SMS',
    example: 'otp',
    nullable: true
  })
  type?: string;
}
