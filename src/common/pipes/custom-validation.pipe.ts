import { ValidationPipe, ValidationError, BadRequestException } from '@nestjs/common';
import { AppException, ErrorCode } from '@common/exceptions';
import { TOOLS_ERROR_CODES } from '@modules/tools/exceptions';

/**
 * Custom ValidationPipe để chuyển đổi lỗi validation thành AppException
 */
export class CustomValidationPipe extends ValidationPipe {
  constructor() {
    super({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      exceptionFactory: (errors: ValidationError[]) => {
        // Tìm lỗi liên quan đến toolName và pattern
        const toolNamePatternError = this.findToolNamePatternError(errors);

        // Nếu có lỗi toolName pattern, ném AppException với mã lỗi cụ thể
        if (toolNamePatternError) {
          throw new AppException(
            TOOLS_ERROR_CODES.TOOL_NAME_INVALID,
            'Tên tool chỉ được chứa a-z, A-Z, 0-9, hoặc dấu gạch dưới',
            { property: 'toolName', errors: toolNamePatternError }
          );
        }

        // Nếu không phải lỗi toolName pattern, sử dụng xử lý mặc định
        const formattedErrors = this.formatErrors(errors);
        console.log('Validation errors:', JSON.stringify(formattedErrors, null, 2));
        return new BadRequestException({
          message: 'Validation failed',
          errors: formattedErrors
        });
      },
    });
  }

  /**
   * Tìm lỗi liên quan đến toolName và pattern
   * @param errors Danh sách lỗi validation
   * @returns Lỗi toolName pattern nếu tìm thấy, null nếu không tìm thấy
   */
  private findToolNamePatternError(errors: ValidationError[]): string[] | null {
    for (const error of errors) {
      if (error.property === 'toolName' && error.constraints && error.constraints.matches) {
        return [error.constraints.matches];
      }

      // Tìm kiếm đệ quy trong các lỗi con
      if (error.children && error.children.length > 0) {
        const childError = this.findToolNamePatternError(error.children);
        if (childError) {
          return childError;
        }
      }
    }

    return null;
  }

  /**
   * Format lỗi validation thành dạng dễ đọc
   * @param errors Danh sách lỗi validation
   * @returns Danh sách lỗi đã được format
   */
  private formatErrors(errors: ValidationError[]): any[] {
    return errors.map(error => {
      const errorInfo: any = {
        property: error.property,
        value: error.value,
        constraints: error.constraints || {},
      };

      if (error.constraints) {
        errorInfo.messages = Object.values(error.constraints);
      }

      if (error.children && error.children.length > 0) {
        errorInfo.children = this.formatErrors(error.children);
      }

      return errorInfo;
    });
  }
}
