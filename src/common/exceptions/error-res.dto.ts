import { ErrorCode } from '@/common';

/**
 * Interface định nghĩa cấu trúc response lỗi chuẩn
 * Được sử dụng để đảm bảo tính nhất quán trong các response lỗi
 */
export interface ErrorResponse {
  /**
   * Mã lỗi từ ErrorCode enum
   * Dùng để xác định loại lỗi một cách chính xác
   */
  code: ErrorCode;

  /**
   * Thông báo lỗi dành cho người dùng
   * Nên rõ ràng và hữu ích
   */
  message: string;

  /**
   * Thông tin chi tiết về lỗi
   * Có thể là object với các trường cụ thể hoặc mảng các lỗi
   */
  details?: any;

  /**
   * Thời điểm xảy ra lỗi
   * Định dạng ISO string
   */
  timestamp: string;

  /**
   * Đường dẫn API gây ra lỗi
   */
  path?: string;

  /**
   * ID định danh duy nhất cho request
   * Dùng để theo dõi và debug lỗi
   */
  requestId?: string;
}
