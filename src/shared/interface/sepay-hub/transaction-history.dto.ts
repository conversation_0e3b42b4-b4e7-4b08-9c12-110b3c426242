/**
 * Enum đại diện cho các loại giao dịch
 */
export enum TransferType {
  CREDIT = 'credit', // Tiền vào
  DEBIT = 'debit',   // Tiền ra
}

/**
 * DTO đại diện cho thông tin lịch sử giao dịch từ SePay Hub
 */
export interface TransactionHistoryDto {
  /**
   * ID giao dịch (deprecated)
   */
  id?: string;

  /**
   * ID giao dịch
   */
  transaction_id: string;

  /**
   * Thời gian nhận giao dịch (định dạng Y-m-d H:i:s)
   */
  transaction_date: string;

  /**
   * ID tài khoản ngân hàng sở hữu giao dịch
   */
  bank_account_id: string;

  /**
   * Số tài khoản ngân hàng
   */
  account_number: string;

  /**
   * ID công ty (tổ chức) sở hữu tài khoản ngân hàng
   */
  company_id: string;

  /**
   * ID ngân hàng
   */
  bank_id: string;

  /**
   * ID VA (nếu có)
   */
  va_id?: string;

  /**
   * Số VA
   */
  va?: string;

  /**
   * Mã tham chiếu FT
   */
  reference_number: string;

  /**
   * Nội dung giao dịch
   */
  transaction_content: string;

  /**
   * Mã thanh toán (nếu có)
   */
  payment_code?: string;

  /**
   * Loại giao dịch (credit: tiền vào, debit: tiền ra)
   */
  transfer_type: TransferType;

  /**
   * Số tiền giao dịch
   */
  amount: string;
}
