import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { Logger } from '@nestjs/common';

describe('DatabaseModule (e2e)', () => {
  let module: TestingModule;
  let dataSource: DataSource;
  const logger = new Logger('DatabaseE2ETest');

  // Mock database config for e2e test
  const mockDatabaseConfig = {
    host: 'localhost',
    port: 5432,
    username: 'test_user',
    password: 'test_password',
    database: 'test_db',
    ssl: false,
  };

  beforeAll(async () => {
    // Create a testing module with TypeOrmModule
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'postgres',
          host: mockDatabaseConfig.host,
          port: mockDatabaseConfig.port,
          username: mockDatabaseConfig.username,
          password: mockDatabaseConfig.password,
          database: mockDatabaseConfig.database,
          autoLoadEntities: true,
          synchronize: false,
          ssl: {
            rejectUnauthorized: !mockDatabaseConfig.ssl,
          },
          // Use in-memory database for testing
        }),
      ],
    }).compile();

    // Get the DataSource instance
    dataSource = module.get<DataSource>(DataSource);
  });

  afterAll(async () => {
    // Close the connection after all tests
    if (dataSource && dataSource.isInitialized) {
      await dataSource.destroy();
    }
    await module.close();
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
    expect(dataSource).toBeDefined();
  });

  it('should connect to the database successfully', async () => {
    // Check if the connection is established
    expect(dataSource.isInitialized).toBe(true);

    // Log connection info
    const { host, port, username, database } = dataSource.options as any;
    logger.log(`Connected to database: ${database} on ${host}:${port} as ${username}`);

    // Try to execute a simple query
    try {
      const result = await dataSource.query('SELECT 1 as value');
      expect(result).toBeDefined();
      expect(result[0].value).toBe(1);
      logger.log('Successfully executed test query');
    } catch (error) {
      logger.error(`Failed to execute test query: ${error.message}`);
      throw error;
    }
  });

  it('should have migrations configured', () => {
    // Check if migrations are configured
    const options = dataSource.options as any;

    if (options.migrations) {
      expect(options.migrations).toBeDefined();

      // Log migrations path
      logger.log(`Migrations configured: ${JSON.stringify(options.migrations)}`);
    } else {
      logger.log('No migrations explicitly configured in DataSource options');
    }
  });

  it('should have entities configured', async () => {
    // Check if entities are loaded
    const entities = dataSource.entityMetadatas;
    expect(entities.length).toBeGreaterThan(0);

    // Log loaded entities
    const entityNames = entities.map(entity => entity.name);
    logger.log(`Loaded entities: ${entityNames.join(', ')}`);

    // Try to get metadata for each entity
    for (const entity of entities) {
      expect(entity.columns.length).toBeGreaterThan(0);
      logger.log(`Entity ${entity.name} has ${entity.columns.length} columns`);
    }
  });
});
