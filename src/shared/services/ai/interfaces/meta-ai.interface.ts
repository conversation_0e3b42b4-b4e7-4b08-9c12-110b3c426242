/**
 * Interface cho Meta AI Model
 */
export interface MetaAIModel {
  id: string;
  name?: string;
  version?: string;
  description?: string;
  context_length?: number;
  capabilities?: string[];
  parameters?: number;
  created_at?: string;
}

/**
 * Interface cho Meta AI Models Response
 */
export interface MetaAIModelsResponse {
  data: MetaAIModel[];
  object: string;
}

/**
 * Interface cho tham số tạo fine-tuning job của Meta AI
 */
export interface MetaAIFineTuningParams {
  /** ID của model cơ sở để fine-tune (ví dụ: llama-3-8b-instruct) */
  model: string;
  /** Tên hiển thị cho model fine-tuned */
  suffix?: string;
  /** ID của file training data đã upload lên Meta AI */
  trainingFileId: string;
  /** ID của file validation data đã upload lên Meta AI (tùy chọn) */
  validationFileId?: string;
  /** Siêu tham số cho quá trình fine-tuning */
  hyperparameters?: {
    /** Số epoch để huấn luyện (mặc định: 3, tối đa: 20) */
    nEpochs?: number;
    /** Kích thước batch (mặc định: auto) */
    batchSize?: number | 'auto';
    /** Tốc độ học (mặc định: auto) */
    learningRateMultiplier?: number | 'auto';
  };
}

/**
 * Interface cho kết quả tạo fine-tuning job của Meta AI
 */
export interface MetaAIFineTuningResponse {
  /** ID của fine-tuning job */
  id: string;
  /** Loại đối tượng */
  object: string;
  /** ID của model cơ sở */
  model: string;
  /** Thời gian tạo (timestamp) */
  created_at: number;
  /** Thời gian cập nhật cuối cùng (timestamp) */
  updated_at: number;
  /** Trạng thái của job */
  status: 'validating_files' | 'queued' | 'running' | 'succeeded' | 'failed' | 'cancelled';
  /** ID của file training data */
  training_file: string;
  /** ID của file validation data (nếu có) */
  validation_file?: string;
  /** Thông tin về lỗi (nếu có) */
  error?: {
    /** Mã lỗi */
    code: string;
    /** Thông báo lỗi */
    message: string;
  };
  /** Siêu tham số đã sử dụng */
  hyperparameters: {
    /** Số epoch để huấn luyện */
    n_epochs: number;
    /** Kích thước batch */
    batch_size: number | 'auto';
    /** Tốc độ học */
    learning_rate_multiplier: number | 'auto';
  };
  /** ID của model fine-tuned (chỉ có khi job hoàn thành) */
  fine_tuned_model?: string;
  /** Kết quả của quá trình fine-tuning */
  result_files?: string[];
  /** Số lượng token đã xử lý */
  trained_tokens?: number;
  /** Thời gian hoàn thành (timestamp) */
  finished_at?: number;
  /** Tổ chức sở hữu */
  organization_id: string;
}
