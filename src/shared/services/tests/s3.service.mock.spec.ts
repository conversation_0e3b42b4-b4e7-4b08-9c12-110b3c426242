import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';

// Mock classes and interfaces
class MockAppException extends Error {
  constructor(public readonly errorCode: any, message: string) {
    super(message);
  }
}

const ErrorCode = {
  CLOUD_FLARE_ERROR_UPLOAD: { code: 10007, message: 'Error uploading to CloudFlare R2' },
  CLOUD_FLARE_ERROR_DELETE: { code: 10008, message: 'Error deleting from CloudFlare R2' },
  CLOUD_FLARE_ERROR_DOWNLOAD: { code: 10009, message: 'Error downloading from CloudFlare R2' },
  CLOUD_FLARE_ERROR_COPY: { code: 10010, message: 'Error copying in CloudFlare R2' },
};

enum TimeIntervalEnum {
  FIFTEEN_MINUTES = 15 * 60 * 1000,
  THIRTY_MINUTES = 30 * 60 * 1000,
  ONE_HOUR = 60 * 60 * 1000,
  TWO_HOURS = 2 * 60 * 60 * 1000,
  ONE_DAY = 24 * 60 * 60 * 1000,
}

enum CategoryFolderEnum {
  IMAGE = 'images',
  DOCUMENT = 'documents',
  VIDEO = 'videos',
  OTHER = 'others'
}

enum ImageTypeEnum {
  JPEG = 'image/jpeg',
  PNG = 'image/png',
  GIF = 'image/gif',
  SVG = 'image/svg+xml',
  WEBP = 'image/webp',
}

type MediaType = string;

// Mock AWS SDK classes
class S3Client {
  send = jest.fn();
}

class PutObjectCommand {
  constructor(public readonly input: any) {}
}

class GetObjectCommand {
  constructor(public readonly input: any) {}
}

class DeleteObjectCommand {
  constructor(public readonly input: any) {}
}

class DeleteObjectsCommand {
  constructor(public readonly input: any) {}
}

class CopyObjectCommand {
  constructor(public readonly input: any) {}
}

// Mock ConfigService
class ConfigService {
  getConfig = jest.fn();
}

// Mock getSignedUrl function
const getSignedUrl = jest.fn().mockResolvedValue('https://test-presigned-url.com');

// S3Service implementation
class S3Service {
  private readonly logger = new Logger(S3Service.name);
  private readonly s3Client: S3Client;
  private readonly bucketName: string;
  private readonly cdnUrl: string;

  constructor(private readonly configService: ConfigService) {
    this.s3Client = new S3Client();
    const storageConfig = this.configService.getConfig('storage');
    this.bucketName = storageConfig.cloudflare.bucketName;
    this.cdnUrl = storageConfig.cdn.url;
  }

  async createPresignedWithID(key: string, expirationTimeInMillis: TimeIntervalEnum, type: MediaType, maxSize: number): Promise<string> {
    try {
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        ContentType: type,
        Metadata: {
          'x-amz-meta-max-file-size': maxSize.toString(),
          'x-amz-meta-media-type': type,
        },
      });

      return await getSignedUrl(this.s3Client, command, {
        expiresIn: expirationTimeInMillis / 1000, // Convert to seconds
      });
    } catch (error) {
      this.logger.error('Cloudflare R2 Error: ', error);
      throw new MockAppException(ErrorCode.CLOUD_FLARE_ERROR_UPLOAD, 'Failed to create presigned URL');
    }
  }

  async deleteFile(key: string): Promise<boolean> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete file from S3/R2: ${error.message}`, error.stack);
      throw new MockAppException(ErrorCode.CLOUD_FLARE_ERROR_DELETE, `Failed to delete file: ${error.message}`);
    }
  }

  async deleteFiles(keys: string[]): Promise<{
    deleted: (string | undefined)[];
    errors: { key: string | undefined; message: string }[];
  }> {
    if (!keys || keys.length === 0) {
      return { deleted: [], errors: [] };
    }

    try {
      const objects = keys.map(key => ({ Key: key }));
      const command = new DeleteObjectsCommand({
        Bucket: this.bucketName,
        Delete: {
          Objects: objects,
        },
      });

      const response = await this.s3Client.send(command);
      const deleted = response.Deleted?.map(item => item.Key) || [];
      const errors = response.Errors?.map(error => ({
        key: error.Key,
        message: error.Message || 'Unknown error',
      })) || [];

      return { deleted, errors };
    } catch (error) {
      this.logger.error(`Failed to delete multiple files from S3/R2: ${error.message}`, error.stack);
      throw new MockAppException(ErrorCode.CLOUD_FLARE_ERROR_DELETE, `Failed to delete multiple files: ${error.message}`);
    }
  }

  getDownloadUrl(key: string): string {
    if (!key || typeof key !== 'string') {
      this.logger.warn(`Invalid key provided: ${key}`);
      return '';
    }

    const normalizedKey = key.startsWith('/') ? key.substring(1) : key;
    return `${this.cdnUrl}/${normalizedKey}`;
  }

  getDownloadUrls(keys: string[]): string[] {
    if (!keys || !Array.isArray(keys) || keys.length === 0) {
      this.logger.warn('Empty or invalid keys array provided');
      return [];
    }

    return keys.map(key => this.getDownloadUrl(key));
  }

  async createPresignedDownloadUrl(key: string, expirationTimeInMillis: TimeIntervalEnum): Promise<string> {
    try {
      const normalizedKey = key.startsWith('/') ? key.substring(1) : key;
      
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: normalizedKey,
      });

      return await getSignedUrl(this.s3Client, command, {
        expiresIn: expirationTimeInMillis / 1000, // Convert to seconds
      });
    } catch (error) {
      this.logger.error(`Failed to create presigned download URL for ${key}: ${error.message}`, error.stack);
      throw new MockAppException(ErrorCode.CLOUD_FLARE_ERROR_DOWNLOAD, `Failed to create presigned download URL: ${error.message}`);
    }
  }

  async createPresignedDownloadUrls(keys: string[], expirationTimeInMillis: TimeIntervalEnum): Promise<{ [key: string]: string }> {
    if (!keys || !Array.isArray(keys) || keys.length === 0) {
      return {};
    }

    const result: { [key: string]: string } = {};

    await Promise.all(
      keys.map(async (key) => {
        try {
          const url = await this.createPresignedDownloadUrl(key, expirationTimeInMillis);
          result[key] = url;
        } catch (error) {
          this.logger.error(`Failed to create presigned URL for ${key}: ${error.message}`);
        }
      })
    );

    return result;
  }

  async copyFile(
    sourceKey: string,
    destinationKey: string,
    destinationBucket?: string
  ): Promise<{ etag: string; lastModified: Date }> {
    try {
      const normalizedSourceKey = sourceKey.startsWith('/') ? sourceKey.substring(1) : sourceKey;
      const normalizedDestinationKey = destinationKey.startsWith('/') ? destinationKey.substring(1) : destinationKey;
      
      const targetBucket = destinationBucket || this.bucketName;
      
      const command = new CopyObjectCommand({
        Bucket: targetBucket,
        Key: normalizedDestinationKey,
        CopySource: `${this.bucketName}/${normalizedSourceKey}`,
      });

      const response = await this.s3Client.send(command);

      return {
        etag: response.CopyObjectResult?.ETag?.replace(/\"/g, '') || '',
        lastModified: response.CopyObjectResult?.LastModified || new Date(),
      };
    } catch (error) {
      this.logger.error(
        `Failed to copy file from ${sourceKey} to ${destinationKey}: ${error.message}`,
        error.stack
      );

      throw new MockAppException(
        ErrorCode.CLOUD_FLARE_ERROR_COPY,
        `Failed to copy file: ${error.message}`
      );
    }
  }

  async copyFiles(copyOperations: { sourceKey: string; destinationKey: string; destinationBucket?: string }[]): Promise<{
    copied: { sourceKey: string; destinationKey: string; etag: string }[];
    errors: { sourceKey: string; destinationKey: string; message: string }[];
  }> {
    if (!copyOperations || !Array.isArray(copyOperations) || copyOperations.length === 0) {
      return { copied: [], errors: [] };
    }

    const copied: { sourceKey: string; destinationKey: string; etag: string }[] = [];
    const errors: { sourceKey: string; destinationKey: string; message: string }[] = [];

    await Promise.all(
      copyOperations.map(async ({ sourceKey, destinationKey, destinationBucket }) => {
        try {
          const result = await this.copyFile(sourceKey, destinationKey, destinationBucket);
          copied.push({
            sourceKey,
            destinationKey,
            etag: result.etag,
          });
        } catch (error) {
          errors.push({
            sourceKey,
            destinationKey,
            message: error.message || 'Unknown error',
          });
        }
      })
    );

    return { copied, errors };
  }
}

describe('S3Service', () => {
  let service: S3Service;
  let mockS3Client: S3Client;
  let mockConfigService: ConfigService;

  const mockStorageConfig = {
    cloudflare: {
      bucketName: 'test-bucket',
      accessKey: 'test-access-key',
      secretKey: 'test-secret-key',
      endpoint: 'https://test-endpoint.com',
    },
    cdn: {
      url: 'https://test-cdn.com',
    },
  };

  beforeEach(async () => {
    // Create mock for ConfigService
    mockConfigService = new ConfigService();
    mockConfigService.getConfig.mockReturnValue(mockStorageConfig);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: S3Service,
          useFactory: () => new S3Service(mockConfigService),
        },
      ],
    }).compile();

    service = module.get<S3Service>(S3Service);
    mockS3Client = (service as any).s3Client;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createPresignedWithID', () => {
    it('should create a presigned URL for upload', async () => {
      // Arrange
      const key = 'test-key';
      const expirationTime = TimeIntervalEnum.FIFTEEN_MINUTES;
      const type = ImageTypeEnum.JPEG;
      const maxSize = 5 * 1024 * 1024; // 5MB

      // Act
      const result = await service.createPresignedWithID(key, expirationTime, type, maxSize);

      // Assert
      expect(result).toBe('https://test-presigned-url.com');
      expect(getSignedUrl).toHaveBeenCalled();
      expect(getSignedUrl.mock.calls[0][1].input).toEqual({
        Bucket: mockStorageConfig.cloudflare.bucketName,
        Key: key,
        ContentType: type,
        Metadata: {
          'x-amz-meta-max-file-size': maxSize.toString(),
          'x-amz-meta-media-type': type,
        },
      });
    });
  });

  describe('deleteFile', () => {
    it('should delete a file successfully', async () => {
      // Arrange
      const key = 'test-key';
      mockS3Client.send.mockResolvedValueOnce({});

      // Act
      const result = await service.deleteFile(key);

      // Assert
      expect(mockS3Client.send).toHaveBeenCalled();
      expect(mockS3Client.send.mock.calls[0][0].input).toEqual({
        Bucket: mockStorageConfig.cloudflare.bucketName,
        Key: key,
      });
      expect(result).toBe(true);
    });

    it('should throw an exception when there is an error', async () => {
      // Arrange
      const key = 'test-key';
      const errorMessage = 'Test error';
      mockS3Client.send.mockRejectedValueOnce(new Error(errorMessage));

      // Act & Assert
      await expect(service.deleteFile(key)).rejects.toThrow(errorMessage);
    });
  });

  describe('deleteFiles', () => {
    it('should delete multiple files successfully', async () => {
      // Arrange
      const keys = ['test-key-1', 'test-key-2'];
      const mockResponse = {
        Deleted: [
          { Key: 'test-key-1' },
          { Key: 'test-key-2' },
        ],
        Errors: [],
      };
      mockS3Client.send.mockResolvedValueOnce(mockResponse);

      // Act
      const result = await service.deleteFiles(keys);

      // Assert
      expect(mockS3Client.send).toHaveBeenCalled();
      expect(mockS3Client.send.mock.calls[0][0].input).toEqual({
        Bucket: mockStorageConfig.cloudflare.bucketName,
        Delete: {
          Objects: [
            { Key: 'test-key-1' },
            { Key: 'test-key-2' },
          ],
        },
      });
      expect(result.deleted).toEqual(['test-key-1', 'test-key-2']);
      expect(result.errors).toEqual([]);
    });

    it('should handle exceptions when deleting files', async () => {
      // Arrange
      const keys = ['test-key-1', 'test-key-2'];
      const mockResponse = {
        Deleted: [{ Key: 'test-key-1' }],
        Errors: [{ Key: 'test-key-2', Message: 'Access denied' }],
      };
      mockS3Client.send.mockResolvedValueOnce(mockResponse);

      // Act
      const result = await service.deleteFiles(keys);

      // Assert
      expect(mockS3Client.send).toHaveBeenCalled();
      expect(result.deleted).toEqual(['test-key-1']);
      expect(result.errors).toEqual([{ key: 'test-key-2', message: 'Access denied' }]);
    });

    it('should return empty arrays when no keys are provided', async () => {
      // Act
      const result = await service.deleteFiles([]);

      // Assert
      expect(mockS3Client.send).not.toHaveBeenCalled();
      expect(result.deleted).toEqual([]);
      expect(result.errors).toEqual([]);
    });
  });

  describe('getDownloadUrl', () => {
    it('should return a download URL', () => {
      // Arrange
      const key = 'test-key';
      const expectedUrl = `${mockStorageConfig.cdn.url}/${key}`;

      // Act
      const result = service.getDownloadUrl(key);

      // Assert
      expect(result).toBe(expectedUrl);
    });

    it('should normalize the key by removing leading slash', () => {
      // Arrange
      const key = '/test-key';
      const normalizedKey = 'test-key';
      const expectedUrl = `${mockStorageConfig.cdn.url}/${normalizedKey}`;

      // Act
      const result = service.getDownloadUrl(key);

      // Assert
      expect(result).toBe(expectedUrl);
    });

    it('should return empty string for invalid key', () => {
      // Act
      const result = service.getDownloadUrl('');

      // Assert
      expect(result).toBe('');
    });
  });

  describe('getDownloadUrls', () => {
    it('should return download URLs for multiple keys', () => {
      // Arrange
      const keys = ['test-key-1', 'test-key-2'];
      const expectedUrls = [
        `${mockStorageConfig.cdn.url}/test-key-1`,
        `${mockStorageConfig.cdn.url}/test-key-2`,
      ];

      // Act
      const result = service.getDownloadUrls(keys);

      // Assert
      expect(result).toEqual(expectedUrls);
    });

    it('should return empty array for empty keys array', () => {
      // Act
      const result = service.getDownloadUrls([]);

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe('createPresignedDownloadUrl', () => {
    it('should create a presigned URL for download', async () => {
      // Arrange
      const key = 'test-key';
      const expirationTime = TimeIntervalEnum.FIFTEEN_MINUTES;

      // Act
      const result = await service.createPresignedDownloadUrl(key, expirationTime);

      // Assert
      expect(getSignedUrl).toHaveBeenCalled();
      expect(getSignedUrl.mock.calls[0][1].input).toEqual({
        Bucket: mockStorageConfig.cloudflare.bucketName,
        Key: key,
      });
      expect(result).toBe('https://test-presigned-url.com');
    });
  });

  describe('copyFile', () => {
    it('should copy a file successfully', async () => {
      // Arrange
      const sourceKey = 'source-key';
      const destinationKey = 'destination-key';
      const mockResponse = {
        CopyObjectResult: {
          ETag: '"test-etag"',
          LastModified: new Date(),
        },
      };
      mockS3Client.send.mockResolvedValueOnce(mockResponse);

      // Act
      const result = await service.copyFile(sourceKey, destinationKey);

      // Assert
      expect(mockS3Client.send).toHaveBeenCalled();
      expect(mockS3Client.send.mock.calls[0][0].input).toEqual({
        Bucket: mockStorageConfig.cloudflare.bucketName,
        Key: destinationKey,
        CopySource: `${mockStorageConfig.cloudflare.bucketName}/${sourceKey}`,
      });
      expect(result.etag).toBe('test-etag');
      expect(result.lastModified).toBeInstanceOf(Date);
    });

    it('should copy a file to a different bucket', async () => {
      // Arrange
      const sourceKey = 'source-key';
      const destinationKey = 'destination-key';
      const destinationBucket = 'destination-bucket';
      const mockResponse = {
        CopyObjectResult: {
          ETag: '"test-etag"',
          LastModified: new Date(),
        },
      };
      mockS3Client.send.mockResolvedValueOnce(mockResponse);

      // Act
      const result = await service.copyFile(sourceKey, destinationKey, destinationBucket);

      // Assert
      expect(mockS3Client.send).toHaveBeenCalled();
      expect(mockS3Client.send.mock.calls[0][0].input).toEqual({
        Bucket: destinationBucket,
        Key: destinationKey,
        CopySource: `${mockStorageConfig.cloudflare.bucketName}/${sourceKey}`,
      });
      expect(result.etag).toBe('test-etag');
      expect(result.lastModified).toBeInstanceOf(Date);
    });
  });
});
