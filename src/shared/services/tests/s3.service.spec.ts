import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { ConfigService } from '../../../config';
import { S3Service } from '../s3.service';
import {
  CopyObjectCommand,
  DeleteObjectCommand,
  DeleteObjectsCommand,
  GetObjectCommand,
  PutObjectCommand,
  S3Client
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { TimeIntervalEnum } from '../../utils';
import { AppException, ErrorCode } from '@common/exceptions/app.exception';
import { ImageTypeEnum, MediaType } from '../../utils/file';

// Mock AWS SDK
jest.mock('@aws-sdk/client-s3');
jest.mock('@aws-sdk/s3-request-presigner');

describe('S3Service', () => {
  let service: S3Service;
  let mockS3Client: any;
  let mockConfigService: jest.Mocked<ConfigService>;

  const mockStorageConfig = {
    cloudflare: {
      bucketName: 'test-bucket',
      accessKey: 'test-access-key',
      secretKey: 'test-secret-key',
      endpoint: 'https://test-endpoint.com',
    },
    cdn: {
      url: 'https://test-cdn.com',
    },
  };

  beforeEach(async () => {
    // Create mock for S3Client
    mockS3Client = {
      send: jest.fn(),
    };

    // Create mock for ConfigService
    mockConfigService = {
      getConfig: jest.fn().mockReturnValue(mockStorageConfig),
    } as unknown as jest.Mocked<ConfigService>;

    // Mock getSignedUrl
    (getSignedUrl as jest.Mock).mockResolvedValue('https://test-presigned-url.com');

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        S3Service,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: S3Client,
          useValue: mockS3Client,
        },
        {
          provide: Logger,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<S3Service>(S3Service);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createPresignedWithID', () => {
    it('should create a presigned URL for upload', async () => {
      // Arrange
      const key = 'test-key';
      const expirationTime = TimeIntervalEnum.FIFTEEN_MINUTES;
      const type = ImageTypeEnum.JPEG;
      const maxSize = 5 * 1024 * 1024; // 5MB

      // Act
      const result = await service.createPresignedWithID(key, expirationTime, type, maxSize);

      // Assert
      expect(PutObjectCommand).toHaveBeenCalledWith({
        Bucket: mockStorageConfig.cloudflare.bucketName,
        Key: key,
        ContentType: type,
        Metadata: {
          'x-amz-meta-max-file-size': maxSize.toString(),
          'x-amz-meta-media-type': type,
        },
      });
      expect(getSignedUrl).toHaveBeenCalled();
      expect(result).toBe('https://test-presigned-url.com');
    });

    it('should throw an exception when there is an error', async () => {
      // Arrange
      const key = 'test-key';
      const expirationTime = TimeIntervalEnum.FIFTEEN_MINUTES;
      const type = ImageTypeEnum.JPEG;
      const maxSize = 5 * 1024 * 1024; // 5MB
      const errorMessage = 'Test error';

      (getSignedUrl as jest.Mock).mockRejectedValueOnce(new Error(errorMessage));

      // Act & Assert
      await expect(service.createPresignedWithID(key, expirationTime, type, maxSize))
        .rejects
        .toThrow(AppException);
    });
  });

  describe('deleteFile', () => {
    it('should delete a file successfully', async () => {
      // Arrange
      const key = 'test-key';
      mockS3Client.send.mockResolvedValueOnce({} as any);

      // Act
      const result = await service.deleteFile(key);

      // Assert
      expect(DeleteObjectCommand).toHaveBeenCalledWith({
        Bucket: mockStorageConfig.cloudflare.bucketName,
        Key: key,
      });
      expect(mockS3Client.send).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('should throw an exception when there is an error', async () => {
      // Arrange
      const key = 'test-key';
      const errorMessage = 'Test error';
      mockS3Client.send.mockRejectedValueOnce(new Error(errorMessage) as never);

      // Act & Assert
      await expect(service.deleteFile(key))
        .rejects
        .toThrow(AppException);
    });
  });

  describe('deleteFiles', () => {
    it('should delete multiple files successfully', async () => {
      // Arrange
      const keys = ['test-key-1', 'test-key-2'];
      const mockResponse = {
        Deleted: [
          { Key: 'test-key-1' },
          { Key: 'test-key-2' },
        ],
        Errors: [],
      };
      mockS3Client.send.mockResolvedValueOnce(mockResponse as any);

      // Act
      const result = await service.deleteFiles(keys);

      // Assert
      expect(DeleteObjectsCommand).toHaveBeenCalled();
      expect(mockS3Client.send).toHaveBeenCalled();
      expect(result.deleted).toEqual(['test-key-1', 'test-key-2']);
      expect(result.errors).toEqual([]);
    });

    it('should handle exceptions when deleting files', async () => {
      // Arrange
      const keys = ['test-key-1', 'test-key-2'];
      const mockResponse = {
        Deleted: [{ Key: 'test-key-1' }],
        Errors: [{ Key: 'test-key-2', Message: 'Access denied' }],
      };
      mockS3Client.send.mockResolvedValueOnce(mockResponse as any);

      // Act
      const result = await service.deleteFiles(keys);

      // Assert
      expect(DeleteObjectsCommand).toHaveBeenCalled();
      expect(mockS3Client.send).toHaveBeenCalled();
      expect(result.deleted).toEqual(['test-key-1']);
      expect(result.errors).toEqual([{ key: 'test-key-2', message: 'Access denied' }]);
    });

    it('should return empty arrays when no keys are provided', async () => {
      // Act
      const result = await service.deleteFiles([]);

      // Assert
      expect(mockS3Client.send).not.toHaveBeenCalled();
      expect(result.deleted).toEqual([]);
      expect(result.errors).toEqual([]);
    });

    it('should throw an exception when there is an error', async () => {
      // Arrange
      const keys = ['test-key-1', 'test-key-2'];
      const errorMessage = 'Test error';
      mockS3Client.send.mockRejectedValueOnce(new Error(errorMessage) as never);

      // Act & Assert
      await expect(service.deleteFiles(keys))
        .rejects
        .toThrow(AppException);
    });
  });

  describe('getDownloadUrl', () => {
    it('should return a download URL', () => {
      // Arrange
      const key = 'test-key';
      const expectedUrl = `${mockStorageConfig.cdn.url}/${key}`;

      // Act
      const result = service.getDownloadUrl(key);

      // Assert
      expect(result).toBe(expectedUrl);
    });

    it('should normalize the key by removing leading slash', () => {
      // Arrange
      const key = '/test-key';
      const normalizedKey = 'test-key';
      const expectedUrl = `${mockStorageConfig.cdn.url}/${normalizedKey}`;

      // Act
      const result = service.getDownloadUrl(key);

      // Assert
      expect(result).toBe(expectedUrl);
    });

    it('should return empty string for invalid key', () => {
      // Act
      const result = service.getDownloadUrl('');

      // Assert
      expect(result).toBe('');
    });
  });

  describe('getDownloadUrls', () => {
    it('should return download URLs for multiple keys', () => {
      // Arrange
      const keys = ['test-key-1', 'test-key-2'];
      const expectedUrls = [
        `${mockStorageConfig.cdn.url}/test-key-1`,
        `${mockStorageConfig.cdn.url}/test-key-2`,
      ];

      // Act
      const result = service.getDownloadUrls(keys);

      // Assert
      expect(result).toEqual(expectedUrls);
    });

    it('should return empty array for empty keys array', () => {
      // Act
      const result = service.getDownloadUrls([]);

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe('createPresignedDownloadUrl', () => {
    it('should create a presigned URL for download', async () => {
      // Arrange
      const key = 'test-key';
      const expirationTime = TimeIntervalEnum.FIFTEEN_MINUTES;

      // Act
      const result = await service.createPresignedDownloadUrl(key, expirationTime);

      // Assert
      expect(GetObjectCommand).toHaveBeenCalledWith({
        Bucket: mockStorageConfig.cloudflare.bucketName,
        Key: key,
      });
      expect(getSignedUrl).toHaveBeenCalled();
      expect(result).toBe('https://test-presigned-url.com');
    });

    it('should throw an exception when there is an error', async () => {
      // Arrange
      const key = 'test-key';
      const expirationTime = TimeIntervalEnum.FIFTEEN_MINUTES;
      const errorMessage = 'Test error';

      (getSignedUrl as jest.Mock).mockRejectedValueOnce(new Error(errorMessage));

      // Act & Assert
      await expect(service.createPresignedDownloadUrl(key, expirationTime))
        .rejects
        .toThrow(AppException);
    });
  });

  describe('createPresignedDownloadUrls', () => {
    it('should create presigned URLs for multiple keys', async () => {
      // Arrange
      const keys = ['test-key-1', 'test-key-2'];
      const expirationTime = TimeIntervalEnum.FIFTEEN_MINUTES;
      const expectedUrls = {
        'test-key-1': 'https://test-presigned-url.com',
        'test-key-2': 'https://test-presigned-url.com',
      };

      // Act
      const result = await service.createPresignedDownloadUrls(keys, expirationTime);

      // Assert
      expect(GetObjectCommand).toHaveBeenCalledTimes(2);
      expect(getSignedUrl).toHaveBeenCalledTimes(2);
      expect(result).toEqual(expectedUrls);
    });

    it('should return empty object for empty keys array', async () => {
      // Arrange
      const expirationTime = TimeIntervalEnum.FIFTEEN_MINUTES;

      // Act
      const result = await service.createPresignedDownloadUrls([], expirationTime);

      // Assert
      expect(result).toEqual({});
    });

    it('should handle exceptions for individual keys', async () => {
      // Arrange
      const keys = ['test-key-1', 'test-key-2'];
      const expirationTime = TimeIntervalEnum.FIFTEEN_MINUTES;
      const errorMessage = 'Test error';

      // Mock first call to succeed, second to fail
      (getSignedUrl as jest.Mock)
        .mockResolvedValueOnce('https://test-presigned-url.com')
        .mockRejectedValueOnce(new Error(errorMessage));

      // Act
      const result = await service.createPresignedDownloadUrls(keys, expirationTime);

      // Assert
      expect(GetObjectCommand).toHaveBeenCalledTimes(2);
      expect(getSignedUrl).toHaveBeenCalledTimes(2);
      expect(result).toEqual({ 'test-key-1': 'https://test-presigned-url.com' });
    });
  });

  describe('copyFile', () => {
    it('should copy a file successfully', async () => {
      // Arrange
      const sourceKey = 'source-key';
      const destinationKey = 'destination-key';
      const mockResponse = {
        CopyObjectResult: {
          ETag: '"test-etag"',
          LastModified: new Date(),
        },
      };
      mockS3Client.send.mockResolvedValueOnce(mockResponse as any);

      // Act
      const result = await service.copyFile(sourceKey, destinationKey);

      // Assert
      expect(CopyObjectCommand).toHaveBeenCalledWith({
        Bucket: mockStorageConfig.cloudflare.bucketName,
        Key: destinationKey,
        CopySource: `${mockStorageConfig.cloudflare.bucketName}/${sourceKey}`,
      });
      expect(mockS3Client.send).toHaveBeenCalled();
      expect(result.etag).toBe('test-etag');
      expect(result.lastModified).toBeInstanceOf(Date);
    });

    it('should copy a file to a different bucket', async () => {
      // Arrange
      const sourceKey = 'source-key';
      const destinationKey = 'destination-key';
      const destinationBucket = 'destination-bucket';
      const mockResponse = {
        CopyObjectResult: {
          ETag: '"test-etag"',
          LastModified: new Date(),
        },
      };
      mockS3Client.send.mockResolvedValueOnce(mockResponse as any);

      // Act
      const result = await service.copyFile(sourceKey, destinationKey, destinationBucket);

      // Assert
      expect(CopyObjectCommand).toHaveBeenCalledWith({
        Bucket: destinationBucket,
        Key: destinationKey,
        CopySource: `${mockStorageConfig.cloudflare.bucketName}/${sourceKey}`,
      });
      expect(mockS3Client.send).toHaveBeenCalled();
      expect(result.etag).toBe('test-etag');
      expect(result.lastModified).toBeInstanceOf(Date);
    });

    it('should throw an exception when there is an error', async () => {
      // Arrange
      const sourceKey = 'source-key';
      const destinationKey = 'destination-key';
      const errorMessage = 'Test error';
      mockS3Client.send.mockRejectedValueOnce(new Error(errorMessage) as never);

      // Act & Assert
      await expect(service.copyFile(sourceKey, destinationKey))
        .rejects
        .toThrow(AppException);
    });
  });

  describe('copyFiles', () => {
    it('should copy multiple files successfully', async () => {
      // Arrange
      const copyOperations = [
        { sourceKey: 'source-key-1', destinationKey: 'destination-key-1' },
        { sourceKey: 'source-key-2', destinationKey: 'destination-key-2' },
      ];
      const mockResponse = {
        CopyObjectResult: {
          ETag: '"test-etag"',
          LastModified: new Date(),
        },
      };
      mockS3Client.send.mockResolvedValue(mockResponse as any);

      // Act
      const result = await service.copyFiles(copyOperations);

      // Assert
      expect(CopyObjectCommand).toHaveBeenCalledTimes(2);
      expect(mockS3Client.send).toHaveBeenCalledTimes(2);
      expect(result.copied).toHaveLength(2);
      expect(result.errors).toHaveLength(0);
      expect(result.copied[0].sourceKey).toBe('source-key-1');
      expect(result.copied[0].destinationKey).toBe('destination-key-1');
      expect(result.copied[0].etag).toBe('test-etag');
    });

    it('should handle exceptions for individual files', async () => {
      // Arrange
      const copyOperations = [
        { sourceKey: 'source-key-1', destinationKey: 'destination-key-1' },
        { sourceKey: 'source-key-2', destinationKey: 'destination-key-2' },
      ];
      const mockResponse = {
        CopyObjectResult: {
          ETag: '"test-etag"',
          LastModified: new Date(),
        },
      };
      const errorMessage = 'Test error';

      // Mock first call to succeed, second to fail
      mockS3Client.send
        .mockResolvedValueOnce(mockResponse as any)
        .mockRejectedValueOnce(new Error(errorMessage) as never);

      // Act
      const result = await service.copyFiles(copyOperations);

      // Assert
      expect(CopyObjectCommand).toHaveBeenCalledTimes(2);
      expect(mockS3Client.send).toHaveBeenCalledTimes(2);
      expect(result.copied).toHaveLength(1);
      expect(result.errors).toHaveLength(1);
      expect(result.copied[0].sourceKey).toBe('source-key-1');
      expect(result.errors[0].sourceKey).toBe('source-key-2');
      expect(result.errors[0].message).toBe(errorMessage);
    });

    it('should return empty arrays when no operations are provided', async () => {
      // Act
      const result = await service.copyFiles([]);

      // Assert
      expect(mockS3Client.send).not.toHaveBeenCalled();
      expect(result.copied).toEqual([]);
      expect(result.errors).toEqual([]);
    });
  });
});
