# Zalo Integration Services

Module này cung cấp các service để tích hợp với Zalo API, cho phép tương tác với Zalo Official Account (OA), gửi tin nhắn thông báo qua Zalo Notification Service (ZNS), và xử lý webhook từ Zalo.

## Tổng quan

Zalo là một nền tảng nhắn tin phổ biến tại Việt Nam với hơn 100 triệu người dùng. Zalo cung cấp nhiều API cho phép doanh nghiệp tương tác với khách hàng thông qua:

1. **Zalo Official Account (OA)**: T<PERSON><PERSON> kho<PERSON>n ch<PERSON>h thức của doanh nghiệp trên <PERSON>, cho phép gửi tin nhắn, tương tác với người dùng.
2. **Zalo Notification Service (ZNS)**: Dịch vụ gửi thông báo đến người dùng <PERSON>alo thông qua số điện thoại, ngay cả khi họ chưa theo dõi Official Account.
3. **Zalo Social API**: API cho phép tích hợp đăng nhập bằng Zalo và truy cập thông tin người dùng.

## Cài đặt

### Cấu hình môi trường

Thêm các biến môi trường sau vào file `.env`:

```env
# Zalo API
ZALO_APP_ID=your_app_id
ZALO_APP_SECRET=your_app_secret
ZALO_WEBHOOK_SECRET=your_webhook_secret
ZALO_WEBHOOK_URL=https://your-domain.com/api/zalo/webhook
```

### Đăng ký module

Module đã được đăng ký trong `ServicesModule`, nên bạn có thể sử dụng các service của nó trong bất kỳ module nào đã import `ServicesModule`.

## Các service

### ZaloService

Service cơ bản cung cấp các phương thức để tương tác với Zalo API.

```typescript
// Lấy access token cho Official Account
const accessToken = await zaloService.getOaAccessToken(
  appId,
  appSecret,
  code,
  redirectUri
);

// Tạo URL xác thực cho Official Account
const authUrl = zaloService.createOaAuthUrl(appId, redirectUri);
```

### ZaloOaService

Service cung cấp các phương thức để tương tác với Zalo Official Account API.

```typescript
// Lấy thông tin Official Account
const oaInfo = await zaloOaService.getOaInfo(accessToken);

// Gửi tin nhắn văn bản
const result = await zaloOaService.sendTextMessage(
  accessToken,
  userId,
  'Xin chào từ RedAI!'
);

// Thiết lập webhook
await zaloOaService.setWebhook(accessToken, 'https://your-domain.com/api/zalo/webhook');
```

### ZaloZnsService

Service cung cấp các phương thức để tương tác với Zalo Notification Service (ZNS).

```typescript
// Gửi tin nhắn ZNS
const result = await zaloZnsService.sendZnsMessage(accessToken, {
  phone: '**********',
  template_id: 'your_template_id',
  template_data: {
    customer_name: 'Nguyễn Văn A',
    order_id: 'ORD123456',
    amount: '100,000 VND'
  }
});

// Lấy danh sách template ZNS
const templates = await zaloZnsService.getZnsTemplates(accessToken);
```

### ZaloWebhookService

Service cung cấp các phương thức để xử lý webhook từ Zalo.

```typescript
// Xác thực webhook
const isValid = zaloWebhookService.verifyWebhook(timestamp, mac, body);

// Xử lý sự kiện webhook
await zaloWebhookService.processWebhookEvent(event);
```

### ZaloAgentService

Service cung cấp các phương thức để tích hợp Zalo với Agent trong hệ thống.

```typescript
// Gửi tin nhắn từ agent đến người dùng Zalo
const result = await zaloAgentService.sendAgentMessageToZalo(
  accessToken,
  userId,
  'Xin chào, tôi là trợ lý ảo của bạn!'
);

// Gửi tin nhắn phức tạp từ agent đến người dùng Zalo
const result = await zaloAgentService.sendAgentComplexMessageToZalo(
  accessToken,
  userId,
  {
    type: 'image',
    url: 'https://example.com/image.jpg',
    caption: 'Hình ảnh từ agent'
  }
);

// Xử lý tin nhắn từ người dùng Zalo và chuyển tiếp đến agent
await zaloAgentService.processZaloMessageToAgent(
  accessToken,
  userId,
  'Tôi cần hỗ trợ',
  agentId
);
```

## Các loại tin nhắn

### Tin nhắn văn bản

```typescript
const message: ZaloTextMessage = {
  type: 'text',
  text: 'Xin chào từ RedAI!'
};
await zaloOaService.sendMessage(accessToken, userId, message);
```

### Tin nhắn hình ảnh

```typescript
const message: ZaloImageMessage = {
  type: 'image',
  url: 'https://example.com/image.jpg',
  caption: 'Mô tả hình ảnh'
};
await zaloOaService.sendMessage(accessToken, userId, message);
```

### Tin nhắn tệp đính kèm

```typescript
const message: ZaloFileMessage = {
  type: 'file',
  url: 'https://example.com/document.pdf',
  name: 'Tài liệu.pdf'
};
await zaloOaService.sendMessage(accessToken, userId, message);
```

### Tin nhắn template

```typescript
const message: ZaloTemplateMessage = {
  type: 'template',
  template_id: 'your_template_id',
  template_data: {
    title: 'Tiêu đề',
    subtitle: 'Mô tả',
    image_url: 'https://example.com/image.jpg',
    buttons: [
      {
        title: 'Xem chi tiết',
        url: 'https://example.com/details'
      }
    ]
  }
};
await zaloOaService.sendMessage(accessToken, userId, message);
```

## Xử lý webhook

Để xử lý webhook từ Zalo, bạn cần tạo một controller để nhận các sự kiện webhook:

```typescript
@Controller('api/zalo/webhook')
export class ZaloWebhookController {
  constructor(private readonly zaloWebhookService: ZaloWebhookService) {}

  @Post()
  async handleWebhook(
    @Headers('X-ZEvent-Signature') mac: string,
    @Headers('X-ZEvent-Timestamp') timestamp: string,
    @Body() body: any,
    @Req() req: Request
  ) {
    // Xác thực webhook
    const rawBody = req.rawBody.toString();
    const isValid = this.zaloWebhookService.verifyWebhook(timestamp, mac, rawBody);

    if (!isValid) {
      throw new UnauthorizedException('Invalid webhook signature');
    }

    // Xử lý sự kiện webhook
    return this.zaloWebhookService.processWebhookEvent(body);
  }
}
```

## Tích hợp với Agent

Để tích hợp Zalo với Agent trong hệ thống, bạn cần:

1. Tạo một service để xử lý việc chuyển tiếp tin nhắn giữa Zalo và Agent
2. Sử dụng `ZaloAgentService` để gửi tin nhắn từ Agent đến người dùng Zalo
3. Xử lý webhook từ Zalo và chuyển tiếp tin nhắn đến Agent

Ví dụ về cách tích hợp:

```typescript
// Trong controller hoặc service của bạn
@Injectable()
export class YourService {
  constructor(
    private readonly zaloAgentService: ZaloAgentService,
    // Các dependency khác
  ) {}

  async handleMessageFromZalo(oaId: string, userId: string, message: string): Promise<void> {
    // Tìm agent được kết nối với Official Account
    const agent = await this.findAgentByOaId(oaId);

    if (!agent) {
      return;
    }

    // Chuyển tiếp tin nhắn đến agent
    const agentResponse = await this.processMessageWithAgent(agent.id, message);

    // Gửi phản hồi từ agent đến người dùng Zalo
    await this.zaloAgentService.sendAgentMessageToZalo(
      agent.accessToken,
      userId,
      agentResponse
    );
  }
}
```

## Ví dụ

Thư mục `examples` chứa các ví dụ về cách sử dụng Zalo Integration Services trong ứng dụng:

- `zalo-controller.example.ts`: Ví dụ về controller để xử lý các request liên quan đến Zalo
- `zalo-entity.example.ts`: Ví dụ về các entity để lưu trữ dữ liệu Zalo
- `zalo-repository.example.ts`: Ví dụ về các repository để tương tác với database
- `zalo-service.example.ts`: Ví dụ về service để xử lý logic nghiệp vụ

## Tài liệu tham khảo

- [Zalo Official Account API](https://developers.zalo.me/docs/api/official-account-api/api/api-tong-quan-post-4219)
- [Zalo Notification Service (ZNS)](https://developers.zalo.me/docs/api/zalo-notification-service/api/api-tong-quan-post-4219)
- [Zalo Social API](https://developers.zalo.me/docs/api/social-api/api/api-tong-quan-post-4219)
- [Zalo Developers](https://developers.zalo.me/)
- [Zalo Business API](https://business.zalo.me/)
