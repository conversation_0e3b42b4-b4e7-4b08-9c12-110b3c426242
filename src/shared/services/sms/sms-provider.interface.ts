/**
 * Interface định nghĩa các phương thức chung cho tất cả các nhà cung cấp SMS
 */
export interface ISmsProvider {
  /**
   * Tên nhà cung cấp SMS
   */
  readonly providerName: string;

  /**
   * G<PERSON><PERSON> tin nhắn SMS đến một số điện thoại
   * @param phoneNumber Số điện thoại của người nhận
   * @param message Nội dung tin nhắn
   * @param options Các tùy chọn bổ sung dành riêng cho nhà cung cấp
   * @returns Promise chứa ID tin nhắn hoặc lỗi
   */
  sendSms(phoneNumber: string, message: string, options?: any): Promise<SmsResponse>;

  /**
   * G<PERSON><PERSON> tin nhắn SMS đến nhiều số điện thoại
   * @param phoneNumbers Danh sách số điện thoại của người nhận
   * @param message Nội dung tin nhắn
   * @param options Các tùy chọn bổ sung dành riêng cho nhà cung cấp
   * @returns Promise chứa kết quả cho từng người nhận
   */
  sendBulkSms(phoneNumbers: string[], message: string, options?: any): Promise<BulkSmsResponse>;

  /**
   * Kiểm tra trạng thái của tin nhắn đã gửi
   * @param messageId ID của tin nhắn cần kiểm tra
   * @returns Promise chứa trạng thái của tin nhắn
   */
  checkMessageStatus(messageId: string): Promise<MessageStatusResponse>;

  /**
   * Gửi tin nhắn SMS với brandname
   * @param phoneNumber Số điện thoại của người nhận
   * @param message Nội dung tin nhắn
   * @param brandname Tên thương hiệu sử dụng làm người gửi
   * @param options Các tùy chọn bổ sung dành riêng cho nhà cung cấp
   * @returns Promise chứa ID tin nhắn hoặc lỗi
   */
  sendBrandnameSms(phoneNumber: string, message: string, brandname: string, options?: any): Promise<SmsResponse>;

  /**
   * Gửi tin nhắn SMS OTP (One-Time Password)
   * @param phoneNumber Số điện thoại của người nhận
   * @param otpCode Mã OTP cần gửi
   * @param options Các tùy chọn bổ sung dành riêng cho nhà cung cấp
   * @returns Promise chứa ID tin nhắn hoặc lỗi
   */
  sendOtp(phoneNumber: string, otpCode: string, options?: any): Promise<SmsResponse>;

  /**
   * Kiểm tra kết nối với nhà cung cấp SMS
   * @param config Cấu hình của nhà cung cấp
   * @returns Promise chỉ ra liệu kết nối có thành công hay không
   */
  testConnection(config: any): Promise<ConnectionTestResponse>;
}

/**
 * Interface cho phản hồi khi gửi SMS
 */
export interface SmsResponse {
  /**
   * Chỉ ra liệu việc gửi tin nhắn có thành công hay không
   */
  success: boolean;

  /**
   * ID của tin nhắn (nếu thành công)
   */
  messageId?: string;

  /**
   * Mã lỗi (nếu thất bại)
   */
  errorCode?: string;

  /**
   * Thông báo lỗi (nếu thất bại)
   */
  errorMessage?: string;

  /**
   * Dữ liệu thô của phản hồi từ nhà cung cấp
   */
  rawResponse?: any;
}

/**
 * Interface cho phản hồi khi gửi SMS hàng loạt
 */
export interface BulkSmsResponse {
  /**
   * Tổng số tin nhắn đã gửi thành công
   */
  successCount: number;

  /**
   * Tổng số tin nhắn gửi thất bại
   */
  failureCount: number;

  /**
   * Kết quả chi tiết cho từng người nhận
   */
  results: {
    /**
     * Số điện thoại của người nhận
     */
    phoneNumber: string;

    /**
     * Chỉ ra liệu việc gửi tin nhắn có thành công cho người nhận này hay không
     */
    success: boolean;

    /**
     * ID của tin nhắn (nếu thành công)
     */
    messageId?: string;

    /**
     * Mã lỗi (nếu thất bại)
     */
    errorCode?: string;

    /**
     * Thông báo lỗi (nếu thất bại)
     */
    errorMessage?: string;
  }[];

  /**
   * ID của giao dịch hoặc chiến dịch
   */
  transactionId?: string;

  /**
   * Tổng chi phí gửi tin nhắn
   */
  totalCost?: number;

  /**
   * Dữ liệu thô của phản hồi từ nhà cung cấp
   *
   * Données brutes de la réponse du fournisseur
   */
  rawResponse?: any;
}

/**
 * Interface cho phản hồi trạng thái tin nhắn
 *
 * Interface pour la réponse de statut de message
 */
export interface MessageStatusResponse {
  /**
   * ID của tin nhắn
   *
   * ID du message
   */
  messageId: string;

  /**
   * Trạng thái của tin nhắn
   *
   * Statut du message
   */
  status: MessageStatus;

  /**
   * Thời gian cập nhật trạng thái gần nhất
   *
   * Horodatage de la dernière mise à jour du statut
   */
  updatedAt: Date;

  /**
   * Thông tin chi tiết bổ sung về trạng thái
   *
   * Détails supplémentaires sur le statut
   */
  details?: string;

  /**
   * Dữ liệu thô của phản hồi từ nhà cung cấp
   *
   * Données brutes de la réponse du fournisseur
   */
  rawResponse?: any;
}

/**
 * Enum các trạng thái có thể có của tin nhắn
 *
 * Énumération des statuts possibles pour un message
 */
export enum MessageStatus {
  /**
   * Tin nhắn đang chờ gửi
   *
   * Message en attente d'envoi
   */
  PENDING = 'PENDING',

  /**
   * Tin nhắn đang trong quá trình gửi
   *
   * Message en cours d'envoi
   */
  SENDING = 'SENDING',

  /**
   * Tin nhắn đã được gửi đến người nhận
   *
   * Message délivré au destinataire
   */
  DELIVERED = 'DELIVERED',

  /**
   * Tin nhắn không được gửi đến người nhận
   *
   * Message non délivré au destinataire
   */
  FAILED = 'FAILED',

  /**
   * Tin nhắn đã hết hạn (không được gửi trong khoảng thời gian quy định)
   *
   * Message expiré (non délivré dans le délai imparti)
   */
  EXPIRED = 'EXPIRED',

  /**
   * Tin nhắn bị từ chối bởi nhà cung cấp
   *
   * Message rejeté par le fournisseur
   */
  REJECTED = 'REJECTED',

  /**
   * Trạng thái không xác định
   *
   * Statut inconnu
   */
  UNKNOWN = 'UNKNOWN'
}

/**
 * Interface cho phản hồi kiểm tra kết nối
 *
 * Interface pour la réponse de test de connexion
 */
export interface ConnectionTestResponse {
  /**
   * Chỉ ra liệu kết nối có thành công hay không
   *
   * Indique si la connexion est réussie
   */
  success: boolean;

  /**
   * Thông báo mô tả kết quả của việc kiểm tra
   *
   * Message décrivant le résultat du test
   */
  message: string;

  /**
   * Thông tin chi tiết bổ sung (trong trường hợp thất bại)
   *
   * Détails supplémentaires (en cas d'échec)
   */
  details?: any;
}
