# SePay Hub Documentation

## Model Schema

Tổng hợp các model có sử dụng trong tài liệu.

### Model Ngân hàng

| Thu<PERSON><PERSON> tính | <PERSON><PERSON> tả | Kiểu dữ liệu |
|------------|-------|--------------|
| id | ID ngân hàng | string |
| brand_name | Tên ngân hàng | string |
| full_name | Tên đầy đủ ngân hàng | string |
| short_name | Tên ngắn gọn ngân hàng | string |
| code | Mã ngân hàng | string |
| bin | Bin ngân hàng | string |
| logo_path | URL Logo ngân hàng | string |
| icon | URL Icon ngân hàng | string |
| active | Trạng thái ngân hàng được hỗ trợ bởi SePay | "1" \| "0" |

### Model Bộ đếm giao dịch

| Thu<PERSON><PERSON> tính | <PERSON><PERSON> tả | Kiểu dữ liệu |
|------------|-------|--------------|
| company_id | ID công ty (tổ chức) | string |
| date | Ngày phát sinh bộ đếm | Y-m-d |
| transaction | Tổng số lượng giao dịch trong ngày | string |
| transaction_in | Tổng số lượng giao dịch tiền vào trong ngày | string |
| transaction_out | Tổng số lượng giao dịch tiền ra trong ngày | string |

### Model Công ty (tổ chức)

| Thuộc tính | Mô tả | Kiểu dữ liệu |
|------------|-------|--------------|
| id | ID công ty (tổ chức) | string |
| full_name | Tên đầy đủ | string |
| short_name | Tên viết tắt | string |
| status | Trạng thái công ty (tổ chức) | Pending, Active, Suspended, Terminated, Cancelled, Fraud |
| created_at | Ngày tạo | Y-m-d H:i:s |
| updated_at | Lần cập nhật cuối | Y-m-d H:i:s |

### Model Cấu hình công ty (tổ chức)

| Thuộc tính | Mô tả | Kiểu dữ liệu |
|------------|-------|--------------|
| payment_code | Cấu hình nhận diện mã thanh toán | "on" \| "off" |
| payment_code_prefix | Cấu hình tiền tố mã thanh toán | string |
| payment_code_suffix_from | Cấu hình độ dài tối thiểu hậu tố mã thanh toán | number |
| payment_code_suffix_to | Cấu hình độ dài tối đa hậu tố mã thanh toán | number |
| payment_code_suffix_character_type | Cấu hình kiểu ký tự hậu tố mã thanh toán | "NumberAndLetter", "NumberOnly" |
| transaction_amount | Cấu hình số lượng giao dịch | number \| "Unlimited" |

> **Lưu ý:** 
> - NumberAndLetter: Cho phép chữ cái và số
> - NumberOnly: Chỉ cho phép chữ số
> - Số lượng giao dịch có thể là số nguyên dương không âm hoặc giá trị chuỗi "Unlimited" (không giới hạn)

### Model tài khoản ngân hàng

| Thuộc tính | Mô tả | Kiểu dữ liệu |
|------------|-------|--------------|
| id | ID tài khoản ngân hàng | string |
| company_id | ID công ty (tổ chức) sở hữu tài khoản ngân hàng | string |
| bank_id | ID ngân hàng | string |
| account_holder_name | Tên chủ tài khoản | string |
| account_number | Số tài khoản | string |
| accumulated | Số dư | string |
| label | Tên gợi nhớ | string |
| bank_api_connected | Trạng thái đã liên kết API ngân hàng hay chưa | "1" \| "0" |
| last_transaction | Thời gian phát sinh giao dịch lần cuối | Y-m-d H:i:s |
| created_at | Ngày tạo | Y-m-d H:i:s |
| updated_at | Lần cập nhật cuối | Y-m-d H:i:s |

### Model VA

| Thuộc tính | Mô tả | Kiểu dữ liệu |
|------------|-------|--------------|
| id | ID tài khoản ngân hàng | string |
| company_id | ID công ty (tổ chức) sở hữu tài khoản ngân hàng | string |
| bank_account_id | ID tài khoản ngân hàng sở hữu VA | string |
| va | Số VA | string |
| label | Tên gợi nhớ | string |
| active | Trạng thái hoạt động | "1" \| "0" |
| created_at | Ngày tạo | Y-m-d H:i:s |
| updated_at | Lần cập nhật cuối | Y-m-d H:i:s |

### Model lịch sử giao dịch

| Thuộc tính | Mô tả | Kiểu dữ liệu |
|------------|-------|--------------|
| id (deprecated) | ID giao dịch | string |
| transaction_id | ID giao dịch | string |
| transaction_date | Thời gian nhận giao dịch | Y-m-d H:i:s |
| bank_account_id | ID tài khoản ngân hàng sở hữu giao dịch | string |
| account_number | Số tài khoản ngân hàng | string |
| company_id | ID công ty (tổ chức) sở hữu tài khoản ngân hàng | string |
| bank_id | ID ngân hàng | string |
| va_id | ID VA (nếu có) | string |
| va | Số VA | string |
| reference_number | Mã tham chiếu FT | string |
| transaction_content | Nội dung giao dịch | string |
| payment_code | Mã thanh toán (nếu có) | string |
| transfer_type | Loại giao dịch (credit: tiền vào, debit: tiền ra) | "credit" \| "debit" |
| amount | Số tiền giao dịch | string |
