import { MarketingAiOptions, MarketingAiResponse, BaseMarketingAiService } from './base.interface';

/**
 * Content type options
 */
export enum ContentType {
  BLOG_POST = 'blog_post',
  SOCIAL_MEDIA = 'social_media',
  EMAIL = 'email',
  AD_COPY = 'ad_copy',
  PRODUCT_DESCRIPTION = 'product_description',
  HEADLINE = 'headline',
  LANDING_PAGE = 'landing_page',
  PRESS_RELEASE = 'press_release',
  SEO_CONTENT = 'seo_content',
  CUSTOM = 'custom',
}

/**
 * Content tone options
 */
export enum ContentTone {
  PROFESSIONAL = 'professional',
  CASUAL = 'casual',
  FRIENDLY = 'friendly',
  FORMAL = 'formal',
  PERSUASIVE = 'persuasive',
  INFORMATIVE = 'informative',
  ENTHUSIASTIC = 'enthusiastic',
  HUMOROUS = 'humorous',
}

/**
 * Content language options
 */
export enum ContentLanguage {
  ENGLISH = 'en',
  VIETNAMESE = 'vi',
  FRENCH = 'fr',
  GERMAN = 'de',
  SPANISH = 'es',
  ITALIAN = 'it',
  PORTUGUESE = 'pt',
  RUSSIAN = 'ru',
  JAPANESE = 'ja',
  KOREAN = 'ko',
  CHINESE = 'zh',
}

/**
 * Content generation options
 */
export interface ContentGenerationOptions extends MarketingAiOptions {
  /**
   * Type of content to generate
   * @default ContentType.BLOG_POST
   */
  contentType?: ContentType;

  /**
   * Tone of the generated content
   * @default ContentTone.PROFESSIONAL
   */
  tone?: ContentTone;

  /**
   * Language of the generated content
   * @default ContentLanguage.ENGLISH
   */
  language?: ContentLanguage;

  /**
   * Maximum length of the generated content in characters
   */
  maxLength?: number;

  /**
   * Keywords to include in the generated content
   */
  keywords?: string[];

  /**
   * Target audience for the generated content
   */
  audience?: string;

  /**
   * Temperature for content generation (0.0 to 1.0)
   * Lower values make output more focused and deterministic
   * Higher values make output more creative and diverse
   * @default 0.7
   */
  temperature?: number;
}

/**
 * Content generation result
 */
export interface ContentGenerationResult {
  /**
   * Generated content
   */
  content: string;

  /**
   * Title or headline for the content (if applicable)
   */
  title?: string;

  /**
   * Meta description for the content (if applicable)
   */
  metaDescription?: string;

  /**
   * Keywords used in the content
   */
  keywords?: string[];

  /**
   * Content type that was generated
   */
  contentType: ContentType;
}

/**
 * Interface for content generation services
 */
export interface ContentGenerationService extends BaseMarketingAiService {
  /**
   * Generate content from a text prompt
   * @param prompt Text prompt to generate content from
   * @param options Options for content generation
   * @returns A promise that resolves to a response containing the generated content
   */
  generateContent(
    prompt: string,
    options?: ContentGenerationOptions,
  ): Promise<MarketingAiResponse<ContentGenerationResult>>;

  /**
   * Edit existing content using a text prompt
   * @param content Existing content to edit
   * @param prompt Text prompt to guide the editing
   * @param options Options for content editing
   * @returns A promise that resolves to a response containing the edited content
   */
  editContent?(
    content: string,
    prompt: string,
    options?: ContentGenerationOptions,
  ): Promise<MarketingAiResponse<ContentGenerationResult>>;

  /**
   * Generate content variations from existing content
   * @param content Existing content to create variations from
   * @param options Options for content variation generation
   * @returns A promise that resolves to a response containing the content variations
   */
  generateContentVariations?(
    content: string,
    options?: ContentGenerationOptions,
  ): Promise<MarketingAiResponse<ContentGenerationResult>>;
}
