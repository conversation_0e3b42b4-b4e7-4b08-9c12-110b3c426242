import { AppException, ErrorCode } from '@common/exceptions/app.exception';

/**
 * Enum định nghĩa các loại MIME cho file marketplace
 */
export enum MarketplaceMediaTypeEnum {
  HTML = 'text/html',
  JPEG = 'image/jpeg',
  PNG = 'image/png',
  GIF = 'image/gif',
  WEBP = 'image/webp',
}

/**
 * Object tiện ích để làm việc với MarketplaceMediaTypeEnum
 */
export const MarketplaceMediaType = {
  /**
   * Lấy MIME type từ tên hoặc giá trị MIME type
   * @param type Tên loại file (key của enum) hoặc giá trị MIME type
   * @returns Giá trị MIME hợp lệ
   * @throws AppException nếu loại file không tồn tại
   */
  getMimeType(type: string): MarketplaceMediaTypeEnum {
    // Kiểm tra nếu là key của enum (ví dụ: 'HTML')
    const mimeTypeFromKey = MarketplaceMediaTypeEnum[type as keyof typeof MarketplaceMediaTypeEnum];
    if (mimeTypeFromKey) {
      return mimeTypeFromKey;
    }

    // Kiểm tra nếu là value của enum (ví dụ: 'text/html')
    const entries = Object.entries(MarketplaceMediaTypeEnum);
    const entry = entries.find(([_, value]) => value === type);

    if (entry) {
      return MarketplaceMediaTypeEnum[entry[0] as keyof typeof MarketplaceMediaTypeEnum];
    }

    // Nếu không tìm thấy, ném lỗi
    throw new AppException(
      ErrorCode.FILE_TYPE_NOT_FOUND,
      `Loại tệp '${type}' không được hỗ trợ`
    );
  },
};
