---
description: 
globs: 
alwaysApply: true
---
# ✅ CONTROLLER RULES

## 🎯 Objective
Responsible for routing and handling HTTP requests/responses. Should not contain business logic. Converts input/output between HTTP ↔ DTO.

## 📁 Location
Each module has 1 controller file, located in `/controllers`

## 🧱 Organization Conventions
- File naming:
  - Admin: `admin-xxx.controller.ts`
  - User: `user-xxx.controller.ts`
- Class: PascalCase + Controller (example: `AgentBaseAdminController`)
- Method naming: verb + object (example: `findAll`, `create`, `update`)
- Endpoint:
  - Admin: `@Controller('admin/xxx')`
  - User: `@Controller('user/xxx')`

## 💡 Coding Rules
- ❌ No business logic in controllers
- ✅ Use DTOs for input/output
- ✅ Use correct HTTP decorators (`@Get`, `@Post`, `@Patch`, `@Delete`, etc.)
- ✅ Use complete Swagger documentation:
  - `@ApiTags(SWAGGER_API_TAGS.XXX)` with `import { SWAGGER_API_TAGS } from '@/common/swagger/swagger.tags'`
  - `@ApiOperation({ summary: 'Brief description' })`
  - `@ApiResponse({ ... })` with appropriate schema:
    - Single item: `schema: ApiResponseDto.getSchema(XxxResponseDto)`
    - Paginated: `schema: ApiResponseDto.getPaginatedSchema(XxxResponseDto)`
    - No data: `schema: ApiResponseDto.getSchema(null)`
  - `@ApiExtraModels(ApiResponseDto, XxxResponseDto, PaginatedResult)`
  - `@ApiParam()` for path parameters
  - `@ApiQuery()` for query parameters
- ✅ Never return raw entities
- ✅ Guards are mandatory:
  - Admin: `@UseGuards(JwtEmployeeGuard)` with `import { JwtEmployeeGuard } from '@modules/auth/guards'`
  - User: `@UseGuards(JwtUserGuard)` with `import { JwtUserGuard } from '@/modules/auth/guards'`
- ✅ When using Guards, add `@ApiBearerAuth('JWT-auth')`
- ✅ Use `ApiResponseDto` to standardize responses:
  - Single item: `ApiResponseDto.success(result)`
  - Newly created: `ApiResponseDto.created(result)`
  - Paginated: `ApiResponseDto.paginated(result)`
- ❌ Never use `ApiResponseDto<any>` - always specify concrete types:
  - Correct: `ApiResponseDto<UserDto>`, `ApiResponseDto<PaginatedResult<ProductDto>>`, `ApiResponseDto<null>`
  - Incorrect: `ApiResponseDto<any>`, `ApiResponseDto<unknown>`
- ✅ Use decorators to get current user information:
  - Admin: `@CurrentEmployee() employee: JWTPayload`
  - User: `@CurrentUser() user: JWTPayload`
- ✅ Use Promise<DtoResponse> for all controller methods
- ✅ Error handling:
  - ❌ Don't throw exceptions directly in controllers
  - ✅ Let services handle and throw specific exceptions using `AppException`
  - ✅ Use custom `@ApiErrorResponse()` decorator to document errors in Swagger:
    ```typescript
    @ApiErrorResponse(
      MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
      MARKETPLACE_ERROR_CODES.CART_UPDATE_FAILED,
      MARKETPLACE_ERROR_CODES.MISSING_REQUIRED_FIELDS
    )
    ```
  - ✅ Include `ApiErrorResponseDto` in `@ApiExtraModels`
  - ✅ Use module-specific error codes (e.g., `MARKETPLACE_ERROR_CODES`)
  - ✅ Register global exception filters in the application module

## 📝 Standard Controller Structure
```typescript
import { Controller, Get, Post, Body, Param, Patch, Delete, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { XxxService } from '../services/xxx.service';
import { CreateXxxDto, UpdateXxxDto, QueryXxxDto, XxxResponseDto } from '../dto/xxx';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { JWTPayload } from '@modules/auth/interfaces';
import { ApiErrorResponse } from '@common/error/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/error/api-error-response.dto';
import { XXX_ERROR_CODES } from '../common/xxx-error-codes';

/**
 * Controller handling APIs related to XXX management for admin
 */
@ApiTags(SWAGGER_API_TAGS.XXX)
@ApiExtraModels(ApiResponseDto, XxxResponseDto, PaginatedResult, ApiErrorResponseDto)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/xxx')
export class AdminXxxController {
  constructor(private readonly xxxService: XxxService) {}

  /**
   * Get paginated list of XXX
   */
  @Get()
  @ApiOperation({ summary: 'Get list of XXX' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  @ApiResponse({
    status: 200,
    description: 'List of XXX',
    schema: ApiResponseDto.getPaginatedSchema(XxxResponseDto)
  })
  @ApiErrorResponse(
    XXX_ERROR_CODES.DATA_FETCH_ERROR
  )
  async findAll(@Query() queryDto: QueryXxxDto): Promise<ApiResponseDto<PaginatedResult<XxxResponseDto>>> {
    const result = await this.xxxService.findAll(queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Get XXX details by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get XXX details' })
  @ApiParam({ name: 'id', description: 'ID of XXX' })
  @ApiResponse({
    status: 200,
    description: 'XXX details',
    schema: ApiResponseDto.getSchema(XxxResponseDto)
  })
  @ApiErrorResponse(
    XXX_ERROR_CODES.NOT_FOUND,
    XXX_ERROR_CODES.DATA_FETCH_ERROR
  )
  async findOne(@Param('id') id: string): Promise<ApiResponseDto<XxxResponseDto>> {
    const result = await this.xxxService.findOne(id);
    return ApiResponseDto.success(result);
  }

  /**
   * Create new XXX
   */
  @Post()
  @ApiOperation({ summary: 'Create new XXX' })
  @ApiResponse({
    status: 201,
    description: 'XXX has been created',
    schema: ApiResponseDto.getSchema(XxxResponseDto)
  })
  @ApiErrorResponse(
    XXX_ERROR_CODES.CREATION_FAILED,
    XXX_ERROR_CODES.MISSING_REQUIRED_FIELDS,
    XXX_ERROR_CODES.INVALID_DATA
  )
  async create(
    @Body() createXxxDto: CreateXxxDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<XxxResponseDto>> {
    const result = await this.xxxService.create(createXxxDto, employee.id);
    return ApiResponseDto.created(result);
  }

  /**
   * Update XXX information
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Update XXX' })
  @ApiParam({ name: 'id', description: 'ID of XXX' })
  @ApiResponse({
    status: 200,
    description: 'XXX has been updated',
    schema: ApiResponseDto.getSchema(XxxResponseDto)
  })
  @ApiErrorResponse(
    XXX_ERROR_CODES.NOT_FOUND,
    XXX_ERROR_CODES.UPDATE_FAILED,
    XXX_ERROR_CODES.INVALID_DATA,
    XXX_ERROR_CODES.INVALID_STATUS
  )
  async update(
    @Param('id') id: string,
    @Body() updateXxxDto: UpdateXxxDto,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<XxxResponseDto>> {
    const result = await this.xxxService.update(id, updateXxxDto, employee.id);
    return ApiResponseDto.success(result);
  }

  /**
   * Delete XXX
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete XXX' })
  @ApiParam({ name: 'id', description: 'ID of XXX' })
  @ApiResponse({
    status: 200,
    description: 'XXX has been deleted',
    schema: ApiResponseDto.getSchema(null)
  })
  @ApiErrorResponse(
    XXX_ERROR_CODES.NOT_FOUND,
    XXX_ERROR_CODES.DELETE_FAILED
  )
  async remove(
    @Param('id') id: string,
    @CurrentEmployee() employee: JWTPayload
  ): Promise<ApiResponseDto<null>> {
    await this.xxxService.remove(id, employee.id);
    return ApiResponseDto.success(null, 'XXX has been successfully deleted');
  }
}
```

## ✅ Checklist
- [ ] No business logic in controller
- [ ] Clear DTOs for input/output
- [ ] Complete Swagger documentation (ApiTags, ApiOperation, ApiResponse, ApiExtraModels)
- [ ] Correct decorators (Controller, HTTP methods, Guards)
- [ ] Proper error handling with ApiErrorResponse decorator
- [ ] Correct Guards usage (JwtEmployeeGuard/JwtUserGuard)
- [ ] File naming and endpoint conventions (admin/user)
- [ ] Using ApiResponseDto for standardized responses
- [ ] No ApiResponseDto<any> - always specify concrete types
- [ ] Using decorators to get current user information
- [ ] Using Promise<DtoResponse> for all controller methods

- [ ] Including ApiErrorResponseDto in ApiExtraModels