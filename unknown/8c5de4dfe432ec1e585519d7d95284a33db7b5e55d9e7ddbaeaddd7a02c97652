import { Injectable } from '@nestjs/common';
import { UserTemplateEmailRepository } from '../repositories/user-template-email.repository';
import { UserTemplateEmail } from '../entities/user-template-email.entity';
import { CreateTemplateEmailDto, TemplateEmailQueryDto, UpdateTemplateEmailDto } from '../dto/template-email';
import { PaginatedResult } from '@/common/response';

/**
 * Service xử lý logic liên quan đến template email
 */
@Injectable()
export class UserTemplateEmailService {
  constructor(
    private readonly userTemplateEmailRepository: UserTemplateEmailRepository,
  ) {}

  /**
   * Lấy danh sách template email với phân trang và filter
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách template email với phân trang
   */
  async findAll(userId: number, queryDto: TemplateEmailQueryDto): Promise<PaginatedResult<UserTemplateEmail>> {
    return this.userTemplateEmailRepository.findWithPagination(userId, queryDto);
  }

  /**
   * Lấy chi tiết template email
   * @param id ID của template
   * @param userId ID của người dùng
   * @returns Chi tiết template email
   */
  async findById(id: number, userId: number): Promise<UserTemplateEmail> {
    return this.userTemplateEmailRepository.findById(id, userId);
  }

  /**
   * Tạo mới template email
   * @param userId ID của người dùng
   * @param createDto Dữ liệu tạo mới
   * @returns Template email đã tạo
   */
  async create(userId: number, createDto: CreateTemplateEmailDto): Promise<UserTemplateEmail> {
    const now = Date.now();

    const templateData: Partial<UserTemplateEmail> = {
      userId,
      name: createDto.name,
      subject: createDto.subject,
      content: createDto.content,
      tags: createDto.tags || [],
      placeholders: createDto.placeholders || [],
      createdAt: now,
      updatedAt: now,
    };

    return this.userTemplateEmailRepository.create(templateData);
  }

  /**
   * Cập nhật template email
   * @param id ID của template
   * @param userId ID của người dùng
   * @param updateDto Dữ liệu cập nhật
   * @returns Template email đã cập nhật
   */
  async update(id: number, userId: number, updateDto: UpdateTemplateEmailDto): Promise<UserTemplateEmail> {
    const updateData: Partial<UserTemplateEmail> = {
      ...updateDto,
      updatedAt: Date.now(),
    };

    return this.userTemplateEmailRepository.update(id, userId, updateData);
  }

  /**
   * Xóa template email
   * @param id ID của template
   * @param userId ID của người dùng
   * @returns true nếu xóa thành công
   */
  async delete(id: number, userId: number): Promise<boolean> {
    return this.userTemplateEmailRepository.delete(id, userId);
  }
}
