Stack trace:
Frame         Function      Args
0007FFFF9DC0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8CC0) msys-2.0.dll+0x1FE8E
0007FFFF9DC0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA098) msys-2.0.dll+0x67F9
0007FFFF9DC0  000210046832 (000210286019, 0007FFFF9C78, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DC0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9DC0  000210068E24 (0007FFFF9DD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA0A0  00021006A225 (0007FFFF9DD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF1E5E0000 ntdll.dll
7FFF1D930000 KERNEL32.DLL
7FFF1BBB0000 KERNELBASE.dll
7FFF1C3A0000 USER32.dll
7FFF1C0C0000 win32u.dll
7FFF1DB90000 GDI32.dll
7FFF1BF80000 gdi32full.dll
7FFF1B770000 msvcp_win.dll
7FFF1C190000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFF1CE80000 advapi32.dll
7FFF1C570000 msvcrt.dll
7FFF1E4D0000 sechost.dll
7FFF1C7B0000 RPCRT4.dll
7FFF1AE80000 CRYPTBASE.DLL
7FFF1C0F0000 bcryptPrimitives.dll
7FFF1DD60000 IMM32.DLL
