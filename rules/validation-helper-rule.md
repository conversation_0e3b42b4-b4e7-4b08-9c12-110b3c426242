Validation Helper Rules
Purpose

Define rules for creating and using ValidationHelper in each module to handle business data validation.
Ensure every module includes a validation.helper.ts file in its helpers/ directory to manage validation logic specific to that module.
Promote consistency, reusability, and testability for validation functions.
Align with principles from helper-rule.md (pure functions, no framework dependencies) and integrate with other layers such as service and exception.

Location

The ValidationHelper file must be located in the module's helpers/ directory: [module-name]/helpers/.
File name: validation.helper.ts.
Examples:src/modules/user/helpers/validation.helper.ts
src/modules/marketplace/helpers/validation.helper.ts



Organization Rules
1. Class Structure

ValidationHelper must be a class marked with @Injectable() to support Dependency Injection in NestJS.
Class name: ValidationHelper.
Basic structure:import { Injectable } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { [ModuleName]_ERROR_CODES } from '../exceptions/[module-name].exception';

@Injectable()
export class ValidationHelper {
// Validation methods
}



2. Responsibilities of ValidationHelper

Business Data Validation: Validate conditions such as entity status, ownership, required fields, or module-specific constraints.
Throw Exceptions: Use AppException with error codes from [ModuleName]_ERROR_CODES when validation fails.
No Complex Business Logic: Only perform checks and throw errors; do not modify data or interact with repositories.
Reusability: Design validation methods to be reusable across multiple Service methods.

3. Coding Rules

Pure Functions: Methods must have no side effects and not alter external state.
No Framework Dependencies: Do not import components like Repository, Service, or framework-specific utilities.
Use AppException: Every validation failure must throw an AppException with a specific error code from [module-name].exception.ts.
Detailed Comments in English: Each method must include clear documentation describing its purpose, parameters, and possible thrown errors.
Clear Method Names: Use the validate prefix followed by a description of the validation, e.g., validateUserNotDeleted, validateProductIsDraft.
Use Map for Error Messages: Store status-based error messages in a Map or object for maintainability and scalability.private readonly errorMessageStatus = {
[UserStatus.ACTIVE]: 'User is in active status',
[UserStatus.INACTIVE]: 'User has not been activated',
};


Null/Undefined Checks: Always validate input parameters (entities or DTOs) to prevent runtime errors.
Code Optimization: Avoid duplicating validation logic; reuse common methods where possible.

4. Integration with Service

ValidationHelper must be injected into the Service via the constructor:constructor(
private readonly userRepository: UserRepository,
private readonly validationHelper: ValidationHelper,
) {}


Service methods must call ValidationHelper methods before executing business logic:this.validationHelper.validateUserNotDeleted(user);


Do not use ValidationHelper in Controller or Repository.

5. Error Handling

Each validation method must throw an AppException with a specific error code from [ModuleName]_ERROR_CODES.
Do not throw strings, generic Error objects, or framework-specific exceptions (e.g., BadRequestException).
Error messages must be clear and provide detailed information about the failure:throw new AppException(
USER_ERROR_CODES.USER_NOT_FOUND,
'User does not exist',
);



6. Naming Conventions

Method Names: Start with validate, followed by the object and condition, e.g.:
validateUserNotDeleted
validateUserIsInactive
validateUserForCreation


Variable Names: Use camelCase, descriptive names, e.g., errorMessageStatus.

7. Example Implementation
   Below is an example for the user module:
   import { Injectable } from '@nestjs/common';
   import { User } from '../entities/user.entity';
   import { UserStatus } from '../constants/user-status.enum';
   import { AppException } from '@common/exceptions';
   import { USER_ERROR_CODES } from '../exceptions/user.exception';

@Injectable()
export class ValidationHelper {
private readonly errorMessageStatus = {
[UserStatus.ACTIVE]: 'User is in active status',
[UserStatus.INACTIVE]: 'User has not been activated',
[UserStatus.SUSPENDED]: 'User has been suspended',
};

/**
* Validates that the user exists and has not been deleted
* @param user The user to validate
* @throws AppException if the user does not exist or has been deleted
  */
  validateUserNotDeleted(user: User | null): void {
  if (!user) {
  throw new AppException(
  USER_ERROR_CODES.USER_NOT_FOUND,
  'User does not exist',
  );
  }

    if (user.deletedAt) {
      throw new AppException(
        USER_ERROR_CODES.USER_ALREADY_DELETED,
        'User has been deleted',
      );
    }
}

/**
* Validates required fields for user creation
* @param userData Data for creating a user
* @throws AppException if required fields are missing
  */
  validateUserForCreation(userData: { email: string; password: string; name: string }): void {
  const requiredFields: string[] = [];

    if (!userData.email) requiredFields.push('email');
    if (!userData.password) requiredFields.push('password');
    if (!userData.name) requiredFields.push('name');

    if (requiredFields.length > 0) {
      throw new AppException(
        USER_ERROR_CODES.MISSING_REQUIRED_FIELDS,
        `User is missing required fields: ${requiredFields.join(', ')}`,
      );
    }
}
}

8. Checklist

File is located at [module-name]/helpers/validation.helper.ts.
Class is marked with @Injectable().
Contains only validation logic, no business logic.
Uses AppException with error codes from [module-name].exception.ts.
Each method has detailed English comments.
Method names start with validate and are descriptive.
Uses Map or object for status-based error messages.
Does not import Repository, Service, or framework-specific components.
Validates null/undefined for all input parameters.
Injected and used in Service, not in Controller or Repository.

9. Integration with Other Rules

Helper Rule: ValidationHelper adheres to helper-rule.md (pure functions, no framework dependencies).
Service Rule: Used in Service to validate data before executing business logic.
Exception Rule: Integrates with [module-name].exception.ts to throw errors with specific error codes.
Controller Rule: Not used directly in Controller, only through Service.

10. Development Process

When creating a new module:
Create validation.helper.ts in [module-name]/helpers/.
Define validation methods based on business requirements.
Ensure each method throws AppException with appropriate error codes.
Inject ValidationHelper into Service and use in relevant methods.


When updating a module:
Add new validation methods to ValidationHelper for new requirements.
Update [module-name].exception.ts if new error codes are needed.
Ensure new methods follow the rules (comments, naming, etc.).



