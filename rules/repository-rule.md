# ✅ REPOSITORY RULES

## 🎯 Objective
The only layer that interacts with the database. Contains no business logic.

## 📁 Location
In the `repositories/xxx.repository.ts` directory

## 🔄 General Principles
- ✅ Repository is the only layer allowed to interact directly with the database
- ✅ Each entity has only one corresponding repository
- ✅ Repositories should not call other repositories directly, must go through services

## 📄 File Structure
- ✅ Follow standard NestJS file structure: `entities.repository.ts`
- ✅ Configure repository class as follows:
```typescript
@Injectable()
export class AgentSystemRepository extends Repository<AgentSystem> {
  private readonly logger = new Logger(AgentSystemRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentSystem, dataSource.createEntityManager());
  }

  // Các phương thức repository
}
```

## 💡 Coding Rules
- ✅ Only perform DB operations (ORM/query builder)
- ✅ No business logic or error throwing
- ✅ No formatting, mapping, or calculations
- ✅ No dependencies on higher layers (service/controller)
- ✅ Clear method naming
- ✅ Must have a basic query builder for the repository that other methods can use, for example:
```typescript
/**
 * Tạo query builder cơ bản cho agent system
 * @returns SelectQueryBuilder<AgentSystem> để sử dụng trong các phương thức khác
 */
private createBaseQuery(): SelectQueryBuilder<AgentSystem> {
  return this.createQueryBuilder('agentSystem');
}
```
- ✅ Only execute queries and return results, don't throw exceptions
- ✅ Execute queries using SelectQueryBuilder with the following structure:
```typescript
async findAgentSystemById(id: string): Promise<AgentSystem | null> {
  this.logger.log(`Tìm agent system với ID: ${id}`);

  // Tạo query builder với join đến bảng agents
  const qb = this.createBaseQuery()
    .leftJoinAndSelect(
      'agent',
      'agent',
      'agent.id = agentSystem.id'
    )
    .where('agentSystem.id = :id', { id })
    .andWhere('agent.deleted_at IS NULL');

  return qb.getOne();
}
```
- ✅ Only use SelectQueryBuilder, don't use raw SQL queries like:
```typescript
// ❌ KHÔNG ĐƯỢC SỬ DỤNG
this.dataSource.query(
  `DELETE FROM agent_roles_mapping WHERE agent_id = $1 AND role_id = $2`,
  [agentId, roleId]
);
```
- ✅ All functions must have specific Promise return types, don't use any or unknown types
- ✅ Clear and detailed Vietnamese comments for each method

## 🔍 Error Handling
- ✅ Repositories should not throw exceptions, only return null or empty arrays when data is not found
- ✅ Log all important operations with this.logger
- ✅ Handle special cases (null, undefined) before executing queries

## 🔄 Performance Optimization
- ✅ Use indexes defined in the database
- ✅ Avoid N+1 queries by using leftJoinAndSelect when needed
- ✅ Only select necessary columns instead of select * when querying large amounts of data
- ✅ Use pagination when querying many records

## ✅ Checklist
- [ ] No business logic
- [ ] Don't return raw entities unless necessary
- [ ] No table-wide updates without conditions
- [ ] Created createBaseQuery() for reuse
- [ ] All methods have specific return types (no any/unknown)
- [ ] All methods have complete Vietnamese comments
- [ ] Only use QueryBuilder, not raw SQL
- [ ] Proper logging for important operations
- [ ] Correct handling of null/undefined cases
- [ ] Optimized queries to avoid performance issues