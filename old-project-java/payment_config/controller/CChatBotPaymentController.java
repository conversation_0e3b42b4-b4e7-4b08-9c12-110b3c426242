package com.redon_agency.chatbot.user.payment_config.controller;

import com.redon_agency.chatbot.dto.response.ApiResponse;
import com.redon_agency.chatbot.user.payment_config.dto.response.CChatBotPaymentRes;
import com.redon_agency.chatbot.user.payment_config.dto.CChatBotTypePayment;
import com.redon_agency.chatbot.user.payment_config.service.CChatBotPaymentService;
import com.redon_agency.chatbot.utils.enum_utils.PaymentChatbotEnum;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/c_chatbot_payment")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CChatBotPaymentController {
    CChatBotPaymentService cChatBotPaymentService;

    @Operation(summary = "Danh sách chatbot",
            description = "Danh sách chatbot")
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/list-payment-chatbot")
    public ApiResponse<List<CChatBotPaymentRes>> listChatBotPayment() {

        return ApiResponse.<List<CChatBotPaymentRes>>builder()
                .code(200)
                .result(cChatBotPaymentService.listChatBotPayment())
                .build();
    }

    @Operation(summary = "Cài đặt loại payment cho chatbot",
            description = "Cài đặt loại payment cho chatbot")
    @PreAuthorize("isAuthenticated()")
    @PostMapping("/setting-type-payment")
    public ApiResponse<Void> settingTypePayment(
            @RequestParam(value = "chatBotId") Integer chatBotId,
            @RequestParam(value = "paymentMethod") PaymentChatbotEnum paymentMethod
    ) {
        cChatBotPaymentService.settingTypePayment(chatBotId, paymentMethod);
        return ApiResponse.<Void>builder()
                .code(200)
                .message("Cài đặt thành công!")
                .build();
    }

    @Operation(summary = "Thông tin loại payment của chatbot",
            description = "Thông tin loại payment của chatbot")
    @PreAuthorize("isAuthenticated()")
    @GetMapping("/view-type-payment")
    public ApiResponse<CChatBotTypePayment> viewChatBotTypePayment(
            @RequestParam(value = "chatBotId") Integer chatBotId
    ) {
        return ApiResponse.<CChatBotTypePayment>builder()
                .code(200)
                .result(cChatBotPaymentService.viewChatBotTypePayment(chatBotId))
                .build();
    }

    @Operation(summary = "Cài đặt chatbot với payment",
            description = "Cài đặt chatbot với payment")
    @PreAuthorize("isAuthenticated()")
    @PostMapping("/setting-payment-chatbot")
    public ApiResponse<Void> chatBotOnlineBankingSettingPayment(
            @RequestParam(value = "chatBotId") Integer chatBotId,
            @RequestParam(value = "id") Integer id
    ) {
        cChatBotPaymentService.chatBotOnlineBankingSettingPayment(chatBotId, id);
        return ApiResponse.<Void>builder()
                .code(200)
                .message("Liên kêt thành công!")
                .build();
    }
}
