package com.redon_agency.chatbot.user.payment_config.mapper;

import com.redon_agency.chatbot.common.utils.ConvertTupleSaveUtils;
import com.redon_agency.chatbot.user.payment_config.dto.response.CChatBotPaymentRes;
import com.redon_agency.chatbot.utils.CDNUtils;
import com.redon_agency.chatbot.utils.enum_utils.PaymentChatbotEnum;
import com.redon_agency.chatbot.utils.presigned_url.TimeLiveUtils;
import jakarta.persistence.Tuple;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ChatBotPaymentGetwayMapper {
    CDNUtils cdnUtils;

    public List<CChatBotPaymentRes> toListCChatBotPaymentRes(List<Tuple> request) {
        return request.stream().map(this::toCChatBotPaymentRes).toList();
    }

    public CChatBotPaymentRes toCChatBotPaymentRes(Tuple request) {
        Integer id = ConvertTupleSaveUtils.getSafeInteger(request, "id");
        String type = ConvertTupleSaveUtils.getSafeString(request, "type");
        String bankCode = ConvertTupleSaveUtils.getSafeString(request, "bankCode");
        String accountNumber = ConvertTupleSaveUtils.getSafeString(request, "accountNumber");
        String accountName = ConvertTupleSaveUtils.getSafeString(request, "accountName");
        String iconPath = ConvertTupleSaveUtils.getSafeString(request, "iconPath");
        Boolean isVA = ConvertTupleSaveUtils.getSafeBoolean(request, "isVA");
        Integer chatBotId = ConvertTupleSaveUtils.getSafeInteger(request, "chatBotId");
        String chatBotName = ConvertTupleSaveUtils.getSafeString(request, "chatBotName");
        String chatBotIcon = ConvertTupleSaveUtils.getSafeString(request, "chatBotIcon");
        String position = ConvertTupleSaveUtils.getSafeString(request, "position");

        PaymentChatbotEnum enumType = null;
        if (type != null && !type.isEmpty()) {
            try {
                enumType = PaymentChatbotEnum.valueOf(type);
            } catch (IllegalArgumentException ignored) {
            }
        }

        return CChatBotPaymentRes.builder()
                .id(id)
                .type(enumType)
                .bankCode(bankCode)
                .accountNumber(accountNumber)
                .accountName(accountName)
                .iconPath(iconPath)
                .isVA(isVA)
                .chatBotId(chatBotId)
                .chatBotName(chatBotName)
                .chatBotIcon(cdnUtils.generateUrlView(chatBotIcon, TimeLiveUtils.IMAGE))
                .position(position)
                .isLinked(id != null)
                .build();
    }
}
